package com.unimas.pg.dopatch;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.FileUploadBase.FileSizeLimitExceededException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.fileupload.servlet.ServletRequestContext;
import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;
import org.codehaus.jackson.type.TypeReference;

import com.unimas.pg.common.CMDHandler;
import com.unimas.pg.common.Command;
import com.unimas.pg.common.ConfUtil;
import com.unimas.pg.common.EncryAndDecryUtil;
import com.unimas.pg.common.FileUtil;
import com.unimas.pg.common.JSONUtils;
import com.unimas.pg.common.RuntimeCMDHandler;
import com.unimas.pg.common.ZipUtil;

public class PatchLogic {
	protected static final Logger log = Logger.getLogger(PatchLogic.class);
	private static String uploadFolder;// 补丁压缩包上传目录
	// private static String verboseOutFile;//打印输出文件
	private static String zipOutFolder;// 补丁压缩包解压目录
	private static String readmesFolder;// 集中存放readme文件的目录
	// private static String patchFile;//补丁脚本文件
	private static String patchHistoryInfoFile;// 修复历史记录文件
	private static int fileSize;// 补丁文件上传限制大小
	private static PatchLogic logic = null;
	private static Pattern pattern = Pattern
			.compile("[A-Za-z0-9\\-_\\s()（）\\.]+"); // \u4e00-\u9fa5

	private PatchLogic() {
		// InputStream in =
		// getClass().getResourceAsStream("MOD-INF/patch.properties");
		// Properties prop = new Properties();
		// try {
		// prop.load(in);
		// } catch (IOException e) {
		// e.printStackTrace();//
		// }
		// ConfUtil util = new ConfUtil();
		ConfUtil util = ConfUtil.getInstance();
		patchHistoryInfoFile = util.getPatchHistoryInfoFile();
		readmesFolder = util.getPatchReadmeDir();// 集中存放readme文件的目录
		uploadFolder = util.getPatchUploadDir();// 上传后文件存放目录
		zipOutFolder = util.getPatchZipOutDir();// 解压后的目录
		fileSize = Integer.parseInt(util.getPatchFileSize());
		// uploadFolder = (String)prop.get("uploadFolder");//上传后文件存放目录
		// zipOutFolder = (String)prop.get("zipOutFolder");//解压后的目录
		// readmesFolder = (String)prop.get("readmesFolder");//集中存放readme文件的目录
		// patchHistoryInfoFile = (String)prop.get("patchHistoryInfoFile");
		// fileSize = Integer.parseInt((String) prop.get("fileSize")) ;
	}

	public static synchronized PatchLogic getInstance() {
		if (logic == null) {
			logic = new PatchLogic();
		}
		return logic;
	}

	/**
	 * 上传补丁包，解压
	 *
	 * @param request
	 * @return
	 * @throws UIException
	 */
	public synchronized Map<String, String> importfile(
			HttpServletRequest request) throws Exception {
		Map<String, String> map = null;
		String fileName = null;
		try {
			DiskFileItemFactory factory = new DiskFileItemFactory();
			factory.setSizeThreshold(1024 * 1024 * fileSize); // 设置保存到内存中的大小
			ServletFileUpload upload = new ServletFileUpload(factory);
			upload.setFileSizeMax(1024L * 1024L * 1024L);// 设置单个上传文件最大容量值为1GB
			// upload.setSizeMax(fileMaxSize*1024*1024);//设置多个上传文件最大容量值
			List<?> items = upload.parseRequest(new ServletRequestContext(
					request));// 解析请求里的上传文件单元
			fileName = new SimpleDateFormat("yyyyMMddHHmmss")
					.format(new Date()) + ".zip";// 以当前时间命名文件
			File _outFolder = new File(zipOutFolder);// 解压缩临时目标目录
			if (_outFolder.exists()) {
				cleanUp();
			}
			String currtime = String.valueOf(System.currentTimeMillis());
			if (items != null && items.size() > 0) {
				Iterator<?> it = items.iterator();
				if (it.hasNext()) { // 仅有一个文件上传
					FileItem item = (FileItem) it.next();
					fileName = item.getName();// 获取上传的文件名
					if (fileName.indexOf("\\") > -1) {
						int num = fileName.lastIndexOf("\\") + 1;
						fileName = fileName.substring(num);
						// if(!pattern.matcher(fileName).matches()){
						// log.info("上传文件名只能包含英文、数字、空格、" +
						// "小括号、下划线(_)、中短线(-)及点号(.)。");
						// throw new Exception("上传文件名只能包含英文、数字、空格、" +
						// "小括号、下划线(_)、中短线(-)及点号(.)。");
						// }
					}

					if (!pattern.matcher(fileName).matches()) {
						log.info("上传文件名只能包含英文、数字、空格、"
								+ "小括号、下划线(_)、中短线(-)及点号(.)。");
						throw new Exception("上传文件名只能包含英文、数字、空格、"
								+ "小括号、下划线(_)、中短线(-)及点号(.)。");
					}
					File uploadfile = new File(uploadFolder + fileName);
					String unzip_path = zipOutFolder + currtime
							+ File.separator;
					String decrypedzip_path = unzip_path + fileName;
					try {
						if (!item.isFormField()) {
							// if(!ZipUtil.isZipFile(item.getInputStream())){
							// log.info("文件格式不对，其必须是zip格式！");
							// throw new Exception("文件格式不对，其必须是zip格式！");
							// }else {
							// 判断目录是否为空，若不为空则删除
							// commandResult("rm -rf "+uploadFolder,"","");

							ensureFoldersAreExists(currtime);// 检查硬盘上的目录是否都已经创建好
							InputStream input = null;
							// InputStream decryinput = null;
							try {
								item.write(uploadfile);// 上传文件
								input = new FileInputStream(uploadfile);
								if (!ZipUtil.isZipFile(input)) {
									log.info("文件格式不对，其必须是zip格式！");
									throw new Exception("文件格式不对，其必须是zip格式！");
								}
								// 第一步正常解压出密钥和加密的压缩包
								ZipUtil.unzip(uploadFolder + fileName,
										unzip_path, false, "");
								File zipfile = new File(unzip_path + "encry_"
										+ fileName);
								// 解密
								decryFile(zipfile, unzip_path + "AESKey",
										decrypedzip_path);
								// File decryfile = new File(decrypedzip_path);
								// decryinput = new FileInputStream(decryfile);
								ZipUtil.unzip(decrypedzip_path, unzip_path,
										false, "");

								// File uploadfile = new File(uploadFolder+
								// fileName);
								// 删除上传临时文件
								// File upfile = new File(uploadFolder+
								// fileName);
								// if(upfile.exists()){
								// upfile.delete();
								// }

								map = new HashMap<String, String>();
								map.put("filename", fileName);
								map.put("currtime", currtime);

								File f = new File(unzip_path + "readme.txt");
								StringBuffer sb = new StringBuffer();
								if (f.exists()) {
									InputStream fi = null;
									try {
										fi = new FileInputStream(f);
										List<?> readLines = IOUtils
												.readLines(fi, "utf-8");
										int index = readLines.indexOf("[适用范围]");
										for (int i = index; i < readLines
												.size(); i++) {
											sb.append(readLines.get(i)).append(
													"\n");
										}
									} catch (IOException e) {
										e.printStackTrace();
									} finally{
										if(fi!=null){
											fi.close();
										}
									}
								}
								map.put("readme", sb.toString());
								// map.put("readme", new
								// FileUtil().readFileByLines(zipOutFolder+currtime+File.separator+"readme.txt"));
								RuntimeCMDHandler cmdHdl = new RuntimeCMDHandler();
								cmdHdl.exec("chmod 777 " + unzip_path + "*",
										false, null);// 赋权限;//赋权限

							} catch (Exception e) {
								log.info(e.getMessage(), e);
								throw new Exception("文件上传发生错误");
							} finally{
								if(input!=null){
									input.close();
								}
							}/*
							 * finally{ if(decryinput!=null){ try {
							 * decryinput.close(); } catch (IOException e) { } }
							 * }
							 */
						}
						// }
					} catch (IOException e) {
						log.info(e.getMessage(), e);
						throw new Exception("文件上传发生错误");
					}
				}
				return map;
			} else {
				log.info("未能从表单项中获取到文件");
				throw new Exception("未能从表单项中获取到文件");// items为空的情况
			}
		} catch (FileSizeLimitExceededException e) {
			cleanUp();
			log.info(e.getMessage(), e);
			throw new Exception("文件上传发生错误或上传文件超出最大限制" + fileSize + "M", e);
		} catch (FileUploadException e) {
			cleanUp();
			log.info(e.getMessage(), e);
			throw new Exception("上传发生错误", e);
		} catch (Exception e) {
			cleanUp();
			log.info(e.getMessage(), e);
			throw new Exception(e.getMessage(), e);
		} finally {
			File upfile = new File(uploadFolder + fileName);
			if (upfile.exists()) {
				upfile.delete();
			}
		}
	}

	public void decryFile(File file, String keypath, String outFilePath)
			throws Exception {
		BufferedOutputStream stream = null;
		FileOutputStream fstream = null;
		String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
		InputStream in = null;
		try {
			byte[] key = new EncryAndDecryUtil().getAESKey(keypath);
			in = new FileInputStream(file);
			Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
			cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"));
			// 优化缓冲区大小，提高大文件处理性能
			int blockSize = cipher.getBlockSize() * 4000; // 从1000增加到4000，提高处理速度
			int outputSize = cipher.getOutputSize(blockSize);

			// 创建输出流，使用更大的缓冲区
			fstream = new FileOutputStream(outFilePath);
			stream = new BufferedOutputStream(fstream, 65536); // 64KB缓冲区

			byte[] inBytes = new byte[blockSize];
			byte[] outBytes = new byte[outputSize];
			int inLength = 0;
			long totalProcessed = 0;
			long fileSize = file.length();
			long lastLogTime = System.currentTimeMillis();

			log.info("开始解密数据，文件大小: " + fileSize + " bytes");
			System.out.println("开始解密数据，文件大小: " + fileSize + " bytes");

			while ((inLength = in.read(inBytes)) != -1) {
				totalProcessed += inLength;

				if (inLength == blockSize) {
					// 完整块处理
					int outLength = cipher.update(inBytes, 0, blockSize, outBytes);
					stream.write(outBytes, 0, outLength);
				} else {
					// 最后一个不完整的块
					byte[] finalBytes = cipher.doFinal(inBytes, 0, inLength);
					stream.write(finalBytes);
					break;
				}

				// 每10秒输出一次进度，避免日志过多影响性能
				long currentTime = System.currentTimeMillis();
				if (currentTime - lastLogTime > 10000) {
					double progress = (double) totalProcessed / fileSize * 100;
					log.info("解密进度: " + String.format("%.1f", progress) + "% (" + totalProcessed + " / " + fileSize + " bytes)");
					System.out.println("解密进度: " + String.format("%.1f", progress) + "% (" + totalProcessed + " / " + fileSize + " bytes)");
					lastLogTime = currentTime;
				}
			}

			// 如果没有处理任何数据，执行最终处理
			if (totalProcessed == 0) {
				byte[] finalBytes = cipher.doFinal();
				stream.write(finalBytes);
			}

			log.info("解密完成，输出文件: " + outFilePath);
			System.out.println("解密完成，输出文件: " + outFilePath);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new Exception("解密上传文件失败", e);
		} finally {
			if(in != null){
				in.close();
			}
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			if (fstream != null) {
				try {
					fstream.close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}

		}
	}

	public static String commandResult(String param_cmd, String mode,
			String charset) throws Exception {
		Command cmd = new Command();
		cmd.append("/bin/sh").append("-c");
		cmd.append(param_cmd);
		CMDHandler cmdhdl = new CMDHandler(cmd);
		if (!"".equals(charset)) {
			cmdhdl = cmdhdl.setReaderCharset(charset);
		}
		try {
			cmdhdl.exec();
		} catch (Exception e) {
			throw new Exception(e);
		}
		return cmdhdl.getOutput();
	}

	/**
	 * 检查并初始化目录，确保目录存在
	 */
	private void ensureFoldersAreExists(String currtime) {
		String[] folders = { uploadFolder,// 压缩包上传目录
				zipOutFolder,// 解压缩临时目录
				readmesFolder,// readme集中存放的目录
				zipOutFolder + currtime + File.separator };
		for (String folder : folders) {
			File _f = new File(folder);
			if (!_f.exists()) {
				_f.mkdirs();
			}
		}
	}

	public synchronized String execmd(String type, String currtime)
			throws Exception {
		String cmd = "";
		String result = "success";
		Process process = null;
		BufferedReader input = null;
		try {
			File file = new File(zipOutFolder + currtime + "/" + type + ".sh");
			if (!file.exists() && !"uninstall".equals(type)) {
				throw new Exception("补丁包不完整，请重新导入升级包升级！");
			}
			StringBuffer sb = new StringBuffer();
			sb.append("cd ").append(zipOutFolder).append(currtime).append("/;");
			if ("check".equals(type)) {
				// cmd = zipOutFolder+currtime+File.separator+"check.sh";
				sb.append("./check.sh");
			} else if ("uninstall".equals(type)) {
				// cmd = zipOutFolder+currtime+File.separator+"uninstall.sh";
				sb.append("./uninstall.sh");
			} else if ("restart".equals(type)) {
				// cmd = zipOutFolder+currtime+File.separator+"restart.sh";
				sb.append("./restart.sh");
			}
			// Process process = Runtime.getRuntime().exec(cmd);
			process = Runtime.getRuntime().exec(
					new String[] { "/bin/sh", "-c", sb.toString() });
			List<String> processList = new ArrayList<String>();
			input = new BufferedReader(new InputStreamReader(
					process.getInputStream()));
			String line = "";
			while ((line = input.readLine()) != null) {
				processList.add(line);
			}
			input.close();
			int m = processList.size();
			for (int i = 0; i < m; i++) {
				if (m - 1 == i) {
					String ss = processList.get(i);
					if (ss.contains("result:success")) {
						result = "success";
					} else if (ss.contains("result:failed-0")) {
						result = "failed-0";
					} else if (ss.contains("result:failed-1")) {
						result = "failed-1";
					} else if (ss.contains("result:failed")) {
						result = "failed";
						cleanUp();
					}
				}
			}
		} catch (Exception e) {
			log.info(e.getMessage(), e);
			result = "failed";
			throw e;
		} finally {
			if ("restart".equals(type)) {
				cleanUp();
			}
			if(input !=null){
				input.close();
			}
			if (process != null) {
				process.destroy();
			}
		}
		return result;
	}

	public synchronized String exepatch(String filename, String pguser,
			String currtime) throws Exception {
		String result = "failed";
		Process process = null;
		BufferedReader input = null;
		// 将readme文件重命名为{md5(补丁文件)}.readme，并放到指定目录
		// String signature_code = this.getFileMd5(uploadfile);
		try {
			File file = new File(zipOutFolder + currtime + "/install.sh");
			if (!file.exists()) {
				throw new Exception("升级失败！原因：补丁包不完整，请重新导入升级包升级！");
			}
			UUID uuid = UUID.randomUUID();
			String signature_code = uuid.toString();
			RuntimeCMDHandler _ch = new RuntimeCMDHandler();
			_ch.exec("mv " + zipOutFolder + currtime + File.separator
					+ "readme.txt " + readmesFolder + signature_code, false,
					null);
			_ch.close();
			/*
			 * cmd = zipOutFolder+currtime+File.separator+"install.sh"; Process
			 * process = Runtime.getRuntime().exec(cmd);
			 */
			StringBuffer sb = new StringBuffer();
			sb.append("cd ").append(zipOutFolder).append(currtime)
					.append("/; ./install.sh");
			process = Runtime.getRuntime().exec(
					new String[] { "/bin/sh", "-c", sb.toString() });
			List<String> processList = new ArrayList<String>();
			input = new BufferedReader(new InputStreamReader(
					process.getInputStream()));
			String line = "";
			while ((line = input.readLine()) != null) {
				processList.add(line);
			}
			input.close();
			int m = processList.size();
			Date date = new Date();
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String time = df.format(date);
			for (int i = 0; i < m; i++) {
				if (m - 1 == i) {
					String ss = processList.get(i);
					if (ss.contains("result:success")) {
						result = "success";
						// 写文件到patchhistory.json中
						this.writeHistoryFile(time, pguser, filename,
								signature_code);
					} else if (ss.contains("result:failed")) {
						result = "failed";
						cleanUp();
					} else {
						throw new Exception("升级失败！原因：升级脚本格式错误！");
					}
				}
			}
		} catch (Exception e) {
			log.info(e.getMessage(), e);
			result = "failed";
			cleanUp();
			throw e;
		} finally {
			if (input != null) {
				input.close();
			}
			if (process != null) {
				process.destroy();
			}
		}
		return result;
	}

	// 判断restart.sh中是否为空，如果为空，则不弹出重启的窗口
	public boolean is_restart(String currtime) {
		boolean result = false;
		File file = new File(zipOutFolder + currtime + File.separator
				+ "restart.sh");
		if (file.exists()) {
			FileReader fr = null;
			try {
				fr = new FileReader(zipOutFolder + currtime + File.separator
						+ "restart.sh");
				if (fr.read() > -1) {
					result = true;
				} else {
					cleanUp();
				}
			} catch (FileNotFoundException e) {
				e.printStackTrace();
				cleanUp();
			} catch (IOException e) {
				e.printStackTrace();
				cleanUp();
			} finally {
				try {
					fr.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		} else {
			cleanUp();
		}
		return result;
	}

	/**
	 * 写修复历史文件
	 *
	 * @param time
	 *            修复时间
	 * @param usr
	 *            操作账号
	 * @param patchZipFile
	 *            补丁文件名
	 * @param readmeFile
	 *            补丁readme文件
	 * @param signature_code
	 *            特征码
	 * @throws IOException
	 */
	private void writeHistoryFile(String time, String usr, String patchZipFile,
			String signature_code) throws IOException {
		File h_file = new File(patchHistoryInfoFile);
		String _output = "";
		if (h_file.exists()) {
			List<Map<String, String>> list = JSONUtils.getObjFromFile(h_file,
					new TypeReference<List<Map<String, String>>>() {
					});
			Map<String, String> map = new HashMap<String, String>();
			map.put("patch_name", patchZipFile);
			map.put("time", time);
			map.put("user", usr);
			map.put("uuid", signature_code);
			list.add(map);
			_output = JSONUtils.toJson(list);
		} else {
			_output = "[{\"patch_name\":\"" + patchZipFile + "\",\"time\":\""
					+ time + "\",\"user\":\"" + usr + "\",\"uuid\":\""
					+ signature_code + "\"}]";
		}
		FileWriter writer = new FileWriter(h_file, false);
		writer.write(_output);
		writer.flush();
	}

	/**
	 * 清理临时目录及文件 删除临时解压缩输出文件夹 verbose文件不做删除，留给打补丁功能初始时做判断删除
	 */
	private void cleanUp() {
		File _outFolder = new File(zipOutFolder);
		if (_outFolder.exists()) {
			// _outFolder.delete();
			RuntimeCMDHandler _ch = new RuntimeCMDHandler();
			try {
				// 尝试删除临时zip输出目录
				_ch.exec("rm -fr " + zipOutFolder, false, null);
			} catch (Exception e1) {
				// do nothing
			}
		}
	}

	public String geterror(String currtime) throws Exception {
		String str = "";
		try {

			File file = new File(zipOutFolder + currtime + File.separator
					+ "result.failed");
			if (file.exists()) {
				str = new FileUtil().readFileByLines(zipOutFolder + currtime
						+ File.separator + "result.failed", "gbk");
			}
		} catch (Exception e) {
			throw e;
		}
		return str;
	}

	public void deleteTemp() {
		cleanUp();
	}

	public static void main(String args[]) {
		String fileName = "X:\\Documents and Settings\\dev\\桌面\\pt5.020150422.zip";// 获取上传的文件名
		if (fileName.indexOf("\\") > -1) {
			int num = fileName.lastIndexOf("\\") + 1;
			System.out.println(num);
			String s = fileName.substring(num);
			System.out.println(s);
			if (!pattern.matcher(s).matches()) {
				System.out.println("上传文件名只能包含英文、数字、空格、"
						+ "小括号、下划线(_)、中短线(-)及点号(.)。");
			}
		}
	}
}
