package com.example.udp.service;

import com.example.udp.service.impl.LicenseServiceImpl;
import com.udpproxy.service.ServiceConfigurationService;
import com.udpproxy.model.UdpServiceList;
import com.udpproxy.model.UdpServiceList.UdpServiceInfo;
import com.udpproxy.util.ConfigXmlOperator;
import com.udpproxy.util.Constant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 授权到期监控服务
 * 定时检查授权状态，如果授权过期则停止所有运行中的服务
 */
@Component
@EnableScheduling
public class LicenseExpirationMonitor {

    private static final Logger logger = LoggerFactory.getLogger(LicenseExpirationMonitor.class);

    @Autowired
    private LicenseServiceImpl licenseService;

    @Autowired
    private ServiceConfigurationService serviceConfigurationService;

    private boolean wasLicenseValid = true;

    @PostConstruct
    public void init() {
        logger.info("授权到期监控服务已启动");
        // 初始化时检查一次授权状态
        checkLicenseAndStopServicesIfNeeded();
    }

    /**
     * 定时任务，每5分钟检查一次授权状态
     * 如果授权到期，则停止所有运行中的服务
     */
    @Scheduled(fixedRate = 300000) // 300000ms = 5分钟
    public void checkLicenseAndStopServicesIfNeeded() {
        try {
            logger.debug("执行授权状态检查...");
            boolean isLicenseValid = licenseService.isBaseLicenseValid();
            
            // 如果授权状态发生变化（从有效变为无效），记录日志
            if (wasLicenseValid && !isLicenseValid) {
                logger.warn("授权已到期，即将停止所有运行中的服务");
            } else if (!wasLicenseValid && isLicenseValid) {
                logger.info("授权已恢复有效状态");
            }
            
            // 更新上次授权状态
            wasLicenseValid = isLicenseValid;
            
            // 如果授权无效，停止所有运行中的服务
            if (!isLicenseValid) {
                stopAllRunningServices();
            }
        } catch (Exception e) {
            logger.error("检查授权状态时发生错误", e);
        }
    }

    /**
     * 停止所有运行中的服务
     */
    private void stopAllRunningServices() {
        try {
            logger.info("开始停止所有运行中的服务...");
            
            // 从配置文件获取服务列表
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
            UdpServiceList serviceList = configXmlOperator.parseServicesToUdpServiceList();
            
            List<UdpServiceInfo> runningServices = new ArrayList<>();
            
            // 找出所有运行中的服务
            for (UdpServiceInfo service : serviceList.getServices()) {
                if ("running".equals(service.getStatus())) {
                    runningServices.add(service);
                }
            }
            
            logger.info("发现{}个正在运行的服务需要停止", runningServices.size());
            
            // 停止每个运行中的服务
            for (UdpServiceInfo service : runningServices) {
                try {
                    logger.info("正在停止服务: ID={}, 名称={}", service.getId(), service.getName());
                    
                    // 调用stopSMService接口停止服务
                    boolean stopped = serviceConfigurationService.stopSMService(service.getId().toString());
                    
                    if (stopped) {
                        // 更新服务状态为stopped
                        service.setStatus("stopped");
                        configXmlOperator.updateNodeAttribute(
                            service.getId().toString(), 
                            "status", 
                            "stopped"
                        );
                        configXmlOperator.updateNodeAttribute(
                            service.getId().toString(), 
                            "isRun",
                            "false"
                        );
                        logger.info("服务停止成功: ID={}", service.getId());
                    } else {
                        logger.error("服务停止失败: ID={}", service.getId());
                    }
                } catch (Exception e) {
                    logger.error("停止服务时发生错误: ID=" + service.getId(), e);
                }
            }
            
            // 保存更新后的服务列表
            if (!runningServices.isEmpty()) {
                boolean saved = configXmlOperator.saveServiceList(serviceList);
                if (saved) {
                    logger.info("成功更新服务状态到配置文件");
                } else {
                    logger.error("更新服务状态到配置文件失败");
                }
                
                // 强制重新加载服务列表
                serviceConfigurationService.forceReloadServicesList();
            }
            
            logger.info("授权到期处理完成，已停止所有运行中的服务");
        } catch (Exception e) {
            logger.error("停止服务过程中发生错误", e);
        }
    }
} 