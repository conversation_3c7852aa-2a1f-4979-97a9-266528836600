public class TestNetmaskConversion {
    
    /**
     * 将前缀长度转换为子网掩码
     * 例如：24 -> 255.255.255.0, 8 -> 255.0.0.0
     */
    private static String prefixLengthToNetmask(int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            return "255.255.255.0"; // 默认值
        }

        if (prefixLength == 0) {
            return "0.0.0.0";
        }

        // 使用long类型避免位移操作的问题
        long mask = 0xFFFFFFFFL << (32 - prefixLength);
        
        int octet1 = (int) ((mask >>> 24) & 0xFF);
        int octet2 = (int) ((mask >>> 16) & 0xFF);
        int octet3 = (int) ((mask >>> 8) & 0xFF);
        int octet4 = (int) (mask & 0xFF);

        String result = octet1 + "." + octet2 + "." + octet3 + "." + octet4;
        System.out.println("前缀长度 " + prefixLength + " 转换为子网掩码: " + result);
        return result;
    }

    /**
     * 将子网掩码转换为前缀长度
     * 例如：255.255.255.0 -> 24, 255.0.0.0 -> 8
     */
    private static int netmaskToPrefixLength(String netmask) {
        try {
            String[] parts = netmask.split("\\.");
            if (parts.length != 4) {
                System.out.println("子网掩码格式不正确: " + netmask + ", 使用默认值24");
                return 24;
            }

            int prefixLength = 0;
            for (String part : parts) {
                int octet = Integer.parseInt(part);
                if (octet < 0 || octet > 255) {
                    System.out.println("子网掩码八位组值无效: " + octet + ", 使用默认值24");
                    return 24;
                }
                prefixLength += Integer.bitCount(octet);
            }

            System.out.println("子网掩码 " + netmask + " 转换为前缀长度: " + prefixLength);
            return prefixLength;
        } catch (Exception e) {
            System.out.println("转换子网掩码失败: " + netmask + " , 错误: " + e.getMessage() + ", 使用默认值24");
            return 24; // 默认值
        }
    }

    public static void main(String[] args) {
        System.out.println("=== 测试前缀长度到子网掩码的转换 ===");
        prefixLengthToNetmask(8);   // 应该是 255.0.0.0
        prefixLengthToNetmask(16);  // 应该是 255.255.0.0
        prefixLengthToNetmask(24);  // 应该是 255.255.255.0
        prefixLengthToNetmask(32);  // 应该是 255.255.255.255
        
        System.out.println("\n=== 测试子网掩码到前缀长度的转换 ===");
        netmaskToPrefixLength("255.0.0.0");      // 应该是 8
        netmaskToPrefixLength("255.255.0.0");    // 应该是 16
        netmaskToPrefixLength("255.255.255.0");  // 应该是 24
        netmaskToPrefixLength("255.255.255.255"); // 应该是 32
        
        System.out.println("\n=== 测试往返转换 ===");
        String mask8 = prefixLengthToNetmask(8);
        int prefix8 = netmaskToPrefixLength(mask8);
        System.out.println("8 -> " + mask8 + " -> " + prefix8);
        
        String mask16 = prefixLengthToNetmask(16);
        int prefix16 = netmaskToPrefixLength(mask16);
        System.out.println("16 -> " + mask16 + " -> " + prefix16);
    }
}
