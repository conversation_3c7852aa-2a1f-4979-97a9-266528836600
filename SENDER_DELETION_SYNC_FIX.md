# 发送端服务删除后接收端同步状态修复

## 问题描述

当发送端服务被删除后，接收端同步配置未能正确更新接收端状态，导致接收端无法删除对应的服务。

## 问题根源

1. **同步机制**: 发送端删除服务后，config.xml中的服务信息被删除并发送到接收端
2. **状态检测**: 接收端通过对比config.xml发现发送端不存在但接收端存在的服务
3. **状态更新**: 接收端应该自动更新这些服务的状态为`SENDER_DELETED_RECEIVER_EXIST`
4. **删除权限**: 只有状态为`SENDER_DELETED_RECEIVER_EXIST`的接收端服务才能被删除

## 修复内容

### 1. **ServiceController删除逻辑优化**

```java
// 特殊处理：如果是接收端服务且同步状态为发送端已删除，允许直接删除
boolean isReceiverService = (network != null && network == 1);
boolean canDelete = true;

if (isReceiverService) {
    // 获取接收端服务信息以检查同步状态
    Optional<UdpServiceInfo> receiverServiceOpt = serviceConfigService.getReceiverServiceInfoById(id);
    if (receiverServiceOpt.isPresent()) {
        UdpServiceInfo receiverService = receiverServiceOpt.get();
        String syncStatus = receiverService.getSyncStatus();
        
        // 如果同步状态为发送端已删除，允许删除
        if ("SENDER_DELETED_RECEIVER_EXIST".equals(syncStatus)) {
            System.out.println("发送端已删除，允许删除接收端服务");
            canDelete = true;
        }
    }
}
```

### 2. **前端删除确认消息优化**

```javascript
// 检查是否是接收端服务且同步状态为发送端已删除
var syncStatus = $(this).closest('tr').find('.badge').text();
var confirmMessage = '确定要删除服务 "' + serviceName + '" 吗? 此操作不可恢复!';

if (networkType === 1 && syncStatus.includes('发送端已删除')) {
    confirmMessage = '发送端服务已删除，确定要删除对应的接收端服务 "' + serviceName + '" 吗?\n\n此操作将清理接收端的残留配置，建议执行。';
}
```

### 3. **现有同步机制确认**

确认了现有的`syncConfigFromSrcapps`方法已经正确实现了状态更新逻辑：

```java
// 移除srcapps中不存在的服务，但保留接收端已配置的服务
for (UdpServiceInfo service : receiverServicesList.getServices()) {
    if (!processedIds.contains(service.getId())) {
        // 如果是已配置的服务，更新其同步状态为已删除
        if ("SENDER_ADDED_NO_CONFIG_SERVICE".equals(service.getSyncStatus()) ||
            "CONFIG_COMPLETED".equals(service.getSyncStatus())) {
            service.setSyncStatus(ReceiverSyncStatus.SENDER_DELETED_RECEIVER_EXIST.name());
            service.setUpdatedAt(LocalDateTime.now());
        }
    }
}
```

## 同步状态说明

### ReceiverSyncStatus枚举

- `SENDER_ADDED_NO_CONFIG`: 发送端新增服务，但配置文件未发送
- `SENDER_ADDED_NO_SERVICE`: 发送端新增服务，但接收端未创建服务
- `SENDER_ADDED_NO_CONFIG_SERVICE`: 发送端新增服务，但接收端未配置服务
- `SENDER_MODIFIED_NO_CONFIG`: 发送端已经修改服务，但配置文件未发送
- `SENDER_MODIFIED_NO_UPDATE`: 发送端已经修改服务，但接收端未更新配置服务
- **`SENDER_DELETED_RECEIVER_EXIST`**: 发送端已经删除服务，但接收端未删除服务
- `CONFIG_COMPLETED`: 配置已经完成

## 工作流程

1. **发送端删除服务**
   - 删除本地服务配置文件
   - 从config.xml中移除服务信息
   - 发送更新后的config.xml到接收端

2. **接收端接收配置**
   - 通过UDP接收config.xml文件
   - 触发`syncConfigFromSrcapps`方法

3. **状态自动更新**
   - 对比发送端config.xml和接收端服务列表
   - 发现接收端存在但发送端不存在的服务
   - 自动更新状态为`SENDER_DELETED_RECEIVER_EXIST`

4. **接收端删除服务**
   - 前端只显示状态为`SENDER_DELETED_RECEIVER_EXIST`的删除按钮
   - 后端验证同步状态允许删除操作
   - 执行删除并清理接收端配置

## 前端显示逻辑

```html
<!-- 删除按钮 - 当服务未运行，且同步状态为发送端已删除时显示 -->
<button th:if="${service.status != 'running' && service.syncStatus == 'SENDER_DELETED_RECEIVER_EXIST'}"
        class="btn btn-sm btn-danger delete-service"
        th:data-id="${service.id}"
        th:data-name="${service.name}">
    <i class="bi bi-trash"></i> 删除
</button>
```

## 测试场景

1. **正常删除流程**
   - 发送端删除服务
   - 接收端同步配置
   - 接收端服务状态更新为`SENDER_DELETED_RECEIVER_EXIST`
   - 接收端可以删除对应服务

2. **异常情况处理**
   - 网络中断导致配置同步失败
   - 手动触发配置同步
   - 验证状态更新正确性

## 注意事项

- 只有状态为`SENDER_DELETED_RECEIVER_EXIST`的接收端服务才能被删除
- 删除操作会清理接收端的所有相关配置文件
- 建议在发送端删除服务后及时同步接收端配置
- 前端提供友好的删除确认提示，说明这是清理操作

## 相关文件

- `ServiceController.java`: 删除服务的控制器逻辑
- `ServiceConfigurationService.java`: 服务配置管理和同步逻辑
- `services-page.html`: 前端服务列表页面
- `ConfigFileReceiverService.java`: 配置文件接收服务

## 修复验证

修复后，发送端服务删除时：
1. ✅ 接收端能够通过配置同步自动检测到发送端服务已删除
2. ✅ 接收端服务状态自动更新为`SENDER_DELETED_RECEIVER_EXIST`
3. ✅ 接收端显示删除按钮，允许清理残留配置
4. ✅ 删除操作提供友好的确认提示
5. ✅ 删除成功后清理所有相关配置文件
