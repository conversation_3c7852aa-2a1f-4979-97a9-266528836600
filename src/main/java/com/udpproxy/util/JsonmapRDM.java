package com.udpproxy.util;

import java.util.HashMap;
import java.util.Map;




/**Jsonmap 数据模型类
 * 提供 {success:true,a:"",b:""} 的map型的数据输出。
 * <AUTHOR>
 */
public class JsonmapRDM {
	
	/**
	 * 数据对象 
	 */
	HashMap<String,Object> dataObj = new HashMap<String,Object>();
	
	public JsonmapRDM(boolean success){
		this.dataObj.put("success",success);
	}
	
	/**
	 * 构造方法
	 * @param success	成功或失败
	 * @param text	如果为成功，text为message信息，否则为errors exception 信息
	 */
	public JsonmapRDM(boolean success,String text){
		this.dataObj.put("success",success);
		if(success){
			this.dataObj.put("message",text);
		}else{
			Map<String,String> m = new HashMap<String,String>();
			m.put("exception",text);
			this.dataObj.put("errors",m);;
		}
	}
	
	public boolean putPojo(String key,Object pojo){
		if(this.dataObj.containsKey(key))
			return false;
		this.dataObj.put(key, pojo);
		return true;
	}
	
	public String getData() throws Exception {
		String jsonData="";
		if(this.dataObj!= null){
			try {
				jsonData = JSONUtils.toJson(this.dataObj);
			} catch (Exception e) {
				throw new Exception(e);
			}
		}
		return jsonData;
	}

	/* (non-Javadoc)
	 * json类型代码为1
	 * @see com.unimas.ue.frame.ndof.datamodal.DataModal#getTypecode()
	 */
	/*public DMTypeEnum getType() {
		return DMTypeEnum.json;
	}*/


}

