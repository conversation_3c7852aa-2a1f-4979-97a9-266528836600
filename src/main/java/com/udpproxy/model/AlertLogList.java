package com.udpproxy.model;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

@XmlRootElement(name = "alertLogs")
@XmlAccessorType(XmlAccessType.FIELD)
public class AlertLogList {
    
    @XmlElement(name = "alertLog")
    private List<AlertLog> alertLogs;
    
    public AlertLogList() {
        alertLogs = new ArrayList<>();
    }
    
    public List<AlertLog> getAlertLogs() {
        return alertLogs;
    }
    
    public void setAlertLogs(List<AlertLog> alertLogs) {
        this.alertLogs = alertLogs;
    }
} 