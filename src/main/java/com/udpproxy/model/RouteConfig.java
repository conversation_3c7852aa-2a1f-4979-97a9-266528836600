package com.udpproxy.model;

import com.udpproxy.util.LocalDateTimeAdapter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.time.LocalDateTime;

@XmlRootElement(name = "routeConfig")
@XmlAccessorType(XmlAccessType.FIELD)
public class RouteConfig {

    @XmlElement
    private Long id;

    @XmlElement
    private String targetNetwork; // 目标网络IP

    @XmlElement
    private String netmask; // 子网掩码

    @XmlElement
    private String gateway; // 路由地址(网关)

    @XmlElement
    private String deviceName; // 网卡设备名称

    @XmlElement
    private String routeType; // 路由类型：via（通过网关）或 direct（直连）

    @XmlElement
    private String routeSource; // 路由来源：ui（界面添加）或 system（系统路由）

    @XmlElement
    private Integer priority; // 路由优先级：0-65535，默认100

    @XmlElement
    @XmlJavaTypeAdapter(LocalDateTimeAdapter.class)
    private LocalDateTime createdAt;

    @XmlElement
    @XmlJavaTypeAdapter(LocalDateTimeAdapter.class)
    private LocalDateTime updatedAt;

    public RouteConfig() {
        this.priority = 100; // 默认优先级为100
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTargetNetwork() {
        return targetNetwork;
    }

    public void setTargetNetwork(String targetNetwork) {
        this.targetNetwork = targetNetwork;
    }

    public String getNetmask() {
        return netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }

    public String getRouteSource() {
        return routeSource;
    }

    public void setRouteSource(String routeSource) {
        this.routeSource = routeSource;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取CIDR格式的网络地址
     */
    public String getCidrNetwork() {
        if (targetNetwork != null && netmask != null) {
            int cidr = convertNetmaskToCidr(netmask);
            return targetNetwork + "/" + cidr;
        }
        return targetNetwork;
    }

    /**
     * 将子网掩码转换为CIDR格式
     * 例如：255.0.0.0 -> 8, 255.255.255.0 -> 24
     */
    private int convertNetmaskToCidr(String netmask) {
        try {
            String[] parts = netmask.split("\\.");
            if (parts.length != 4) {
                return 24; // 默认值
            }

            int cidr = 0;
            for (String part : parts) {
                int octet = Integer.parseInt(part);
                if (octet < 0 || octet > 255) {
                    return 24; // 默认值
                }
                cidr += Integer.bitCount(octet);
            }

            return cidr;
        } catch (Exception e) {
            return 24; // 默认值
        }
    }

    /**
     * 生成路由命令 - 界面添加的路由统一使用via格式，包含metric和table参数
     */
    public String generateRouteCommand() {
        // 确定优先级，如果未设置则使用默认值100
        int metricValue = (priority != null) ? priority : 100;

        // 根据网卡名称确定table
        String table = getTableByDeviceName(deviceName);

        // 界面添加的路由统一使用via格式，包含metric和table参数
        return String.format("ip route add %s via %s dev %s metric %d table %s",
                getCidrNetwork(), gateway, deviceName, metricValue, table);
    }

    /**
     * 生成删除路由命令 - 界面添加的路由统一使用via格式删除，包含metric和table参数
     */
    public String generateDeleteRouteCommand() {
        // 确定优先级，如果未设置则使用默认值100
        int metricValue = (priority != null) ? priority : 100;

        // 根据网卡名称确定table
        String table = getTableByDeviceName(deviceName);

        if ("via".equals(routeType)) {
            // Via路由：ip route del 20.0.0.0/24 via ******** dev eth0 metric 100 table manager
            return String.format("ip route del %s via %s dev %s metric %d table %s",
                               getCidrNetwork(), gateway, deviceName, metricValue, table);
        } else {
            // 直连路由：ip route del 10.0.0.0/24 dev eth0 table manager
            return String.format("ip route del %s dev %s table %s",
                               getCidrNetwork(), deviceName, table);
        }
    }

    /**
     * 判断路由是否可编辑删除
     * 只有via方式的路由可以编辑删除（无论来源）
     */
    public boolean isEditable() {
        return "via".equals(routeType);
    }

    /**
     * 判断路由是否为via路由
     */
    public boolean isViaRoute() {
        return "via".equals(routeType);
    }

    /**
     * 根据设备名称确定对应的table
     * eth0 -> manager
     * eth1 -> business
     * 其他 -> manager (默认)
     */
    private String getTableByDeviceName(String deviceName) {
        if (deviceName == null) {
            return "manager";
        }

        if (deviceName.equals("eth1")) {
            return "business";
        } else {
            return "manager";
        }
    }
}
