package com.udpproxy.sm;

import java.io.FileInputStream;

public class CommandData {
    private Object dataType; // 指令头
    private Parameter[] params; // 命令需要的参数对象
    private FileInputStream input; // 指令头


    /** 指令信息 **/
    public String getDataTypeString() {
        if (dataType instanceof String) {
            return (String) dataType;
        } else {
            throw new RuntimeException("指令头对象类型不正确 ;");
        }
    }

    /** 指令信息 **/
    public byte[] getDataTypeByte() {
        if (dataType instanceof byte[]) {
            return (byte[]) dataType;
        } else {
            throw new RuntimeException("指令头对象类型不正确 ;");
        }
    }

    /** 命令中所需的命令 **/
    public Parameter[] getParams() { return params; }

    public CommandData(String dataType, Parameter... params) {
        this.dataType = dataType;
        this.params = params;
    }

    public CommandData(byte[] dataType, Parameter... params) {
        this.dataType = dataType;
        this.params = params;
    }

    public void addInput(FileInputStream input) { this.input = input; }

    public FileInputStream getInput() { return input; }
}