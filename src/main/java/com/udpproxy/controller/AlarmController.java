package com.udpproxy.controller;

import com.udpproxy.model.AlarmLog;
import com.udpproxy.model.UdpServiceList;
import com.udpproxy.service.AlarmService;
import com.udpproxy.service.ServiceConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/alarms")
public class AlarmController {

    private static final Logger logger = LoggerFactory.getLogger(AlarmController.class);

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private ServiceConfigurationService serviceConfigurationService;

    @GetMapping
    public String getAlarmsPage(Model model, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        try {
            // 获取服务列表用于下拉框
            List<Map<String, Object>> services = alarmService.getAllServiceNamesForAlarm();
            model.addAttribute("services", services);

            // 只添加查询条件，不执行查询
            model.addAttribute("alarms", new ArrayList<AlarmLog>());
            model.addAttribute("selectedAppName", null);
            model.addAttribute("selectedClientIp", null);
            model.addAttribute("selectedClientPort", null);
            model.addAttribute("startTime", null);
            model.addAttribute("endTime", null);
            model.addAttribute("pageSize", 10);
            model.addAttribute("currentPage", 0);
            model.addAttribute("totalPages", 0);
            model.addAttribute("totalItems", 0);

            return "alarms-page";
        } catch (Exception e) {
            logger.error("报警页面数据加载失败", e);
            model.addAttribute("errorMessage", "数据加载失败：" + e.getMessage());
            model.addAttribute("services", new ArrayList<>());
            model.addAttribute("alarms", new ArrayList<>());
            model.addAttribute("databaseError", true);
            return "alarms-page";
        }
    }

    @GetMapping("/search")
    public String searchAlarms(
            @RequestParam(required = false) String appname,
            @RequestParam(required = false) String clientIp,
            @RequestParam(required = false) Integer clientPort,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        try {
            // 获取服务列表用于下拉框
            List<Map<String, Object>> services = alarmService.getAllServiceNamesForAlarm();
            model.addAttribute("services", services);

            // 确保appname参数正确传递 - 需要转换为Long类型进行比较
            Long selectedAppNameLong = null;
            if (appname != null && !appname.trim().isEmpty()) {
                try {
                    selectedAppNameLong = Long.parseLong(appname);
                } catch (NumberFormatException e) {
                    logger.warn("无效的服务ID格式: {}", appname);
                }
            }

            model.addAttribute("selectedAppName", selectedAppNameLong);
            model.addAttribute("selectedClientIp", clientIp);
            model.addAttribute("selectedClientPort", clientPort);
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            model.addAttribute("pageSize", size);

            // 获取分页的报警记录
            Page<AlarmLog> alarmPage = alarmService.searchAlarms(appname, clientIp, clientPort, startTime, endTime, page, size);
            model.addAttribute("alarms", alarmPage.getContent());
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", alarmPage.getTotalPages());
            model.addAttribute("totalItems", alarmPage.getTotalElements());

            return "alarms-page";
        } catch (Exception e) {
            logger.error("报警查询失败", e);
            model.addAttribute("errorMessage", "查询失败：" + e.getMessage());
            model.addAttribute("services", new ArrayList<>());
            model.addAttribute("alarms", new ArrayList<>());
            model.addAttribute("selectedAppName", appname);
            model.addAttribute("selectedClientIp", clientIp);
            model.addAttribute("selectedClientPort", clientPort);
            model.addAttribute("startTime", startTime);
            model.addAttribute("endTime", endTime);
            model.addAttribute("pageSize", size);
            model.addAttribute("currentPage", 0);
            model.addAttribute("totalPages", 0);
            model.addAttribute("totalItems", 0);
            model.addAttribute("databaseError", true);
            return "alarms-page";
        }
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportAlarms(
            @RequestParam(required = false) String appname,
            @RequestParam(required = false) String clientIp,
            @RequestParam(required = false) Integer clientPort,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            HttpSession session) {

        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return ResponseEntity.status(401).body("未授权的操作".getBytes(StandardCharsets.UTF_8));
        }

        // 如果提供了服务ID，转换为服务名称
        String serviceName = null;
        if (appname != null && !appname.isEmpty()) {
            try {
                Long serviceId = Long.parseLong(appname);
                Optional<UdpServiceList.UdpServiceInfo> serviceInfo = serviceConfigurationService.getServiceInfoById(serviceId);
                if (serviceInfo.isPresent()) {
                    serviceName = serviceInfo.get().getName();
                }
            } catch (NumberFormatException e) {
                // 如果appname不是数字格式，直接使用其值作为服务名称
                serviceName = appname;
            }
        }

        // 查询所有符合条件的报警记录（不分页，导出全部）
        Page<AlarmLog> searchResults = alarmService.searchAlarms(serviceName, clientIp, clientPort, startTime, endTime, 0, 1000);
        List<AlarmLog> alarms = searchResults.getContent();

        // 生成CSV内容
        StringBuilder csvContent = new StringBuilder();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 添加CSV头
        csvContent.append("ID,服务名称,报警时间,报警信息,客户端IP,端口号\n");

        // 添加数据行
        for (AlarmLog alarm : alarms) {
            csvContent.append(alarm.getId()).append(",");
            csvContent.append(escapeCsvField(alarm.getServiceName())).append(",");
            csvContent.append(alarm.getAlarmTime() != null ? alarm.getAlarmTime().format(formatter) : "").append(",");
            csvContent.append(escapeCsvField(alarm.getAlarmInfo())).append(",");
            csvContent.append(escapeCsvField(alarm.getClientIp())).append(",");
            csvContent.append(alarm.getClientPort() != null ? alarm.getClientPort() : "").append("\n");
        }

        // 生成文件名
        String filename = "report_alarms_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("text/csv"));
        headers.setContentDispositionFormData("attachment", filename);

        // 返回CSV文件内容
        return ResponseEntity.ok()
                .headers(headers)
                .body(csvContent.toString().getBytes(StandardCharsets.UTF_8));
    }

    // 辅助方法：转义CSV字段中的特殊字符
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }

        // 如果字段中包含逗号、双引号或换行符，将其用双引号括起来，并将字段中的双引号替换为两个双引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }

        return field;
    }
}