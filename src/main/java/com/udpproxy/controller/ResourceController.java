package com.udpproxy.controller;

import com.udpproxy.model.UdpResource;
import com.udpproxy.service.ConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.time.format.DateTimeFormatter;

@Controller
@RequestMapping("/resources")
public class ResourceController {

    @Autowired
    private ConfigurationService configService;

    @GetMapping
    public String resources(Model model) {
        List<UdpResource> resourcesList = configService.getAllResources();
        
        // 处理日期格式化
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        resourcesList.forEach(resource -> {
            if (resource.getCreatedAt() != null) {
                resource.setCreatedAtStr(resource.getCreatedAt().format(formatter));
            }
        });
        
        model.addAttribute("resources", resourcesList);
        return "resources-page";
    }
    
    @GetMapping("/search")
    public String searchResources(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String ipRange,
            Model model) {
        
        List<UdpResource> resources;
        
        if (name != null && !name.trim().isEmpty()) {
            resources = configService.searchResourcesByName(name);
        } else if (ipRange != null && !ipRange.trim().isEmpty()) {
            resources = configService.searchResourcesByIpRange(ipRange);
        } else {
            resources = configService.getAllResources();
        }
        
        model.addAttribute("resources", resources);
        model.addAttribute("searchName", name);
        model.addAttribute("searchIpRange", ipRange);
        
        return "resources-page";
    }
    
    @GetMapping("/{id}")
    @ResponseBody
    public ResponseEntity<UdpResource> getResource(@PathVariable Long id) {
        Optional<UdpResource> resource = configService.getResourceById(id);
        return resource.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping
    @ResponseBody
    public ResponseEntity<UdpResource> addResource(@RequestBody UdpResource resource) {
        UdpResource saved = configService.addResource(resource);
        return ResponseEntity.ok(saved);
    }
    
    @PutMapping("/{id}")
    @ResponseBody
    public ResponseEntity<UdpResource> updateResource(
            @PathVariable Long id, 
            @RequestBody UdpResource resource) {
        
        Optional<UdpResource> updated = configService.updateResource(id, resource);
        return updated.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @DeleteMapping("/{id}")
    @ResponseBody
    public ResponseEntity<Void> deleteResource(@PathVariable Long id) {
        boolean deleted = configService.deleteResource(id);
        return deleted ? ResponseEntity.ok().build() : ResponseEntity.notFound().build();
    }
} 