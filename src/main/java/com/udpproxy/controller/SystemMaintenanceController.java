package com.udpproxy.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统运维管理控制器
 */
@Controller
@RequestMapping("/system/maintenance")
public class SystemMaintenanceController {

    private static final Logger logger = LoggerFactory.getLogger(SystemMaintenanceController.class);

    /**
     * 显示系统重启页面
     */
    @GetMapping("/reboot")
    public String showRebootPage(HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        // 检查是否为管理员
        String role = (String) session.getAttribute("role");
        if (!"ADMIN".equals(role)) {
            return "redirect:/index";
        }

        return "system-reboot";
    }

    /**
     * 系统重启接口
     */
    @PostMapping("/reboot")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> rebootSystem(HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查是否已登录
            Boolean authenticated = (Boolean) session.getAttribute("authenticated");
            if (authenticated == null || !authenticated) {
                response.put("success", false);
                response.put("message", "未登录，无权限执行此操作");
                return ResponseEntity.status(401).body(response);
            }

            logger.info("管理员请求系统重启");

            // 执行重启命令
            boolean rebootSuccess = executeRebootCommand();

            if (rebootSuccess) {
                response.put("success", true);
                response.put("message", "系统重启命令已执行，系统将在几秒钟后重启");
                logger.info("系统重启命令执行成功");
            } else {
                response.put("success", false);
                response.put("message", "系统重启命令执行失败");
                logger.error("系统重启命令执行失败");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("系统重启操作失败", e);
            response.put("success", false);
            response.put("message", "系统重启操作失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 执行系统重启命令
     */
    private boolean executeRebootCommand() {
        try {
            logger.info("开始执行系统重启命令");

            // 使用ProcessBuilder执行reboot命令
            ProcessBuilder processBuilder = new ProcessBuilder();

            // 根据操作系统选择不同的重启命令
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                // Windows系统
                processBuilder.command("shutdown", "/r", "/t", "5", "/f");
                logger.info("执行Windows重启命令: shutdown /r /t 5 /f");
            } else {
                // Linux/Unix系统 - 直接使用reboot命令
                processBuilder.command("reboot");
                logger.info("执行Linux重启命令: reboot");
            }

            // 启动进程
            Process process = processBuilder.start();

            // 等待命令执行完成（最多等待10秒）
            boolean finished = process.waitFor(10, java.util.concurrent.TimeUnit.SECONDS);

            if (!finished) {
                // 如果10秒内没有完成，强制终止进程
                process.destroyForcibly();
                logger.warn("重启命令执行超时，已强制终止进程");
                return false;
            }

            int exitCode = process.exitValue();
            logger.info("重启命令执行完成，退出码: {}", exitCode);

            // 读取命令输出（如果有）
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("重启命令输出: {}", line);
                }
            }

            // 读取错误输出（如果有）
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.warn("重启命令错误输出: {}", line);
                }
            }

            // 退出码为0表示成功
            return exitCode == 0;

        } catch (IOException e) {
            logger.error("执行重启命令时发生IO异常", e);
            return false;
        } catch (InterruptedException e) {
            logger.error("等待重启命令执行时被中断", e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            logger.error("执行重启命令时发生未知异常", e);
            return false;
        }
    }

}
