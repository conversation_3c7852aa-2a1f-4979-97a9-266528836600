package com.udpproxy.controller;

import com.udpproxy.model.UdpResource;
import com.udpproxy.model.UdpService;
import com.udpproxy.model.UdpServiceList;
import com.udpproxy.model.UdpServiceList.UdpServiceInfo;
import com.udpproxy.service.ConfigurationService;
import com.udpproxy.service.ServiceConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.HashMap;
import javax.servlet.http.HttpSession;
import com.udpproxy.util.DebugLogger;
import java.io.File;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.util.ArrayList;
import com.udpproxy.model.NetworkConfig;
import com.udpproxy.service.NetworkConfigService;
import com.udpproxy.model.NetworkConfigList;
import java.util.Properties;
import java.io.FileInputStream;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

@Controller
@RequestMapping("/services")
public class ServiceController {

    private static final Logger logger = LoggerFactory.getLogger(ServiceController.class);

    @Autowired
    private ServiceConfigurationService serviceConfigService;

    @Autowired
    private ConfigurationService resourceConfigService;

    @Autowired
    private NetworkConfigService networkConfigService;

    @GetMapping
    public String getServicesPage(Model model, HttpSession session) {
        // 检查并修复服务状态
        serviceConfigService.checkAndFixServiceStates();

        // 强制刷新服务列表，确保读取最新配置
        serviceConfigService.forceReloadServicesList();

        List<UdpServiceInfo> filteredServices = new ArrayList<>();

        // 从配置文件读取network值
        int networkType = 1; // 默认为发送端
        try {
            //File configFile = new File("config/cfcard");
            File configFile = new File("/boot/cfcard");
            if (configFile.exists()) {
                Properties props = new Properties();
                try (FileInputStream fis = new FileInputStream(configFile)) {
                    props.load(fis);
                    String networkStr = props.getProperty("network", "0");
                    networkType = Integer.parseInt(networkStr);

                    // 设置ServiceConfigService的network值
                    serviceConfigService.setNetwork(networkType);

                    // 将network值存入session
                    session.setAttribute("networkType", networkType);
                }
            }
        } catch (Exception e) {
            logger.error("读取network配置失败", e);
        }

        List<UdpServiceInfo> allServices = serviceConfigService.getAllServicesInfo();
        if (allServices != null) {
            for (UdpServiceInfo service : allServices) {
                filteredServices.add(service);
            }
        }


        // 计算运行中和已部署的服务数量
        long runningCount = 0;
        long deployedCount = 0;

        for (UdpServiceInfo service : filteredServices) {
            System.out.println("筛选后的服务: ID=" + service.getId() + ", 名称=" + service.getName() +
                ", 网络类型=" + (service.getNetwork() != null && service.getNetwork() == 1 ? "接收端" : "发送端") +
                ", 状态=" + service.getStatus());
            if ("running".equals(service.getStatus())) {
                runningCount++;
                deployedCount++;
            } else if ("deployed".equals(service.getStatus()) || "stopped".equals(service.getStatus())) {
                deployedCount++;
            }
        }

        model.addAttribute("services", filteredServices);
        model.addAttribute("runningCount", runningCount);
        model.addAttribute("deployedCount", deployedCount);
        model.addAttribute("networkType", networkType); // 传递网络类型给前端

        return "services-page";
    }



    @PostMapping("/deploy/{id}")
    @ResponseBody
    public Map<String, Object> deployService(@PathVariable Long id,
                                        HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        int network = serviceConfigService.getNetwork();
        try {
            DebugLogger.log("接收到部署服务请求，ID: " + id + ", 网络类型: " + network);

            // 检查用户是否已登录
            Boolean authenticated = (Boolean) session.getAttribute("authenticated");
            if (authenticated == null || !authenticated) {
                response.put("success", false);
                response.put("message", "未登录，请先登录");
                return response;
            }

            // 根据网络类型获取服务信息
            UdpService service = null;

            if (network == 1) {
                // 接收端服务
                DebugLogger.log("处理接收端服务部署");
                Optional<UdpServiceInfo> receiverServiceOpt = serviceConfigService.getReceiverServiceInfoById(id);

                if (receiverServiceOpt.isPresent()) {
                    Optional<UdpService> serviceOpt = serviceConfigService.getServiceById(id);
                    if (serviceOpt.isPresent()) {
                        service = serviceOpt.get();
                        service.setNetwork(1); // 确保是接收端
                        DebugLogger.log("已获取接收端服务配置: " + service.getName());
                    } else {
                        response.put("success", false);
                        response.put("message", "未找到接收端服务配置");
                        return response;
                    }
                } else {
                    response.put("success", false);
                    response.put("message", "未找到指定的接收端服务");
                    return response;
                }
            } else {
                // 发送端服务
                DebugLogger.log("处理发送端服务部署");
                Optional<UdpService> serviceOpt = serviceConfigService.getServiceById(id);
                if (!serviceOpt.isPresent()) {
                    response.put("success", false);
                    response.put("message", "未找到指定的服务");
                    return response;
                }

                service = serviceOpt.get();
                service.setNetwork(0); // 确保是发送端
                DebugLogger.log("已获取发送端服务配置: " + service.getName());
            }

            // 调用部署服务的方法
            boolean deployed = serviceConfigService.deployService(service);

            if (deployed) {

                response.put("success", true);
                response.put("message", "服务部署成功");
            } else {
                response.put("success", false);
                response.put("message", "服务部署失败，请检查服务配置是否完整");
            }
        } catch (Exception e) {
            DebugLogger.error("部署服务时发生错误", e);
            response.put("success", false);
            response.put("message", "部署服务时发生错误: " + e.getMessage());
        }

        return response;
    }


    @GetMapping("/new")
    public String newServiceForm(Model model, HttpSession session) {
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        // 创建新服务对象
        UdpService service = new UdpService();

        model.addAttribute("service", service);
        model.addAttribute("isNew", true);
        // 获取网络接口列表，用于发送端监听地址下拉框
        List<NetworkConfig> interfaces = networkConfigService.getAllInterfaces();
        model.addAttribute("networkInterfaces", interfaces);
        return "service-config-page";
    }

    @GetMapping("/edit/{id}")
    public String editServiceForm(@PathVariable Long id,
                                 Model model,
                                 HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }


        Optional<UdpService> serviceOpt = serviceConfigService.loadServiceConfig(id);
        Integer network = serviceConfigService.getNetwork();

        // 获取服务完整信息

        if (serviceOpt.isPresent()) {
            UdpService service = serviceOpt.get();

            // 根据服务类型设置network值
            if (network != null && network == 1) {
                logger.info("确认为接收端服务，设置network=1");
                service.setNetwork(1);
            } else {
                logger.info("确认为发送端服务，设置network=0");
                service.setNetwork(0);
            }

            model.addAttribute("service", service);
            model.addAttribute("isNew", false);
            // 根据服务类型设置activeTab
            int activeTab = service.getNetwork() != null ? service.getNetwork() : 0;
            model.addAttribute("activeTab", activeTab);

            // 添加日志用于调试
            logger.info("编辑服务: ID={}, 名称={}, 网络类型={}, 服务器IP={}, 服务器端口={}, getSendaddrmap={}",
                      service.getId(), service.getName(), service.getNetwork(),
                      service.getServerIp(), service.getServerPort(), service.getSendaddrmap());

            // 获取网络接口列表，用于发送端监听地址下拉框
            List<NetworkConfig> interfaces = networkConfigService.getAllInterfaces();
            model.addAttribute("networkInterfaces", interfaces);

            return "service-config-page";
        } else {
            // 服务不存在，重定向到服务列表
            return "redirect:/services?error=服务不存在";
        }
    }

    @PostMapping("/save")
    public String saveService(@ModelAttribute UdpService service,
                              @RequestParam(required = false, defaultValue = "0") Integer network,
                              @RequestParam("isNew") boolean isNew,
                              @RequestParam(value = "multicast", required = false) String multicast,
                              @RequestParam(value = "special_value", required = false) String specialValue,
                              @RequestParam(value = "rules", required = false) String rules,
                              @RequestParam(value = "multicastIp", required = false) String multicastIp,
                              @RequestParam(value = "udpFlood", required = false) String udpFlood,
                              @RequestParam(value = "sendaddrmap", required = false) String sendaddrmap,
                              @RequestParam(value = "iprange", required = false) String iprange,
                              HttpServletRequest request,
                              RedirectAttributes redirectAttributes,
                              HttpSession session) {
        try {
            logger.info("======== 开始处理服务保存请求 ========");
            logger.info("服务ID: {}, 服务名称: {}, 是否新建: {}",
                      service.getId(), service.getName(), isNew);
            logger.info("接收到的参数 - 组播功能: {}, 组播IP: {}, 指定源地址: {}, 规则: {}, UDP Flood防护: {}",
                      multicast, multicastIp, specialValue, rules, udpFlood);

            // 检查用户是否已登录
            Boolean authenticated = (Boolean) session.getAttribute("authenticated");
            if (authenticated == null || !authenticated) {
                logger.warn("未登录用户尝试保存服务");
                return "redirect:/login";
            }

            // 记录表单中的所有参数，用于调试
            logger.debug("所有请求参数:");
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                logger.debug("  {} = {}", paramName, request.getParameter(paramName));
            }

            // 设置网络类型
            service.setNetwork(network);
            logger.info("服务网络类型: {}", network == 1 ? "接收端" : "发送端");

            // 处理组播功能参数
            if (multicast != null) {
                // 检查是否是重复的true/false值，例如"true,true"
                if (multicast.contains(",")) {
                    // 只取第一个值
                    multicast = multicast.split(",")[0];
                    logger.info("修正组播功能值，移除重复: {}", multicast);
                }
                service.setMulticast(multicast);
                logger.info("设置组播功能: {}", multicast);

                // 当组播功能为false时，强制清空指定源地址
                if ("false".equals(multicast)) {
                    service.setSpecial_value("");
                    service.setMulticastIp("");
                    logger.info("组播功能为false，已清空指定源地址和组播IP");
                    // 确保specialValue为空
                    specialValue = "";
                }
            } else {
                service.setMulticast("false");
                logger.info("组播功能参数为空，设置默认值: false");

                // 强制清空指定源地址和组播IP
                service.setSpecial_value("");
                service.setMulticastIp("");
                logger.info("组播功能为false，已清空指定源地址和组播IP");
                // 确保specialValue为空
                specialValue = "";
            }

            // 处理组播IP地址
            if (multicastIp != null) {
                service.setMulticastIp(multicastIp);
                logger.info("设置组播IP地址: {}", multicastIp);
            } else {
                service.setMulticastIp("");
                logger.info("组播IP地址参数为空，设置默认值: 空字符串");
            }

            // 处理地址映射
            if (sendaddrmap != null) {
                service.setSendaddrmap(sendaddrmap);
                logger.info("设置sendaddrmap地址: "+sendaddrmap);
            } else {
                service.setSendaddrmap("");
                logger.info("sendaddrmap参数为空，设置默认值: 空字符串");
            }

            // 处理指定网段设置
            if (iprange != null) {
                service.setIprange(iprange);
                logger.info("设置指定网段设置: {}", iprange);
            } else {
                service.setIprange("");
                logger.info("指定网段设置参数为空，设置默认值: 空字符串");
            }

            // 处理指定源地址
            if (specialValue != null) {
                // 直接设置指定源地址，覆盖原值
                service.setSpecial_value(specialValue);
                logger.info("设置指定源地址: {}", specialValue);
            } else {
                service.setSpecial_value("");
                logger.info("指定源地址参数为空，设置默认值: 空字符串");
            }

            // 处理包过滤规则
            if (rules != null) {
                service.setRules(rules);
                logger.info("设置包过滤规则: {}", rules);
            } else {
                service.setRules("");
                logger.info("包过滤规则参数为空，设置默认值: 空字符串");
            }

            // 处理UDP Flood防护
            if (udpFlood != null) {
                service.setUdpFlood(udpFlood);
                logger.info("设置UDP Flood防护: {}", udpFlood);
            } else {
                service.setUdpFlood("1");  // 默认启用
                logger.info("UDP Flood防护参数为空，设置默认值: 1 (启用)");
            }

            // 确保审计设置正确更新到service对象
            String auditOption = request.getParameter("audit");
            if (auditOption != null) {
                service.setAudit(auditOption);
                logger.info("设置审计: {}", auditOption);
            } else {
                service.setAudit("1"); // 默认审计
                logger.info("审计参数为空，设置默认值: 1 (审计)");
            }

            if (isNew) {
                // 设置默认值，但不覆盖用户选择的审计选项
                service.setStatus(""); // 未部署状态

                // 打印服务对象配置内容
                logger.info("==== 保存前服务配置 ====");
                logger.info("组播功能: {}", service.getMulticast());
                logger.info("组播IP地址: {}", service.getMulticastIp());
                logger.info("指定源地址: {}", service.getSpecial_value());
                logger.info("包过滤规则: {}", service.getRules());
                logger.info("UDP Flood防护: {}", service.getUdpFlood());

                // 保存新服务
                UdpService savedService = serviceConfigService.addService(service);
                logger.info("新服务创建成功 - ID: {}, 类型: {}",
                          savedService.getId(),
                          savedService.getNetwork() == 1 ? "接收端" : "发送端");

                // 如果是接收端服务，加载保存后的完整信息并记录日志
                if (savedService.getNetwork() != null && savedService.getNetwork() == 1) {
                    Optional<UdpService> savedReceiverOpt = serviceConfigService.getReceiverServiceById(savedService.getId());
                    if (savedReceiverOpt.isPresent()) {
                        UdpService savedReceiver = savedReceiverOpt.get();
                        logger.info("接收端服务保存后信息 - ID: {}, 服务器IP: {}, 服务器端口: {}",
                                  savedReceiver.getId(),
                                  savedReceiver.getServerIp(),
                                  savedReceiver.getServerPort());
                    }
                }

                redirectAttributes.addFlashAttribute("success", "服务创建成功");
            } else {
                // 获取现有服务
                Optional<UdpService> existingServiceOpt = serviceConfigService.loadServiceConfig(service.getId());
                if (existingServiceOpt.isPresent()) {
                    UdpService existingService = existingServiceOpt.get();

                    // 对于接收端服务，编辑保存后应该将状态改为"已配置"，需要重新部署
                    if (service.getNetwork() != null && service.getNetwork() == 1) {
                        // 接收端服务：只有在运行中时保持原状态，其他情况都改为"已配置"
                        if ("running".equals(existingService.getStatus())) {
                            service.setStatus(existingService.getStatus());
                            logger.info("接收端服务正在运行中，保持原状态: {}", existingService.getStatus());
                        } else {
                            service.setStatus("configured");
                            logger.info("接收端服务编辑保存，状态改为已配置，需要重新部署");
                        }

                        // 记录现有和提交的服务器配置
                        logger.info("更新接收端服务 - 现有配置: 服务器IP={}, 服务器端口={}",
                                  existingService.getServerIp(), existingService.getServerPort());
                        logger.info("更新接收端服务 - 提交配置: 服务器IP={}, 服务器端口={}",
                                  service.getServerIp(), service.getServerPort());
                    } else {
                        // 发送端服务：只有在运行中时保持原状态，其他情况都改为"已配置"
                        if ("running".equals(existingService.getStatus())) {
                            service.setStatus(existingService.getStatus());
                            logger.info("发送端服务正在运行中，保持原状态: {}", existingService.getStatus());
                        } else {
                            service.setStatus("configured");
                            logger.info("发送端服务编辑保存，状态改为已配置，需要重新部署");
                        }
                    }
                }

                // 打印服务对象配置内容
                logger.info("==== 更新前服务配置 ====");
                logger.info("组播功能: {}", service.getMulticast());
                logger.info("组播IP地址: {}", service.getMulticastIp());
                logger.info("指定源地址: {}", service.getSpecial_value());
                logger.info("包过滤规则: {}", service.getRules());
                logger.info("UDP Flood防护: {}", service.getUdpFlood());
                logger.info("sendaddrmap: {}", service.getSendaddrmap());
                // 更新服务
                Optional<UdpService> updatedServiceOpt = serviceConfigService.updateService(service.getId(), service);

                // 记录更新后的服务信息
                if (updatedServiceOpt.isPresent()) {
                    UdpService updatedService = updatedServiceOpt.get();

                    logger.info("==== 更新后服务配置 ====");
                    logger.info("组播功能: {}", updatedService.getMulticast());
                    logger.info("组播IP地址: {}", updatedService.getMulticastIp());
                    logger.info("指定源地址: {}", updatedService.getSpecial_value());
                    logger.info("包过滤规则: {}", updatedService.getRules());
                    logger.info("UDP Flood防护: {}", updatedService.getUdpFlood());

                    if (updatedService.getNetwork() != null && updatedService.getNetwork() == 1) {
                        logger.info("接收端服务更新后信息 - ID: {}, 服务器IP: {}, 服务器端口: {}",
                                  updatedService.getId(),
                                  updatedService.getServerIp(),
                                  updatedService.getServerPort());
                    }
                }

                redirectAttributes.addFlashAttribute("success", "服务更新成功");
            }

            // 根据服务类型返回到对应的选项卡
            int tab = (service.getNetwork() != null && service.getNetwork() == 1) ? 1 : 0;
            logger.info("服务保存成功，重定向到选项卡: {}", tab);
            logger.info("======== 服务保存请求结束 ========");

            return "redirect:/services?tab=" + tab;
        } catch (Exception e) {
            logger.error("保存服务失败", e);
            redirectAttributes.addFlashAttribute("error", "保存服务失败: " + e.getMessage());
            return isNew ? "redirect:/services/new" : "redirect:/services/edit/" + service.getId();
        }
    }

    /**
     * 启动服务
     */
    @PostMapping("/start/{id}")
    @ResponseBody
    public Map<String, Object> startService(@PathVariable Long id,@RequestParam(required = false, defaultValue = "0") Integer network, HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        network = serviceConfigService.getNetwork();
        try {
            System.out.println("接收到启动服务请求，ID: " + id);

            // 检查用户是否已登录
            Boolean authenticated = (Boolean) session.getAttribute("authenticated");
            if (authenticated == null || !authenticated) {
                System.out.println("启动失败: 用户未登录");
                response.put("success", false);
                response.put("message", "未登录，请先登录");
                return response;
            }
            // 调用启动服务的方法
            boolean started = serviceConfigService.startService(id);

            if (started) {
                System.out.println("启动成功: 服务ID=" + id);
                response.put("success", true);
                response.put("message", "服务启动成功");
            } else {
                System.out.println("启动失败: 服务ID=" + id);
                response.put("success", false);
                response.put("message", "服务启动失败，请检查服务配置");
            }
        } catch (Exception e) {
            System.out.println("启动异常: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "启动服务时发生错误: " + e.getMessage());
        }

        return response;
    }

    /**
     * 停止服务
     */
    @PostMapping("/stop/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> stopService(@PathVariable Long id,@RequestParam(required = false, defaultValue = "0") Integer network, HttpSession session) {
        Map<String, Object> response = new HashMap<>();
        network = serviceConfigService.getNetwork();
        // 检查用户是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录，请先登录");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            Optional<UdpService> serviceOpt = serviceConfigService.getServiceById(id);
            if (!serviceOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "未找到指定的服务");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            UdpService service = serviceOpt.get();

            // 检查服务是否已启动
            // if (!"running".equals(service.getStatus())) {
            //     response.put("success", false);
            //     response.put("message", "服务未启动或当前状态不允许停止");
            //     return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            // }

            // 调用停止服务的方法
            boolean stopped = serviceConfigService.stopService(id);

            if (stopped) {
                // 记录审计日志（如果启用审计）
                if ("audit".equals(service.getAudit())) {
                    // 假设有auditService可以记录审计日志
                    // auditService.logOperation("停止服务", service.getName());
                }

                response.put("success", true);
                response.put("message", "服务停止成功");
            } else {
                response.put("success", false);
                response.put("message", "服务停止失败");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "停止服务时发生错误: " + e.getMessage());
            e.printStackTrace(); // 记录到服务器日志
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 删除服务
     */
    @PostMapping("/delete/{id}")
    @ResponseBody
    public Map<String, Object> deleteService(@PathVariable Long id,@RequestParam(required = false, defaultValue = "0") Integer network, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        try {
            System.out.println("接收到删除服务请求，ID: " + id);
            network = serviceConfigService.getNetwork();
            Optional<UdpService> serviceOpt = serviceConfigService.loadServiceConfig(id);

            if (!serviceOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "未找到指定的服务");
                return response;
            }

            UdpService service = serviceOpt.get();

            // 检查服务是否正在运行
            if ("running".equals(service.getStatus())) {
                response.put("success", false);
                response.put("message", "服务正在运行，请先停止服务");
                return response;
            }

            // 调用删除服务的方法
            boolean deleted = serviceConfigService.deleteService(id);

            if (deleted) {
                serviceConfigService.sendConfigToSrcapps();
                response.put("success", true);
                response.put("message", "服务删除成功");
            } else {
                response.put("success", false);
                response.put("message", "服务删除失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "删除服务时发生错误: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取服务详情
     */
    @GetMapping("/{id}/details")
    @ResponseBody
    public Map<String, Object> serviceDetails(@PathVariable Long id, @RequestParam(required = false, defaultValue = "0") Integer network) {
        Map<String, Object> result = new HashMap<>();
        network = serviceConfigService.getNetwork();
        try {
            DebugLogger.log("获取服务详情 - ID: " + id + ", 网络类型: " + network);

            // 根据网络类型获取服务信息
            if (network == 1) {
                // 尝试从接收端配置获取服务
                Optional<UdpServiceInfo> receiverServiceOpt = serviceConfigService.getReceiverServiceInfoById(id);
                if (receiverServiceOpt.isPresent()) {
                    UdpServiceInfo receiverService = receiverServiceOpt.get();
                    result.put("success", true);
                    result.put("id", receiverService.getId());
                    result.put("name", receiverService.getName());
                    result.put("status", receiverService.getStatus());
                    result.put("network", 1); // 明确设置为接收端
                    result.put("description", receiverService.getDescription());
                    result.put("messageType", receiverService.getMessageType());
                    //result.put("audit", receiverService.getAuditOption());
                    result.put("createdAt", receiverService.getCreatedAt());
                    result.put("syncStatus", receiverService.getSyncStatus());


                    // 添加接收端特有的信息
                    Map<String, Object> receiverInfo = new HashMap<>();

                    // 获取完整的接收端服务信息，以获取服务器IP和端口
                    Optional<UdpService> fullServiceOpt = serviceConfigService.getReceiverServiceById(id);
                    if (fullServiceOpt.isPresent()) {
                        UdpService fullService = fullServiceOpt.get();
                        result.put("audit", fullService.getAudit());
                        result.put("trafficLimit", fullService.getTrafficLimit());
                        receiverInfo.put("serverIp", fullService.getServerIp() != null ? fullService.getServerIp() : "未设置");
                        receiverInfo.put("serverPort", fullService.getServerPort() != null ? fullService.getServerPort().toString() : "未设置");
                    } else {
                        receiverInfo.put("serverIp", "未配置");
                        receiverInfo.put("serverPort", "未配置");
                    }

                    result.put("receiverInfo", receiverInfo);

                    // 添加状态信息
                    Map<String, String> statusInfo = new HashMap<>();
                    //statusInfo.put("uptime", getServiceUptime(receiverService.getStatus()));
                    statusInfo.put("connections", "0"); // 默认值
                    statusInfo.put("traffic", "无流量"); // 默认值
                    result.put("statusInfo", statusInfo);

                    return result;
                } else {
                    // 如果没有找到接收端服务，尝试从一般服务获取
                    DebugLogger.log("未在接收端配置找到服务，尝试从一般服务列表获取");
                    result.put("success", false);
                    result.put("message", "未找到接收端服务");
                    return result;
                }
            } else {
                // 从一般服务列表获取发送端服务
                Optional<UdpService> serviceOpt = serviceConfigService.getServiceById(id);
                if (serviceOpt.isPresent()) {
                    UdpService service = serviceOpt.get();
                    service.setNetwork(0); // 强制设置为发送端

                    result.put("success", true);
                    result.put("id", service.getId());
                    result.put("name", service.getName());
                    result.put("status", service.getStatus());
                    result.put("network", 0); // 明确设置为发送端
                    result.put("description", service.getDescription());
                    result.put("messageType", service.getMessageType());
                    result.put("audit", service.getAudit());
                    result.put("createdAt", service.getCreatedAt());
                    result.put("trafficLimit", service.getTrafficLimit());
                    // 添加发送端特有的配置信息
                    Map<String, Object> senderInfo = new HashMap<>();
                    senderInfo.put("proxyIp", service.getProxyIp() != null ? service.getProxyIp() : "未设置");
                    senderInfo.put("proxyPort", service.getProxyPort() != null ? service.getProxyPort().toString() : "未设置");
                    senderInfo.put("contentKeyCheck", service.getContentKeyCheck());
                    senderInfo.put("contentKeywords", service.getContentKeyCheck() != null && service.getContentKeyCheck() == 1 ?
                                                 service.getContentKeywords() : "未启用关键字检测");
                    senderInfo.put("protocolFilter", service.getProtocolFilter() != null ? service.getProtocolFilter() : "无过滤");

                    // 添加告警后处理信息
                    senderInfo.put("unpassDeal", service.getUnpassDeal() != null ? service.getUnpassDeal() : "0");
                    senderInfo.put("vehiclePlateCheck", service.getVehiclePlateCheck());
                    senderInfo.put("idCardCheck", service.getIdCardCheck());
                    senderInfo.put("crc16FormatCheck", service.getCrc16FormatCheck());
                    senderInfo.put("asnFormatCheck", service.getAsnFormatCheck());

                    // 添加指定网段信息
                    senderInfo.put("iprange", service.getIprange());
                    senderInfo.put("iprangeDisplay", formatIprangeForDisplay(service.getIprange()));

                    result.put("senderInfo", senderInfo);

                    // 添加状态信息
                    Map<String, String> statusInfo = new HashMap<>();
                    //statusInfo.put("uptime", getServiceUptime(service.getStatus()));
                    statusInfo.put("connections", "0"); // 默认值
                    statusInfo.put("traffic", "无流量"); // 默认值
                    result.put("statusInfo", statusInfo);

                    return result;
                } else {
                    result.put("success", false);
                    result.put("message", "未找到发送端服务");
                    return result;
                }
            }

        } catch (Exception e) {
            DebugLogger.log("获取服务详情异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取服务详情失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 格式化指定网段信息用于显示（格式：ip1:port1;ip2:port2;ip3:port3）
     */
    private String formatIprangeForDisplay(String iprange) {
        if (iprange == null || iprange.trim().isEmpty()) {
            return "全网段";
        }

        try {
            // 使用与解析逻辑相同的分割方法，确保一致性
            logger.info("格式化指定网段显示，输入: {}", iprange);

            StringBuilder display = new StringBuilder();

            // 先移除开头和结尾可能的空#
            String cleanInput = iprange.trim();
            if (cleanInput.startsWith("#")) {
                cleanInput = cleanInput.substring(1);
            }
            if (cleanInput.endsWith("#")) {
                cleanInput = cleanInput.substring(0, cleanInput.length() - 1);
            }

            // 使用#分割，得到所有段落
            String[] segments = cleanInput.split("#");
            logger.info("格式化分割后得到{}个段落", segments.length);

            int count = 0;
            for (String segment : segments) {
                if (segment.trim().isEmpty()) continue;

                count++;
                logger.info("格式化第{}个段落: {}", count, segment);

                String[] parts = segment.split("-");
                if (parts.length == 2) {
                    // 保留完整的IP:端口格式，只取第一个（因为通常ip1:port1-ip1:port1是相同的）
                    String ipPort = parts[0];

                    if (count > 1) {
                        display.append(";");
                    }
                    display.append(ipPort);
                }
            }

            String result = count > 0 ? display.toString() : "全网段";
            logger.info("格式化完成，共{}个段落，输出: {}", count, result);
            return result;

        } catch (Exception e) {
            logger.error("格式化指定网段显示失败: {}", e.getMessage());
            return "格式错误";
        }
    }

    // 发送配置文件到srcapps目录
    @PostMapping("/send-config/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> sendConfigFile(@PathVariable Long id, HttpSession session) {
        Map<String, Object> response = new HashMap<>();

        // 检查用户是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            response.put("success", false);
            response.put("message", "未登录，请先登录");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            // 获取服务
            Optional<UdpService> serviceOpt = serviceConfigService.getServiceById(id);
            if (!serviceOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "未找到指定的服务");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            UdpService service = serviceOpt.get();

            // 确保是发送端服务
            //if (service.getNetwork() != null && service.getNetwork() == 1) {
            //    response.put("success", false);
            //    response.put("message", "只有发送端服务可以发送配置到srcapps目录");
            //    return ResponseEntity.ok(response);
            //}

            // 发送配置文件到srcapps目录
            boolean success = serviceConfigService.sendConfigToSrcapps(id);

            response.put("success", success);
            if (success) {
                response.put("message", "配置文件已成功发送到srcapps目录");
            } else {
                response.put("message", "发送配置文件失败");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("发送配置文件失败", e);
            response.put("success", false);
            response.put("message", "发送配置文件时发生错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 从srcapps同步配置
     */
    @PostMapping("/sync-from-srcapps")
    @ResponseBody
    public Map<String, Object> syncFromSrcapps() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 同步配置
            boolean synced = serviceConfigService.syncConfigFromSrcapps();

            if (synced) {
                result.put("success", true);
                result.put("message", "配置已从srcapps同步");
            } else {
                result.put("success", false);
                result.put("message", "从srcapps同步配置失败");
            }
        } catch (Exception e) {
            DebugLogger.error("从srcapps同步配置时发生错误", e);
            result.put("success", false);
            result.put("message", "从srcapps同步配置时发生错误: " + e.getMessage());
        }

        return result;
    }


    /**
     * 创建接收端服务
     */
    @PostMapping("/create-receiver/{id}")
    @ResponseBody
    public Map<String, Object> createReceiverService(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        serviceConfigService.setNetwork(1);
        try {
            logger.info("开始创建接收端服务，ID: " + id);

            // 使用专门的接收端服务获取方法，确保从接收端配置文件获取最新状态
            Optional<UdpServiceInfo> serviceOpt = serviceConfigService.getReceiverServiceInfoById(id);
            if (!serviceOpt.isPresent()) {
                logger.error("未找到指定的服务，ID: " + id);
                result.put("success", false);
                result.put("message", "未找到指定的服务");
                return result;
            }

            UdpServiceInfo serviceInfo = serviceOpt.get();

            // 打印详细的服务信息用于调试
            logger.info("服务详情 - ID: {}, 名称: {}, 网络类型: {}, 同步状态: {}",
                    serviceInfo.getId(), serviceInfo.getName(),
                    serviceInfo.getNetwork(), serviceInfo.getSyncStatus());


            // 创建接收端服务配置
            logger.info("开始调用createReceiverServiceConfig创建接收端服务配置，ID: " + id);
            boolean created = serviceConfigService.createReceiverServiceConfig(id);

            if (created) {
                logger.info("接收端服务配置创建成功，ID: " + id);

                // 强制重新加载服务列表，确保前端能获取到最新状态
                //serviceConfigService.forceReloadServicesList();

                // 使用专门的方法更新接收端配置文件中的服务状态，而不是通过syncConfigFromSrcapps
                logger.info("更新接收端配置文件中的服务状态");
                boolean statusUpdated = serviceConfigService.updateReceiverServiceList(id, "SENDER_ADDED_NO_CONFIG_SERVICE");
                logger.info("更新接收端配置文件状态结果: {}", statusUpdated);

                // 再次强制重新加载服务列表
                //serviceConfigService.forceReloadServicesList();


                logger.info("服务创建流程完成");

                result.put("success", true);
                result.put("message", "接收端服务配置已创建");
            } else {
                logger.error("接收端服务配置创建失败，ID: " + id);
                result.put("success", false);
                result.put("message", "创建接收端服务配置失败，请检查日志获取更多信息");
            }
        } catch (Exception e) {
            logger.error("创建接收端服务时发生错误", e);
            result.put("success", false);
            result.put("message", "创建接收端服务时发生错误: " + e.getMessage());
        }

        logger.info("createReceiverService处理完成，结果: " + result);
        return result;
    }
}