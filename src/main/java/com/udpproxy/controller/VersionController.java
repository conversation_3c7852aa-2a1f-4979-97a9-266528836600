package com.udpproxy.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.udpproxy.service.UpgradeHistoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/version-info")
public class VersionController {
    private static final Logger logger = LoggerFactory.getLogger(VersionController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @GetMapping("")
    public String versionInfo(Model model, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        Map<String, String> versionInfo = new HashMap<>();
        versionInfo.put("name", "安全隔离单向输出模块");

        // 从配置文件读取版本信息
        try (BufferedReader reader = new BufferedReader(new FileReader("/opt/unimas/version"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("version:")) {
                    versionInfo.put("version", line.substring("version:".length()).trim());
                } else if (line.startsWith("release num:")) {
                    versionInfo.put("releaseNum", line.substring("release num:".length()).trim());
                } else if (line.startsWith("changelist:")) {
                    versionInfo.put("changeList", line.substring("changelist:".length()).trim());
                } else if (line.startsWith("build from")) {
                    versionInfo.put("buildFrom", line.substring("build from".length()).trim());
                }
            }
        } catch (IOException e) {
            versionInfo.put("version", "1.0.0");
            versionInfo.put("releaseNum", "1.0.0");
            versionInfo.put("changeList", "1.0.0");
            versionInfo.put("buildFrom", "1.0.0");
            //versionInfo.put("error", "无法读取版本信息文件");
        }

        model.addAttribute("versionInfo", versionInfo);
        return "version-info";
    }

    /**
     * 获取升级历史接口
     */
    @GetMapping("/upgrade/history")
    @ResponseBody
    public ResponseEntity<List<Map<String, Object>>> getUpgradeHistory(HttpSession session) {
        List<Map<String, Object>> historyList = new ArrayList<>();

        try {
            // 检查是否已登录
            Boolean authenticated = (Boolean) session.getAttribute("authenticated");
            if (authenticated == null || !authenticated) {
                return ResponseEntity.status(401).body(historyList);
            }

            logger.info("获取升级历史信息");

            // 读取升级历史文件
            File historyFile = new File("/var/unimas/patch/history_info.json");
            if (historyFile.exists() && historyFile.length() > 0) {
                try {
                    String content = new String(Files.readAllBytes(historyFile.toPath()));
                    logger.info("升级历史文件内容: {}", content);

                    // 解析JSON数组
                    List<Map<String, Object>> rawHistory = objectMapper.readValue(content,
                            new TypeReference<List<Map<String, Object>>>() {});

                    // 提取需要的字段
                    for (Map<String, Object> item : rawHistory) {
                        Map<String, Object> historyItem = new HashMap<>();
                        historyItem.put("patch_name", item.get("patch_name"));
                        historyItem.put("time", item.get("time"));
                        historyList.add(historyItem);
                    }

                    logger.info("成功解析升级历史，共{}条记录", historyList.size());

                } catch (Exception e) {
                    logger.error("解析升级历史文件失败", e);
                }
            } else {
                logger.info("升级历史文件不存在或为空: /var/unimas/patch/history_info.json");
            }

            return ResponseEntity.ok(historyList);

        } catch (Exception e) {
            logger.error("获取升级历史失败", e);
            return ResponseEntity.status(500).body(historyList);
        }
    }
}