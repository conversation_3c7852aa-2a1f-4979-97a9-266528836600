package com.udpproxy.controller;

import com.udpproxy.service.SystemHealthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/system")
public class SystemController {

    private static final Logger logger = LoggerFactory.getLogger(SystemController.class);

    @Autowired
    private SystemHealthService systemHealthService;

    @GetMapping("/health")
    public String healthCheck(Model model, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        Map<String, Object> healthResult = systemHealthService.checkSystemHealth();
        model.addAttribute("healthResult", healthResult);
        return "system-health";
    }

    @PostMapping("/repair")
    public String repairSystem(RedirectAttributes redirectAttributes, HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            return "redirect:/login";
        }

        boolean repaired = systemHealthService.repairSystem();
        if (repaired) {
            redirectAttributes.addFlashAttribute("success", "系统修复成功");
        } else {
            redirectAttributes.addFlashAttribute("error", "系统修复失败");
        }
        return "redirect:/system/health";
    }

    /**
     * 获取系统资源使用情况API - 每10秒调用一次
     */
    @GetMapping("/api/resource-usage")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getResourceUsage(HttpSession session) {
        // 检查是否已登录
        Boolean authenticated = (Boolean) session.getAttribute("authenticated");
        if (authenticated == null || !authenticated) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "未登录");
            return ResponseEntity.status(401).body(error);
        }

        try {
            Map<String, Object> resourceInfo = systemHealthService.getResourceUsageInfo();
            logger.info("API返回资源使用情况: {}", resourceInfo);
            return ResponseEntity.ok(resourceInfo);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "获取资源信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
}