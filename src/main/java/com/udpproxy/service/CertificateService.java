package com.udpproxy.service;

import com.udpproxy.model.Certificate;
import com.udpproxy.model.CertificateList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.io.FileInputStream;
import java.nio.file.StandardCopyOption;
import com.udpproxy.util.DebugLogger;
import java.nio.ByteBuffer;
import com.example.asn.certmanager.programmingtypes.Uint8;
import com.example.asn.certmanager.cermanagementhttp.CertApplyType;
import com.example.asn.certmanager.cermanagementhttp.CertInquiryRequest;
import com.example.asn.certmanager.cermanagementhttp.CertInquiryResponse;
import com.example.asn.certmanager.cermanagementhttp.MessageRequestFrame;
import com.example.asn.certmanager.cermanagementhttp.MessageResponseFrame;
import com.example.asn.certmanager.Certmanager;
import com.example.asn.certmanager.CertManagerCoerFactory;
import java.util.HashMap;
import java.util.Map;
import com.example.asn.client.HttpClient;
import com.example.asn.certmanager.programmingtypes.CertificateId;
import com.example.asn.certmanager.programmingtypes.SignatureValue;
import com.example.asn.certmanager.programmingtypes.EcsigP256Signature;
import com.oss.asn1.OctetString;
import com.example.asn.certmanager.cermanagementhttp.CertDeleteRequest;
import com.example.asn.certmanager.cermanagementhttp.CertDeleteResponse;
import com.example.asn.certmanager.cermanagementhttp.CertWriteRequest;
import com.example.asn.certmanager.cermanagementhttp.CertWriteResponse;

@Service
public class CertificateService {

    private static final Logger logger = LoggerFactory.getLogger(CertificateService.class);

    @Value("${certificates.dir:./config/certificates}")
    private String certificatesDir;
    
    @Value("${config.dir:./config}")
    private String configDir;
    
    private static final String CERTIFICATES_LIST_FILE = "certificates.xml";
    
    private CertificateList certificateList = new CertificateList();
    private AtomicLong certificateIdSequence = new AtomicLong(1);
    
    private String CERT_STORAGE_DIR; // 证书存储目录

    private static HttpClient client = new HttpClient("http://127.0.0.1:8080");
    
    @PostConstruct
    public void init() {
        // 不再从XML加载证书列表，而是通过证书查询请求获取
    }
    

    private void createCertificatesDirIfNotExists() {
        // 创建完整的证书存储路径
        File dir = new File(certificatesDir);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            logger.info("创建证书存储目录: {} 结果: {}", certificatesDir, created);
            
            if (!created) {
                logger.error("无法创建证书存储目录! 请检查路径权限: {}", certificatesDir);
                // 尝试创建一个备用目录
                try {
                    File tempDir = new File("./cert");
                    if (!tempDir.exists()) {
                        tempDir.mkdirs();
                    }
                    certificatesDir = tempDir.getAbsolutePath();
                    logger.info("已创建备用证书存储目录: {}", certificatesDir);
                } catch (Exception e) {
                    logger.error("创建备用目录也失败: " + e.getMessage(), e);
                }
            }
        } else {
            logger.info("证书存储目录已存在: {}", certificatesDir);
        }
    }
    
    private String getCertificateStorePath() {
        return certificatesDir;
    }
    
    // 加载证书列表
    public void loadCertificatesList() {
        logger.info("不再从XML文件加载证书列表，将通过证书查询请求获取");
    }
    
    // 保存证书列表
    public void saveCertificatesList() {
        try {
            System.out.println("开始保存证书列表到XML文件");
            
            // 确保目录存在
            File dir = new File(configDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 保存证书列表
            File file = new File(configDir + "/certificates.xml");
            
            JAXBContext context = JAXBContext.newInstance(CertificateList.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.marshal(certificateList, file);
            
            System.out.println("证书列表已保存到: " + file.getAbsolutePath());
        } catch (Exception e) {
            System.err.println("保存证书列表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 获取所有证书
    public List<Certificate> getAllCertificates() {
        return new ArrayList<>(certificateList.getCertificates());
    }
    
    // 根据ID获取证书
    public Optional<Certificate> getCertificateById(Long id) {
        return certificateList.getCertificates().stream()
                .filter(cert -> cert.getId().equals(id))
                .findFirst();
    }
    
    // 根据别名获取证书
    public Optional<Certificate> getCertificateByAlias(String alias) {
        if (alias == null) {
            return Optional.empty();
        }
        return certificateList.getCertificates().stream()
                .filter(cert -> alias.equals(cert.getAlias()))
                .findFirst();
    }
    
    // 检查别名是否已存在
    public boolean isAliasExists(String alias) {
        if (alias == null) {
            return false;
        }
        return certificateList.getCertificates().stream()
                .anyMatch(cert -> alias.equals(cert.getAlias()));
    }
    
    /**
     * 处理上传的证书文件 - 增强错误处理
     */
    public Certificate uploadCertificate(String alias, MultipartFile file, String password, String description) {
        try {
            logger.info("处理证书上传，alias: {}, 文件大小: {}, 文件名: {}, 存储目录: {}", 
                       alias, file.getSize(), file.getOriginalFilename(), certificatesDir);
            
            // 确保存储目录存在
            File storageDir = new File(certificatesDir);
            if (!storageDir.exists()) {
                boolean dirCreated = storageDir.mkdirs();
                logger.info("创建证书目录: {} 结果: {}", certificatesDir, dirCreated);
                if (!dirCreated) {
                    logger.error("无法创建证书存储目录: {}", certificatesDir);
                    return null;
                }
            }
            
            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                originalFilename = "cert_" + System.currentTimeMillis();
            }
            
            // 构建新文件名: 别名_证书名
            String targetFileName = alias + "_" + originalFilename;
            File targetFile = new File(storageDir, targetFileName);
            
            logger.info("准备保存证书文件到: {}", targetFile.getAbsolutePath());
            
            // 保存文件
            file.transferTo(targetFile);
            
            if (!targetFile.exists()) {
                logger.error("文件保存失败，目标文件不存在: {}", targetFile.getAbsolutePath());
                return null;
            }
            
            logger.info("证书文件已保存到: {}, 文件大小: {} 字节", targetFile.getAbsolutePath(), targetFile.length());
            
            // 创建证书对象
            Certificate certificate = new Certificate();
            certificate.setId(nextCertificateId());
            certificate.setAlias(alias);
            certificate.setFilename(originalFilename);
            certificate.setStorePath(targetFile.getAbsolutePath());
            certificate.setDescription(description);
            certificate.setUploadTime(LocalDateTime.now());
            
            // 设置为未知类型，不尝试解析证书
            certificate.setType("未知");
            
            // 保存密码（如果有）
            if (password != null && !password.isEmpty()) {
                certificate.setPassword(password);
            }
            
            // 添加到证书列表
            certificateList.getCertificates().add(certificate);
            
            // 保存证书列表到XML文件
            saveCertificatesList();
            
            logger.info("证书已成功导入，ID={}, 别名={}", certificate.getId(), certificate.getAlias());
            return certificate;
        } catch (Exception e) {
            logger.error("证书上传处理失败: " + e.getMessage(), e);
            e.printStackTrace(); // 打印完整堆栈跟踪
            return null;
        }
    }
    
    // 更新证书信息
    public Optional<Certificate> updateCertificate(Long id, Certificate updatedCertificate) {
        Optional<Certificate> certificateOpt = getCertificateById(id);
        
        if (certificateOpt.isPresent()) {
            Certificate certificate = certificateOpt.get();
            
            // 检查别名是否已被其他证书使用
            if (!certificate.getAlias().equals(updatedCertificate.getAlias()) && 
                isAliasExists(updatedCertificate.getAlias())) {
                throw new IllegalArgumentException("证书别名 '" + updatedCertificate.getAlias() + "' 已存在");
            }
            
            // 更新证书属性
            certificate.setAlias(updatedCertificate.getAlias());
            certificate.setDescription(updatedCertificate.getDescription());
            
            // 保存更新
            saveCertificatesList();
            
            return Optional.of(certificate);
        }
        
        return Optional.empty();
    }
    
    // 删除证书
    public boolean deleteCertificate(Long id) {
        Optional<Certificate> certificateOpt = getCertificateById(id);
        
        if (certificateOpt.isPresent()) {
            Certificate certificate = certificateOpt.get();
            logger.info("准备删除证书: ID={}, 别名={}, 存储路径={}", 
                       certificate.getId(), certificate.getAlias(), certificate.getStorePath());
            
            // 删除文件
            boolean fileDeleted = true;
            try {
                if (certificate.getStorePath() != null && !certificate.getStorePath().isEmpty()) {
                    File certFile = new File(certificate.getStorePath());
                    if (certFile.exists()) {
                        fileDeleted = certFile.delete();
                        logger.info("删除证书文件: {} 结果: {}", certificate.getStorePath(), fileDeleted);
                        
                        if (!fileDeleted) {
                            // 尝试使用Files.delete
                            try {
                                Files.delete(certFile.toPath());
                                fileDeleted = true;
                                logger.info("使用Files.delete成功删除证书文件: {}", certificate.getStorePath());
                            } catch (IOException ioe) {
                                logger.warn("Files.delete也无法删除证书文件: {}, 错误: {}", 
                                          certificate.getStorePath(), ioe.getMessage());
                                // 这里我们继续执行，即使文件没删除，也要从列表中移除该证书
                            }
                        }
                    } else {
                        logger.warn("证书文件不存在: {}", certificate.getStorePath());
                    }
                } else {
                    logger.warn("证书没有存储路径: ID={}, 别名={}", certificate.getId(), certificate.getAlias());
                }
            } catch (Exception e) {
                logger.error("删除证书文件异常: " + e.getMessage(), e);
                // 这里我们继续执行，即使文件删除失败，也要尝试从列表中移除该证书
                fileDeleted = false;
            }
            
            // 从列表中移除
            boolean removed = certificateList.getCertificates().removeIf(cert -> cert.getId().equals(id));
            if (removed) {
                saveCertificatesList();
                logger.info("证书已从列表中移除: ID={}, 别名={}", certificate.getId(), certificate.getAlias());
                return true;
            } else {
                logger.warn("无法从列表中移除证书: ID={}, 别名={}", certificate.getId(), certificate.getAlias());
                return false;
            }
        } else {
            logger.warn("要删除的证书不存在: ID={}", id);
            return false;
        }
    }
    
    public Resource getCertificateFile(Long id) throws IOException {
        Optional<Certificate> certOpt = getCertificateById(id);
        if (certOpt.isPresent()) {
            String filePath = certOpt.get().getStorePath();
            if (filePath != null) {
                File file = new File(filePath);
                if (file.exists()) {
                    return new FileSystemResource(file);
                }
            }
        }
        
        throw new IOException("证书文件不存在");
    }
    
    public List<Certificate> searchCertificates(String alias, String type, String validity) {
        return certificateList.getCertificates().stream()
                .filter(cert -> alias == null || alias.isEmpty() || cert.getAlias().contains(alias))
                .filter(cert -> type == null || type.isEmpty() || cert.getType().equals(type))
                .filter(cert -> {
                    if (validity == null || validity.isEmpty()) {
                        return true;
                    }
                    
                    LocalDateTime now = LocalDateTime.now();
                    
                    switch (validity) {
                        case "valid":
                            return now.isBefore(cert.getValidTo());
                        case "expiring":
                            return now.isBefore(cert.getValidTo()) && 
                                   now.isAfter(cert.getValidTo().minusDays(30));
                        case "expired":
                            return now.isAfter(cert.getValidTo());
                        default:
                            return true;
                    }
                })
                .collect(Collectors.toList());
    }
    
    // 添加 nextCertificateId 方法
    private Long nextCertificateId() {
        return certificateIdSequence.getAndIncrement();
    }
    
    // 添加 saveCertificateList 方法 - 这是对现有 saveCertificatesList 方法的别名
    public void saveCertificateList() {
        saveCertificatesList();
    }

    public String getConfigDir() {
        return configDir;
    }

    public String getCertificatesDir() {
        return certificatesDir;
    }

    public String getCertificatesListFilePath() {
        return Paths.get(configDir, CERTIFICATES_LIST_FILE).toString();
    }
    
    /**
     * 通过证书查询请求获取证书信息
     * @return 包含注册证书和应用证书信息的Map
     */
    public Map<String, Certificate> loadCertificatesFromInquiry() {
        Map<String, Certificate> certificates = new HashMap<>();
        
        try {
            logger.info("开始通过证书查询请求获取证书信息...");
            
            // 查询注册证书（code=1）
            Certificate registrationCert = queryCertificateByType(CertApplyType.registerCertType, 1);
            if (registrationCert != null) {
                certificates.put("registrationCert", registrationCert);
                logger.info("成功获取注册证书信息");
            } else {
                logger.warn("未能获取注册证书信息");
            }
            
            // 查询应用证书（code=2）
            Certificate applicationCert = queryCertificateByType(CertApplyType.applicationCertType, 1);
            if (applicationCert != null) {
                certificates.put("applicationCert", applicationCert);
                logger.info("成功获取应用证书信息");
            } else {
                logger.warn("未能获取应用证书信息");
            }
            
            return certificates;
        } catch (Exception e) {
            logger.error("获取证书信息失败: " + e.getMessage(), e);
            return certificates;
        }
    }
    
    /**
     * 根据证书类型和索引查询证书信息
     * 
     * @param certType 证书类型（注册证书/应用证书）
     * @param certIndex 证书索引
     * @return 证书对象，包含需要展示的信息
     */
    private Certificate queryCertificateByType(CertApplyType certType, int certIndex) {
        try {
            logger.info("查询{}证书，索引: {}", 
                      certType == CertApplyType.registerCertType ? "注册" : "应用", 
                      certIndex);
            
            // 创建证书查询请求
            CertInquiryRequest request = new CertInquiryRequest();
            request.setCertType(certType);
            request.setCertIndex(new Uint8(certIndex));
            
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setCertificationQuery(request);
            
            // 编码消息
            byte[] encodedData = CertManagerCoerFactory.encodeMessageRequestFrame(frame);
            
            // 发送请求 - 这里需要实现HTTP客户端发送请求的逻辑
            // 为简化示例，这里假设已经存在一个client对象
            // byte[] response = client.post("/certmng", encodedData);
            
            // 模拟HTTP请求 - 实际应用中需要替换为真实的HTTP请求
            byte[] response = sendCertificateRequest(encodedData);
            
            // 解码响应
            MessageResponseFrame responseFrame = Certmanager.getCOERCoder().decode(
                ByteBuffer.wrap(response), new MessageResponseFrame());
            
            // 判断响应是否包含证书查询结果
            if (responseFrame.getContent().hasCertificationQuery()) {
                CertInquiryResponse certResponse = responseFrame.getContent().getCertificationQuery();
                logger.info("证书查询成功");
                
                if (certResponse.getCert() != null) {
                    // 创建我们自己的证书对象
                    Certificate cert = new Certificate();
                    
                    // 获取ASN.1证书对象
                    com.example.asn.certmanager.programmingtypes.Certificate asnCert = certResponse.getCert();
                    
                    // 设置基本信息
                    cert.setId(System.currentTimeMillis()); // 生成一个唯一ID
                    cert.setType(certType == CertApplyType.registerCertType ? "注册证书" : "应用证书");
                    //cert.setSignatureValue(asnCert.getSignatureValue()==null?null:asnCert.getSignatureValue());
                    // 获取证书标识（ToBeSignedCertificate中id信息）
                    if (asnCert.getToBeSigned() != null && asnCert.getToBeSigned().getId() != null) {
                        //cert.setCertId(asnCert.getToBeSigned().getId());
                        // 根据CertificateId类型获取标识
                        if (asnCert.getToBeSigned().getId().hasName()) {
                            String nameStr = asnCert.getToBeSigned().getId().getName().toString();
                            // 处理名称，去除前缀和引号
                            if (nameStr.contains("::=")) {
                                nameStr = nameStr.substring(nameStr.indexOf("::=") + 4).trim();
                                // 去除所有引号类型
                                if (nameStr.startsWith("\"") && nameStr.endsWith("\"")) {
                                    nameStr = nameStr.substring(1, nameStr.length() - 1);
                                }
                                if (nameStr.startsWith("'") && nameStr.endsWith("'")) {
                                    nameStr = nameStr.substring(1, nameStr.length() - 1);
                                }
                            }
                            cert.setAlias(nameStr);
                        } else if (asnCert.getToBeSigned().getId().hasBinaryId()) {
                            String binaryIdStr = asnCert.getToBeSigned().getId().getBinaryId().toString();
                            // 处理二进制ID，去除前缀和后缀的'H等标识
                            if (binaryIdStr.contains("::=")) {
                                binaryIdStr = binaryIdStr.substring(binaryIdStr.indexOf("::=") + 4).trim();
                               
                                // 去除所有引号类型
                                if (binaryIdStr.startsWith("\"") && binaryIdStr.endsWith("\"")) {
                                    binaryIdStr = binaryIdStr.substring(1, binaryIdStr.length() - 1);
                                }
                                if (binaryIdStr.startsWith("'") && binaryIdStr.endsWith("'")) {
                                    binaryIdStr = binaryIdStr.substring(1, binaryIdStr.length() - 1);
                                }
                            }
                            cert.setAlias(binaryIdStr);
                        } else {
                            cert.setAlias("未知ID");
                        }
                    }
                    
                    // 获取有效期（ToBeSignedCertificate中validityPeriod信息）
                    if (asnCert.getToBeSigned() != null && asnCert.getToBeSigned().getValidityPeriod() != null) {
                        // 从Time32和Duration转换为LocalDateTime
                        try {
                            // Time32类型是从2004年1月1日00:00:00 UTC开始的秒数
                            long startTime = asnCert.getToBeSigned().getValidityPeriod().getStart().longValue();
                            // 2004年1月1日的UTC时间戳
                            long epoch2004 = 1072915200L; // 2004-01-01 00:00:00 UTC
                            // 转换为标准时间戳（从1970年开始）
                            long epochSeconds = epoch2004 + startTime;
                            
                            // 转换为LocalDateTime
                            LocalDateTime validFrom = LocalDateTime.ofInstant(
                                java.time.Instant.ofEpochSecond(epochSeconds),
                                ZoneId.systemDefault());
                            cert.setValidFrom(validFrom);
                            
                            // 获取持续时间
                            long duration = 0;
                            // 根据Duration类型获取持续时间
                            if (asnCert.getToBeSigned().getValidityPeriod().getDuration().hasSeconds()) {
                                duration = asnCert.getToBeSigned().getValidityPeriod().getDuration().getSeconds().longValue();
                            } else if (asnCert.getToBeSigned().getValidityPeriod().getDuration().hasYears()) {
                                duration = asnCert.getToBeSigned().getValidityPeriod().getDuration().getYears().longValue() * 365 * 24 * 60 * 60;
                            }
                            
                            // 计算有效期结束时间
                            LocalDateTime validTo = validFrom.plusSeconds(duration);
                            cert.setValidTo(validTo);
                        } catch (Exception e) {
                            logger.error("解析证书有效期失败: " + e.getMessage(), e);
                        }
                    }
                    
                    // 获取签发者（CertificateBase中的issuer信息）
                    if (asnCert.getIssuer() != null) {
                        StringBuilder issuerStr = new StringBuilder();
                        
                        if (asnCert.getIssuer().hasSelf()) {
                            issuerStr.append("自签名");
                        } else if (asnCert.getIssuer().hasSha256AndDigest()) {
                            String digestValue = asnCert.getIssuer().getSha256AndDigest().toString();
                            
                            // 处理哈希值，去除前缀
                            if (digestValue.contains("::=")) {
                                digestValue = digestValue.substring(digestValue.indexOf("::=") + 4).trim();
                                
                                if (digestValue.startsWith("\"") && digestValue.endsWith("\"")) {
                                    digestValue = digestValue.substring(1, digestValue.length() - 1);
                                }
                            }
                            //去除'H'
                            if (digestValue.endsWith("'H")) {
                                digestValue = digestValue.substring(0, digestValue.length() - 1);
                            }
                            if (digestValue.startsWith("'") && digestValue.endsWith("'")) {
                                digestValue = digestValue.substring(1, digestValue.length() - 1);
                            }
                            issuerStr.append(digestValue);
                        } else if (asnCert.getIssuer().hasSha384AndDigest()) {
                            String digestValue = asnCert.getIssuer().getSha384AndDigest().toString();
                            // 处理哈希值，去除前缀
                            if (digestValue.contains("::=")) {
                                digestValue = digestValue.substring(digestValue.indexOf("::=") + 4).trim();
                                
                                if (digestValue.startsWith("\"") && digestValue.endsWith("\"")) {
                                    digestValue = digestValue.substring(1, digestValue.length() - 1);
                                }
                                
                            }
                            //去除'H'
                            if (digestValue.endsWith("'H")) {
                                digestValue = digestValue.substring(0, digestValue.length() - 1);
                            }
                            if (digestValue.startsWith("'") && digestValue.endsWith("'")) {
                                digestValue = digestValue.substring(1, digestValue.length() - 1);
                            }
                            issuerStr.append(digestValue);
                        } else if (asnCert.getIssuer().hasSm3AndDigest()) {
                            String digestValue = asnCert.getIssuer().getSm3AndDigest().toString();
                            // 处理哈希值，去除前缀
                            if (digestValue.contains("::=")) {
                                digestValue = digestValue.substring(digestValue.indexOf("::=") + 4).trim();
                                
                                if (digestValue.startsWith("\"") && digestValue.endsWith("\"")) {
                                    digestValue = digestValue.substring(1, digestValue.length() - 1);
                                }
                            }
                            //去除'H'
                            if (digestValue.endsWith("'H")) {
                                digestValue = digestValue.substring(0, digestValue.length() - 1);
                            }
                            if (digestValue.startsWith("'") && digestValue.endsWith("'")) {
                                digestValue = digestValue.substring(1, digestValue.length() - 1);
                            }
                            issuerStr.append(digestValue);
                        }
                        
                        cert.setIssuer(issuerStr.toString());
                    }
                    
                    return cert;
                } else {
                    logger.warn("响应中不包含证书内容");
                }
            } else {
                logger.warn("未收到预期的证书查询响应");
            }
        } catch (Exception e) {
            logger.error("证书查询请求失败: " + e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 发送证书请求的模拟实现
     * 在实际应用中，这里应该实现真正的HTTP请求发送逻辑
     */
    private byte[] sendCertificateRequest(byte[] encodedData) {
        // 这里只是模拟实现，实际使用时需要替换为真实的HTTP请求
        // 返回一个空的响应，实际应用中应该返回真实的响应数据
        logger.info("模拟发送证书查询请求，数据大小: {} 字节", encodedData.length);
        
        // 创建一个模拟的响应
        try {
            // 实际应用中，此处应该返回服务器的真实响应
             // 发送请求
            byte[] response = client.post("/certmng", encodedData);
            
            // 返回空的响应
            return response;
        } catch (Exception e) {
            logger.error("创建模拟响应失败: " + e.getMessage(), e);
            return new byte[0];
        }
    }

    /**
     * 删除应用证书
     * @return 删除操作是否成功
     */
    public boolean deleteApplicationCertificate() {
        try {
            logger.info("开始删除应用证书...");
            
            // 查询应用证书（code=2）
            Certificate appCert = queryCertificateByType(CertApplyType.applicationCertType, 1);

            if (appCert == null) {
                logger.warn("未找到应用证书信息，无法删除");
                return false;
            }
            
            logger.info("找到应用证书: {}", appCert.getAlias());
            
            // 创建证书删除请求
            CertDeleteRequest request = new CertDeleteRequest();
            request.setCertType(CertApplyType.applicationCertType); // 指定为应用证书
            //request.setCertId(appCert.getCertId());
            //request.setSignatureValue(appCert.getSignatureValue());
            
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setCertificationDelete(request);
            
            // 编码消息
            byte[] encodedData = CertManagerCoerFactory.encodeMessageRequestFrame(frame);
            
            // 发送请求
            logger.info("正在发送应用证书删除请求...");
            byte[] response = client.post("/certmng", encodedData);
            
            // 解码响应
            MessageResponseFrame responseFrame = CertManagerCoerFactory.decodeMessageResponseFrame(response);
            
            // 处理响应
            if (responseFrame.getContent().hasCertificationDelete()) {
                CertDeleteResponse certResponse = responseFrame.getContent().getCertificationDelete();
                logger.info("应用证书删除成功!");
                return true;
            } else {
                logger.warn("未收到预期的证书删除响应");
                return false;
            }
        } catch (Exception e) {
            logger.error("应用证书删除失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 辅助方法: 十六进制字符串转字节数组
     */
    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                                 + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
    
    /**
     * 辅助方法: 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
    
    /**
     * 辅助方法: 获取证书类型字符串
     */
    private static String getCertTypeString(CertApplyType certType) {
        if (certType == CertApplyType.registerCertType) {
            return "注册证书";
        } else if (certType == CertApplyType.applicationCertType) {
            return "应用证书";
        } else {
            return "未知类型";
        }
    }



    /**
     * 导入应用证书
     * @param file 上传的证书文件
     * @return 导入是否成功
     */
    public boolean importApplicationCertificate(MultipartFile file) {
        try {
            logger.info("开始导入应用证书，文件名: {}, 大小: {} 字节", file.getOriginalFilename(), file.getSize());
            
            // 将MultipartFile转换为字节数组
            byte[] certBytes = file.getBytes();
            
            // 使用CertManagerCoerFactory解码证书字节
            com.example.asn.certmanager.programmingtypes.Certificate cert = null;
            try {
                cert = CertManagerCoerFactory.decodeCertificate(certBytes);
                logger.info("证书解码成功");
            } catch (Exception e) {
                logger.error("证书解码失败: " + e.getMessage(), e);
                return false;
            }
            
            // 创建证书写入请求
            CertWriteRequest request = new CertWriteRequest();
            request.setCertType(CertApplyType.applicationCertType); // 指定为应用证书
            request.setCert(cert);
            
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setCertificationWrite(request);
            
            // 编码消息
            byte[] encodedData = CertManagerCoerFactory.encodeMessageRequestFrame(frame);
            
            // 发送请求
            logger.info("正在发送应用证书写入请求...");
            byte[] response = client.post("/certmng", encodedData);
            
            // 解码响应
            MessageResponseFrame responseFrame = CertManagerCoerFactory.decodeMessageResponseFrame(response);
            
            // 处理响应
            if (responseFrame.getContent().hasCertificationWrite()) {
                CertWriteResponse certResponse = responseFrame.getContent().getCertificationWrite();
                logger.info("应用证书导入成功! 证书索引: {}", certResponse.getCertIndex().intValue());
                return true;
            } else {
                logger.warn("未收到预期的证书写入响应");
                return false;
            }
        } catch (Exception e) {
            logger.error("应用证书导入失败: " + e.getMessage(), e);
            return false;
        }
    }
} 