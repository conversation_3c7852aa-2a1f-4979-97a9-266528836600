package com.udpproxy.service;

import com.udpproxy.model.AuditLog;
import com.udpproxy.model.AuditLogList;
import com.udpproxy.model.UdpService;
import com.udpproxy.model.UdpServiceList;
import com.udpproxy.repository.AuditRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
public class AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditService.class);

    @Value("${config.dir:./config}")
    private String configDir;
    
    private static final String AUDIT_LOGS_FILE = "audit_logs.xml";
    
    @Autowired
    private ServiceConfigurationService serviceConfigurationService;
    
    @Autowired
    private AuditRepository auditRepository;
    
    private AuditLogList auditLogList = new AuditLogList();
    private AtomicLong auditLogIdSequence = new AtomicLong(1);
    
    @PostConstruct
    public void init() {
        // 确保目录路径没有多余的空格
        configDir = configDir.trim();
        createConfigDirIfNotExists();
        loadAuditLogs();
    }
    
    private void createConfigDirIfNotExists() {
        File dir = new File(configDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }
    
    private String getAuditLogsFilePath() {
        return Paths.get(configDir.trim(), AUDIT_LOGS_FILE).toString();
    }
    
    // 加载审计日志
    public void loadAuditLogs() {
        File file = new File(getAuditLogsFilePath());
        if (file.exists()) {
            try {
                JAXBContext context = JAXBContext.newInstance(AuditLogList.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                auditLogList = (AuditLogList) unmarshaller.unmarshal(file);
                
                // 找出最大ID，用于后续生成新ID
                auditLogList.getAuditLogs().stream()
                        .mapToLong(AuditLog::getId)
                        .max()
                        .ifPresent(maxId -> auditLogIdSequence.set(maxId + 1));
            } catch (JAXBException e) {
                logger.error("Failed to load audit logs", e);
                // 如果出错，初始化为空列表
                auditLogList = new AuditLogList();
            }
        } else {
            // 如果文件不存在，初始化为空列表
            auditLogList = new AuditLogList();
        }
    }
    
    // 保存审计日志
    public void saveAuditLogs() {
        try {
            // 确保配置目录存在
            createConfigDirIfNotExists();
            
            // 获取完整的文件路径
            String filePath = getAuditLogsFilePath();
            
            // 创建文件的父目录(以防万一)
            Path path = Paths.get(filePath);
            Files.createDirectories(path.getParent());
            
            // 写入文件
            JAXBContext context = JAXBContext.newInstance(AuditLogList.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.marshal(auditLogList, new File(filePath));
        } catch (Exception e) {
            logger.error("Failed to save audit logs", e);
            throw new RuntimeException("Failed to save audit logs", e);
        }
    }
    
    // 记录审计日志
    public AuditLog logAuditEvent(Long serviceId, String operationType, String details, 
                                  String sourceIp, Integer sourcePort, 
                                  String destinationIp, Integer destinationPort, 
                                  Long dataSize, String username) {
        
        // 检查服务是否启用了审计
        Optional<UdpService> serviceOpt = serviceConfigurationService.getServiceById(serviceId);
        if (serviceOpt.isPresent()) {
            UdpService service = serviceOpt.get();
            if (!"1".equals(service.getAudit())) {
                // 服务未启用审计，不记录日志
                return null;
            }
            
            AuditLog auditLog = new AuditLog();
            auditLog.setId(auditLogIdSequence.getAndIncrement());
            auditLog.setServiceId(serviceId);
            auditLog.setServiceName(service.getName());
            auditLog.setOperationType(operationType);
            auditLog.setDetails(details);
            auditLog.setSourceIp(sourceIp);
            auditLog.setSourcePort(sourcePort);
            auditLog.setDestinationIp(destinationIp);
            auditLog.setDestinationPort(destinationPort);
            auditLog.setDataSize(dataSize);
            auditLog.setUsername(username);
            auditLog.setTimestamp(LocalDateTime.now());
            
            auditLogList.getAuditLogs().add(auditLog);
            saveAuditLogs();
            
            return auditLog;
        }
        
        return null;
    }
    
    // 获取所有审计日志
    public List<AuditLog> getAllAuditLogs() {
        return new ArrayList<>(auditLogList.getAuditLogs());
    }
    
    // 根据服务ID获取审计日志
    public List<AuditLog> getAuditLogsByServiceId(Long serviceId) {
        return auditLogList.getAuditLogs().stream()
                .filter(log -> log.getServiceId().equals(serviceId))
                .collect(Collectors.toList());
    }
    
    // 根据日期范围获取审计日志
    public List<AuditLog> getAuditLogsByDateRange(LocalDateTime from, LocalDateTime to) {
        return auditLogList.getAuditLogs().stream()
                .filter(log -> {
                    LocalDateTime timestamp = log.getTimestamp();
                    return timestamp != null && 
                           (from == null || timestamp.isAfter(from) || timestamp.isEqual(from)) && 
                           (to == null || timestamp.isBefore(to) || timestamp.isEqual(to));
                })
                .collect(Collectors.toList());
    }
    
    // 根据操作类型获取审计日志
    public List<AuditLog> getAuditLogsByOperationType(String operationType) {
        return auditLogList.getAuditLogs().stream()
                .filter(log -> operationType.equals(log.getOperationType()))
                .collect(Collectors.toList());
    }
    
    // 查询审计日志，支持分页
    public Page<AuditLog> searchAuditLogs(Long serviceId, String clientIp, 
                                         LocalDateTime beginTime, LocalDateTime endTime, 
                                         int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<AuditLog> result = auditRepository.findBySearchCriteria(
                serviceId, clientIp, beginTime, endTime, pageable);
        
        // 为每个审计记录设置服务名称
        enrichAuditLogsWithServiceNames(result.getContent());
        
        return result;
    }
    
    // 获取所有服务ID和名称的映射
    public List<Map<String, Object>> getAllServiceNamesForAudit() {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 使用ServiceConfigurationService获取所有服务信息
        List<UdpServiceList.UdpServiceInfo> services = serviceConfigurationService.getAllServicesInfo();
        
        for (UdpServiceList.UdpServiceInfo service : services) {
            Map<String, Object> serviceMap = new HashMap<>();
            serviceMap.put("name", service.getName());
            serviceMap.put("id", service.getId());
            result.add(serviceMap);
        }
        
        return result;
    }
    
    // 为审计日志添加服务名称
    private void enrichAuditLogsWithServiceNames(List<AuditLog> auditLogs) {
        if (auditLogs == null || auditLogs.isEmpty()) {
            return;
        }
        
        // 收集所有需要查询的服务ID
        Set<Long> serviceIds = new HashSet<>();
        for (AuditLog log : auditLogs) {
            if (log.getServiceId() != null) {
                serviceIds.add(log.getServiceId());
            }
        }
        
        // 获取服务名称
        Map<Long, String> serviceNames = new HashMap<>();
        for (Long serviceId : serviceIds) {
            Optional<UdpServiceList.UdpServiceInfo> service = serviceConfigurationService.getServiceInfoById(serviceId);
            service.ifPresent(s -> serviceNames.put(serviceId, s.getName()));
        }
        
        // 设置服务名称
        for (AuditLog log : auditLogs) {
            if (log.getServiceId() != null) {
                log.setServiceName(serviceNames.getOrDefault(log.getServiceId(), "未知服务"));
            }
        }
    }
} 