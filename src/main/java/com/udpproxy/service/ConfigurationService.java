package com.udpproxy.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.udpproxy.model.UdpResource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
public class ConfigurationService {

    @Value("${config.dir:./config}")
    private String configDir;
    
    private static final String RESOURCES_FILE = "udp-resources.json";
    
    private final ObjectMapper objectMapper;
    private List<UdpResource> resources = new ArrayList<>();
    private AtomicLong resourceIdSequence = new AtomicLong(1);
    
    public ConfigurationService() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }
    
    @PostConstruct
    public void init() {
        createConfigDirIfNotExists();
        loadResources();
    }
    
    private void createConfigDirIfNotExists() {
        File dir = new File(configDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }
    
    private String getResourcesFilePath() {
        // 去除可能的前后空格
        String cleanConfigDir = configDir.trim();
        return Paths.get(cleanConfigDir, RESOURCES_FILE).toString();
    }
    
    // 加载UDP资源配置
    public void loadResources() {
        File file = new File(getResourcesFilePath());
        if (file.exists()) {
            try {
                resources = objectMapper.readValue(file, new TypeReference<List<UdpResource>>() {});
                // 找出最大ID，用于后续生成新ID
                resources.stream()
                        .mapToLong(UdpResource::getId)
                        .max()
                        .ifPresent(maxId -> resourceIdSequence.set(maxId + 1));
            } catch (IOException e) {
                e.printStackTrace();
                // 如果出错，初始化为空列表
                resources = new ArrayList<>();
            }
        } else {
            // 如果文件不存在，初始化为空列表
            resources = new ArrayList<>();
        }
    }
    
    // 保存UDP资源配置
    public void saveResources() {
        try {
            // 确保配置目录存在
            createConfigDirIfNotExists();
            
            // 获取完整的文件路径
            String filePath = getResourcesFilePath();
            
            // 创建文件的父目录(以防万一)
            Path path = Paths.get(filePath);
            Files.createDirectories(path.getParent());
            
            // 写入文件
            objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValue(new File(filePath), resources);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("保存UDP资源配置失败", e);
        }
    }
    
    // 获取所有UDP资源
    public List<UdpResource> getAllResources() {
        return new ArrayList<>(resources);
    }
    
    // 根据ID获取UDP资源
    public Optional<UdpResource> getResourceById(Long id) {
        return resources.stream()
                .filter(resource -> resource.getId().equals(id))
                .findFirst();
    }
    
    // 添加UDP资源
    public UdpResource addResource(UdpResource resource) {
        // 设置ID、创建时间和更新时间
        resource.setId(resourceIdSequence.getAndIncrement());
        LocalDateTime now = LocalDateTime.now();
        resource.setCreatedAt(now);
        resource.setUpdatedAt(now);
        
        resources.add(resource);
        saveResources();
        return resource;
    }
    
    // 更新UDP资源
    public Optional<UdpResource> updateResource(Long id, UdpResource updated) {
        Optional<UdpResource> resourceOpt = getResourceById(id);
        
        if (resourceOpt.isPresent()) {
            UdpResource resource = resourceOpt.get();
            
            // 更新资源属性
            resource.setName(updated.getName());
            resource.setSegmentType(updated.getSegmentType());
            resource.setIpRanges(updated.getIpRanges());
            resource.setDescription(updated.getDescription());
            resource.setUpdatedAt(LocalDateTime.now());
            
            saveResources();
            return Optional.of(resource);
        }
        
        return Optional.empty();
    }
    
    // 删除UDP资源
    public boolean deleteResource(Long id) {
        boolean removed = resources.removeIf(resource -> resource.getId().equals(id));
        if (removed) {
            saveResources();
        }
        return removed;
    }
    
    // 根据名称搜索UDP资源
    public List<UdpResource> searchResourcesByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return getAllResources();
        }
        
        String searchTerm = name.toLowerCase();
        return resources.stream()
                .filter(resource -> resource.getName().toLowerCase().contains(searchTerm))
                .collect(Collectors.toList());
    }
    
    // 根据IP范围搜索UDP资源
    public List<UdpResource> searchResourcesByIpRange(String ipRange) {
        if (ipRange == null || ipRange.trim().isEmpty()) {
            return getAllResources();
        }
        
        String searchTerm = ipRange.toLowerCase();
        return resources.stream()
                .filter(resource -> resource.getSegmentType().equals("specific") &&
                        resource.getIpRanges().stream()
                                .anyMatch(range -> (range.getStartIp() + "-" + range.getEndIp()).toLowerCase().contains(searchTerm)))
                .collect(Collectors.toList());
    }
} 