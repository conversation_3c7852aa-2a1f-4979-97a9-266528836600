package com.udpproxy.service;

import com.udpproxy.model.UdpResource;
import com.udpproxy.model.UdpService;
import com.udpproxy.model.UdpServiceList;
import com.udpproxy.model.UdpServiceList.UdpServiceInfo;
import com.udpproxy.util.*;
import com.udpproxy.sm.*;
import com.udpproxy.sm.Command;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Objects;
import java.util.HashSet;
import java.util.Set;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;

// 添加必要的导入
// import com.example.asn.codec.OssServAdapter;
// import com.example.asn.servicemanager.servicemanagementhttp.*;
// import com.example.asn.util.IPUtil;
// import com.example.asn.client.*;
// import com.example.asn.logic.ServiceConfigLogic;
@Service
public class ServiceConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceConfigurationService.class);
    //private static HttpClient client = new HttpClient("http://127.0.0.1:8080");

    private String configDir = Constant.SERVICE_PRE_PATH;

    private static final String SERVICES_LIST_FILE = "config.xml";

    private String receiverConfigDir = Constant.SERVICE_PRE_PATH;

    private String srcappsDir = Constant.SERVICE_PRE_PATH+"srcapps";

    @Autowired
    private ConfigurationService resourceConfigService;

    private UdpServiceList servicesList = new UdpServiceList();
    private AtomicLong serviceIdSequence = new AtomicLong(11);

    private Integer network = 1;

    private static final int port = 20560;

    public void setNetwork(Integer network) {
        this.network = network;
    }

    public Integer getNetwork(){
        return network;
    }

    @PostConstruct
    public void init() {
        createConfigDirIfNotExists();

        // 确保接收端配置目录存在
        File receiverDir = new File(receiverConfigDir);
        if (!receiverDir.exists()) {
            receiverDir.mkdirs();
        }

        loadServicesList();
        // 从配置文件读取network值
        try {
            //File configFile = new File("config/cfcard");
            File configFile = new File("/boot/cfcard");
            if (configFile.exists()) {
                Properties props = new Properties();
                try (FileInputStream fis = new FileInputStream(configFile)) {
                    props.load(fis);
                    String networkStr = props.getProperty("network", "0");
                    this.setNetwork(Integer.parseInt(networkStr));
                }
            }
        } catch (Exception e) {
            logger.error("读取network配置失败", e);
            this.setNetwork(1); // 默认设置为发送端
        }
    }

    private void createConfigDirIfNotExists() {
        File dir = new File(configDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    private String getServicesListFilePath() {
        return Paths.get(configDir.trim(), SERVICES_LIST_FILE).toString();
    }

    /**
     * 获取接收端服务列表文件路径（提供给外部使用的公开方法）
     * @return 接收端服务列表文件路径
     */
    public String getReceiverServicesListFilePath() {
        return Paths.get(receiverConfigDir.trim(), SERVICES_LIST_FILE).toString();
    }

    private String getServiceFilePath(Long serviceId) {
        if (network != null && network == 1) {
                // 接收端服务配置路径
                return Paths.get(receiverConfigDir.trim(), serviceId + ".xml").toString();
        }
        // 默认使用发送端配置路径
        return Paths.get(configDir.trim(), serviceId + ".xml").toString();
    }

    /**
     * 加载服务列表
     */
    public void loadServicesList() {
        System.out.println("开始加载服务列表...");
        // File file = new File(getServicesListFilePath());
        // if(network!=null && network==1){
        //     file = new File(getReceiverServicesListFilePath());
        // }
        // 清空现有服务列表，但仅清空内存中的列表，不删除文件
        servicesList.getServices().clear();

        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        UdpServiceList serList = configXmlOperator.parseServicesToUdpServiceList();
        servicesList.getServices().addAll(serList.getServices());

        // 循环获取每个服务的运行状态
        for (UdpServiceInfo service : servicesList.getServices()) {
            try {
                // 检查服务进程是否在运行
                boolean isRunning = isServiceRunning(service.getId());

                // 更新服务状态
                if (isRunning) {
                    service.setStatus("running");
                } else if ("running".equals(service.getStatus())) {
                    // 如果服务标记为运行但实际未运行，更正状态
                    service.setStatus("deployed");
                }

                DebugLogger.log("服务 " + service.getId() + " (" + service.getName() + ") 状态: " + service.getStatus());
            } catch (Exception e) {
                DebugLogger.error("检查服务 " + service.getId() + " 状态时出错", e);
                // 出错时将状态设为configured
                service.setStatus("configured");
            }
        }


        // // 加载服务列表
        // if (file.exists()) {
        //     try {
        //         JAXBContext context = JAXBContext.newInstance(UdpServiceList.class);
        //         Unmarshaller unmarshaller = context.createUnmarshaller();
        //         UdpServiceList senderList = (UdpServiceList) unmarshaller.unmarshal(file);

        //         if (senderList != null && senderList.getServices() != null) {
        //             System.out.println("从配置文件加载了 " + senderList.getServices().size() + " 个服务");

        //             // 将所有发送端服务添加到服务列表中
        //             for (UdpServiceInfo service : senderList.getServices()) {
        //                 // 确保网络类型正确设置为发送端
        //                 if (service.getNetwork() == null) {
        //                     service.setNetwork(network);
        //                 }
        //                 servicesList.getServices().add(service);
        //             }
        //         }
        //     } catch (JAXBException e) {
        //         System.err.println("从配置文件加载服务列表失败: " + e.getMessage());
        //         e.printStackTrace();
        //     }
        // } else {
        //     System.out.println("配置文件不存在，将创建新的服务列表");
        // }

        // // 更新服务ID序列
        // updateServiceIdSequence();

        System.out.println("服务列表加载完成，共 " + servicesList.getServices().size() + " 个服务");
    }

    /**
     * 根据命令获取服务运行状态
     * 通过执行shell命令检查指定服务ID的进程是否在运行
     * @param id 服务ID
     * @return 如果服务正在运行返回true，否则返回false
     */
    public boolean isServiceRunning(Long id) {
        try {
            String serviceId = id.toString();
            // 执行shell命令检查服务是否运行
            Process process = Runtime.getRuntime().exec(
                new String[] { "/bin/sh", "-c",
                "ps -fe |grep udpProxy |awk 'BEGIN{oj=0}{if($9==\"" + serviceId +
                "\"){oj=1}}END{if(oj==1){print \"true\"}else{print \"false\"}}'"
            });

            // 读取命令输出结果
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = reader.readLine();
            process.waitFor();
            reader.close();

            // 根据输出结果判断服务是否运行
            return line != null && line.trim().equals("true");
        } catch (Exception e) {
            logger.error("检查服务运行状态失败", e);
            return false;
        }
    }

    private void updateServiceIdSequence() {
        if (!servicesList.getServices().isEmpty()) {
            servicesList.getServices().stream()
                    .mapToLong(UdpServiceInfo::getId)
                    .max()
                    .ifPresent(maxId -> serviceIdSequence.set(maxId + 1));
        }
    }

    // 从指定路径加载服务列表
    private void loadServicesFromPath(String filePath) {
        System.out.println("开始从路径加载服务列表: " + filePath);
        File file = new File(filePath);
        if (file.exists()) {
            try {
                JAXBContext context = JAXBContext.newInstance(UdpServiceList.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                UdpServiceList loadedList = (UdpServiceList) unmarshaller.unmarshal(file);

                // 将加载的服务添加到总列表中，避免重复添加
                if (loadedList != null && loadedList.getServices() != null) {
                    System.out.println("从 " + filePath + " 加载了 " + loadedList.getServices().size() + " 个服务");


                    for (UdpServiceInfo service : loadedList.getServices()) {

                        // 检查ID是否已存在，避免重复
                        boolean exists = servicesList.getServices().stream()
                                .anyMatch(s -> s.getId().equals(service.getId()));
                        if (!exists) {
                            System.out.println("添加服务 ID=" + service.getId() +
                                ", 名称=" + service.getName() +
                                ", 网络类型=" + (service.getNetwork() == 1 ? "接收端" : "发送端"));
                            servicesList.getServices().add(service);
                        } else {
                            System.out.println("跳过重复服务 ID=" + service.getId() + ", 名称=" + service.getName());
                        }
                    }
                }
            } catch (JAXBException e) {
                logger.error("加载服务列表失败: " + filePath, e);
                e.printStackTrace(); // 打印详细堆栈跟踪
            }
        } else {
            System.out.println("配置文件不存在: " + filePath);
        }
    }

    /**
     * 保存服务列表到XML文件
     */
    public void saveServicesList() {
        try {
            // 检查目录是否存在
            createConfigDirIfNotExists();
            System.out.println("开始保存服务列表到XML文件, 网络类型: " + (network == 0 ? "发送端" : "接收端"));
            if(network==0){
                List<UdpServiceInfo> senderServices = servicesList.getServices().stream()
                    .collect(Collectors.toList());
                File senderFile = new File(getServicesListFilePath());
                saveServicesToFile(senderServices, senderFile);
            }else{
                 // 确保接收端配置目录存在
                File receiverDir = new File(receiverConfigDir);
                if (!receiverDir.exists()) {
                    receiverDir.mkdirs();
                }
                // 分离发送端和接收端服务

                List<UdpServiceInfo> receiverServices = servicesList.getServices().stream()
                        .collect(Collectors.toList());
                //打印接收端服务信息
                System.out.println("接收端服务列表包含 " + receiverServices.size() + " 个服务");
                for (UdpServiceInfo service : servicesList.getServices()) {
                    System.out.println("接收端服务: ID=" + service.getId() +
                        ", 名称=" + service.getName() +
                        ", 状态=" + service.getStatus() +
                        ", network=" + service.getNetwork() +
                        ", 同步状态=" + service.getSyncStatus());
                }
                File receiverFile = new File(getReceiverServicesListFilePath());

                // 使用新的XML操作类保存接收端服务列表
                try {
                    // 如果接收端文件已存在，尝试合并而不是覆盖
                    if (receiverFile.exists()) {
                        // 读取现有接收端服务列表
                        ConfigXmlOperator xmlOperator = new ConfigXmlOperator(receiverFile.getPath());
                        UdpServiceList existingReceiverList = xmlOperator.parseServicesToUdpServiceList();

                        if (existingReceiverList != null && existingReceiverList.getServices() != null) {
                            // 合并服务列表 - 新的服务和更新的服务会覆盖旧服务
                            Map<Long, UdpServiceInfo> existingServicesMap = existingReceiverList.getServices().stream()
                                    .collect(Collectors.toMap(UdpServiceInfo::getId, s -> s, (s1, s2) -> s2));

                            // 更新现有服务或添加新服务
                            for (UdpServiceInfo service : receiverServices) {
                                existingServicesMap.put(service.getId(), service);
                            }

                            // 转换回列表
                            List<UdpServiceInfo> mergedServices = new ArrayList<>(existingServicesMap.values());

                            // 创建新的合并列表
                            UdpServiceList mergedList = new UdpServiceList();
                            mergedList.setServices(mergedServices);

                            // 保存合并后的列表
                            xmlOperator.saveServiceList(mergedList);

                            System.out.println("接收端服务列表已保存(合并模式): " + receiverFile.getAbsolutePath() +
                                    ", 总服务数: " + mergedServices.size());
                        } else {
                            // 如果读取失败或为空，直接保存当前列表
                            ConfigXmlOperator newOperator = new ConfigXmlOperator(receiverFile.getPath());
                            UdpServiceList newList = new UdpServiceList();
                            newList.setServices(receiverServices);
                            newOperator.saveServiceList(newList);
                        }
                    } else {
                        // 如果文件不存在，直接保存
                        ConfigXmlOperator newOperator = new ConfigXmlOperator(receiverFile.getPath());
                        UdpServiceList newList = new UdpServiceList();
                        newList.setServices(receiverServices);
                        newOperator.saveServiceList(newList);
                    }
                } catch (Exception e) {
                    System.err.println("使用新XML操作类保存接收端服务列表失败，将使用传统方式: " + e.getMessage());
                    saveServicesToFile(receiverServices, receiverFile);
                }
            }

            System.out.println("服务列表已保存完成");
        } catch (Exception e) {
            System.err.println("保存服务列表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


    // 保存服务列表到文件
    private void saveServicesToFile(List<UdpServiceInfo> services, File file) throws JAXBException {
        try {
            // 创建临时服务列表对象
            UdpServiceList tempList = new UdpServiceList();
            tempList.setServices(services);

            // 使用ServiceXmlOperator保存服务列表
            ConfigXmlOperator xmlOperator = new ConfigXmlOperator(file.getPath());
            boolean success = xmlOperator.saveServiceList(tempList);

            if (success) {
                System.out.println("服务列表已成功保存到: " + file.getAbsolutePath());
            } else {
                System.err.println("保存服务列表失败");

            }
        } catch (Exception e) {
            System.err.println("保存服务列表时发生错误: " + e.getMessage());
        }
    }

    // 加载单个服务配置
    public Optional<UdpService> loadSendServiceConfig(Long serviceId) {
        // 首先检查是否存在于发送端或接收端服务列表中
        Optional<UdpServiceInfo> senderServiceOpt = getSenderServiceInfoById(serviceId);

        // 确定服务类型
        boolean isSenderService = senderServiceOpt.isPresent();

        DebugLogger.log("loadServiceConfig - 服务ID=" + serviceId +
                ", 是发送端: " + isSenderService);

        File file = new File(getServiceFilePath(serviceId));
        if (file.exists()) {
            try {
                JAXBContext context = JAXBContext.newInstance(UdpService.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                UdpService service = (UdpService) unmarshaller.unmarshal(file);

                return Optional.of(service);
            } catch (JAXBException e) {
                e.printStackTrace();
            }
        }
        return Optional.empty();
    }

    // 加载单个服务配置
    public Optional<UdpService> loadServiceConfig(Long serviceId) {
        ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId.toString());
        UdpService service = serviceXmlOperator.loadUdpService();
        // 打印服务信息
        if (service != null) {
            logger.info("加载服务配置 - 服务ID={}, 名称={}, 网络类型={}",
                    service.getId(), service.getName(), service.getNetwork());
            return Optional.of(service);
        } else {
            logger.warn("未能加载服务配置，serviceId={}", serviceId);
            return Optional.empty();
        }
    }


    // 获取所有服务信息
    public List<UdpServiceInfo> getAllServicesInfo() {
        System.out.println("获取所有服务信息，当前服务列表大小: " + servicesList.getServices().size());

        // 统计发送端和接收端服务数量
        long senderCount = servicesList.getServices().stream()
                .filter(s -> s.getNetwork() == null || s.getNetwork() == 0)
                .count();
        long receiverCount = servicesList.getServices().stream()
                .filter(s -> s.getNetwork() != null && s.getNetwork() == 1)
                .count();

        System.out.println("服务统计 - 总数: " + servicesList.getServices().size() +
                ", 发送端: " + senderCount + ", 接收端: " + receiverCount);

        // 打印每个服务的基本信息
        for (UdpServiceInfo service : servicesList.getServices()) {
            System.out.println("服务: ID=" + service.getId() +
                    ", 名称=" + service.getName() +
                    ", 网络类型=" + service.getNetwork() +
                    ", 状态=" + service.getStatus());

            //handleServiceStatusQuery(service);
        }

        return new ArrayList<>(servicesList.getServices());
    }

    //  /**
    //  * 处理服务状态查询请求
    //  */
    // private void handleServiceStatusQuery(UdpServiceInfo service) {
    //     try {
    //         // 获取服务ID
    //         String serviceId = service.getId().toString();

    //         // 创建服务状态查询请求
    //         ServiceStatusQueryRequest queryRequest = new ServiceStatusQueryRequest();
    //         queryRequest.setMessageType(ContentMessageType.queryWorkStatus);
    //         queryRequest.setServiceId(new ServiceId(Short.parseShort(serviceId)));

    //         // 发送请求并处理响应
    //         // 创建消息帧
    //         MessageRequestFrame frame = new MessageRequestFrame();
    //         frame.setVersion(new Uint8(1));
    //         frame.setContent(new MessageRequestFrame.Content());
    //         frame.getContent().setServiceStatusQueryRequest(queryRequest);
    //         MessageResponseFrame responseFrame = sendRequest(frame);

    //         // 输出响应信息
    //         if (responseFrame.getContent().hasServiceStatusQueryResponse()) {
    //             ServiceStatusQueryResponse response = responseFrame.getContent().getServiceStatusQueryResponse();
    //             logger.info("服务状态查询成功");
    //             logger.info("服务ID: " + response.getServiceId());

    //             ServiceStatus status = response.getServiceStatus();
    //             String statusDesc = "未知";
    //             if (status == ServiceStatus.running) {
    //                 statusDesc = "运行中";
    //                 service.setStatus("running");
    //             } else if (status == ServiceStatus.stopped) {
    //                 statusDesc = "已停止";
    //                 if("running".equals(service.getStatus())){//改为停止
    //                     service.setStatus("deployed");
    //                 }
    //             }
    //             logger.info("服务状态: " + statusDesc);
    //         } else if (responseFrame.getContent().hasError()) {
    //             ErrorResponse errorResponse = responseFrame.getContent().getError();
    //             logger.info("请求失败: " + errorResponse.getErrorState());
    //         }
    //     } catch (Exception e) {
    //        logger.info("服务["+service.getId()+"]状态查询失败: " + e.getMessage());
    //        if("running".equals(service.getStatus())){//改为停止
    //             service.setStatus("deployed");
    //        }
    //     }
    // }

    /**
     * 专门获取接收端服务信息
     * 该方法直接从接收端配置文件加载服务，确保获取最新的接收端服务信息
     */
    public List<UdpServiceInfo> getReceiverServicesInfo() {
        String receiverFilePath = getReceiverServicesListFilePath();
        File receiverFile = new File(receiverFilePath);

        List<UdpServiceInfo> receiverServices = new ArrayList<>();

        if (receiverFile.exists()) {
            try {
                DebugLogger.log("开始从接收端配置文件加载服务: " + receiverFilePath);

                // 使用ConfigXmlOperator加载接收端服务列表
                ConfigXmlOperator receiverConfigXmlOperator = new ConfigXmlOperator(receiverFilePath);
                UdpServiceList receiverList = receiverConfigXmlOperator.parseServicesToUdpServiceList();

                if (receiverList != null && receiverList.getServices() != null) {
                    DebugLogger.log("直接从接收端配置文件加载了 " + receiverList.getServices().size() + " 个服务");

                    for (UdpServiceInfo service : receiverList.getServices()) {
                        // 确保网络类型正确设置为接收端
                        if (service.getNetwork() == null) {
                            service.setNetwork(1);
                            DebugLogger.log("接收端服务网络类型为null，已自动设置为1: ID=" + service.getId());
                        }

                        // 打印服务信息
                        DebugLogger.log("接收端服务: ID=" + service.getId() +
                                ", 名称=" + service.getName() +
                                ", 网络类型=" + service.getNetwork() +
                                ", 状态=" + service.getStatus() +
                                ", 同步状态=" + service.getSyncStatus());

                        receiverServices.add(service);
                    }
                } else {
                    DebugLogger.log("接收端配置文件不包含有效服务列表");
                }
            } catch (Exception e) {
                logger.error("加载接收端服务列表失败: " + receiverFilePath, e);
                DebugLogger.error("解析接收端配置文件失败", e);
                // 尝试使用备份方法获取接收端服务
                receiverServices = getReceiverServicesFromMemory();
            }
        } else {
            DebugLogger.log("接收端配置文件不存在: " + receiverFilePath);
        }

        DebugLogger.log("最终返回接收端服务数量: " + receiverServices.size());
        return receiverServices;
    }

    /**
     * 从内存中获取接收端服务（作为备份方法）
     */
    private List<UdpServiceInfo> getReceiverServicesFromMemory() {
        DebugLogger.log("尝试从内存中检索接收端服务...");
        List<UdpServiceInfo> receiverServices = new ArrayList<>();

        if (servicesList != null && servicesList.getServices() != null) {
            for (UdpServiceInfo service : servicesList.getServices()) {
                if (service.getNetwork() != null && service.getNetwork() == 1) {
                    receiverServices.add(service);
                }
            }
        }

        DebugLogger.log("从内存中检索到 " + receiverServices.size() + " 个接收端服务");
        return receiverServices;
    }

    // 获取单个服务信息
    public Optional<UdpServiceInfo> getServiceInfoById(Long id) {
        return servicesList.getServices().stream()
                .filter(service -> service.getId().equals(id))
                .findFirst();
    }

    // 获取单个服务完整配置
    public Optional<UdpService> getServiceById(Long id) {
        return loadServiceConfig(id);
    }

    // 获取单个发送端服务完整配置
    public Optional<UdpService> getSendServiceById(Long id) {
        return loadSendServiceConfig(id);
    }

    // 添加获取所有服务的方法
    public List<UdpService> getAllServices() {
        // 读取所有服务配置并返回
        return servicesList.getServices().stream()
                .map(info -> {
                    Long serviceId = info.getId();
                    return getServiceById(serviceId).orElse(null);
                })
                .filter(service -> service != null)
                .collect(Collectors.toList());
    }

    // 修改isServiceNameExists方法，使其只接收服务名称参数
    public boolean isServiceNameExists(String name) {
        return isServiceNameExists(name, null);
    }

    // 保留现有的isServiceNameExists方法，用于检查服务更新时名称是否冲突
    public boolean isServiceNameExists(String name, Long excludeId) {
        // 检查服务名称是否已存在
        return servicesList.getServices().stream()
                .filter(info -> info.getName().equals(name) &&
                        (excludeId == null || !info.getId().equals(excludeId)))
                .findAny()
                .isPresent();
    }

    private Map<String,String> builderConfigNode(UdpService service){
        Map<String,String> map = new HashMap<>();
        map.put("creator","unimas");
        map.put("isAudit","true");
        map.put("templateid","");
        map.put("type","11");
        map.put("importServiceId","");
        map.put("servicetype","udp");
        map.put("configedtime","1");
        map.put("displayname",service.getName());
        map.put("secstate","1");
        map.put("isRun","false");
        map.put("istemplate","false");
        map.put("seclevel","4");
        map.put("flowlevel","10");
        map.put("network",service.getNetwork().toString()); //网络类型
        map.put("status",service.getStatus()); //服务状态
        //map.put("syncConfig",service.getSyncConfig().toString()); //同步状态

        //日志信息打印
        logger.info("builderConfigNode: " + map);
        return map;
    }


    /**
     * 添加服务
     * @param service 要添加的服务对象
     * @return 添加成功后的服务对象
     * @throws IllegalArgumentException 如果服务名称为空或服务创建失败
     * @throws RuntimeException 如果配置文件操作过程中发生异常
     */
    public UdpService addService(UdpService service) {

        // 验证服务名称
        if (service.getName() == null || service.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("服务名称不能为空");
        }
        //增加日志打印

        //从config.xml中加载服务列表
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        logger.info("添加服务: " + service.getName());
        int serviceId = 0;
        try {
            if(!configXmlOperator.isDisplayNameExist(service.getName())) {
                //保存config.xml
                // 设置服务状态为"已配置"
                service.setStatus("configured");
                serviceId = configXmlOperator.add(builderConfigNode(service));
            }
            if(serviceId==0 || "error".equals(serviceId)){
                 throw new IllegalArgumentException("服务号创建失败！");
            }
        } catch (Exception e) {
            logger.error("创建服务配置时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("创建服务配置失败: " + e.getMessage(), e);
        }
        logger.info("添加服务: " + service.getName()+"  服务号："+serviceId);
         //保存service.xml
        ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId+"");
        service.setId(Long.valueOf(serviceId));
        serviceXmlOperator.saveUdpService(service);

        // 如果是发送端服务，同步配置到srcapps目录
        if (service.getNetwork() == null || service.getNetwork() == 0) {
            sendConfigToSrcapps(service.getId());
        }

        return service;
    }

    // public String sendServiceConfigRequest(UdpService service, ContentMessageType messageType) {
    //     String serId = "";
    //     try {
    //          // 创建服务配置请求
    //          ServiceConfigRequest configRequest = new ServiceConfigRequest();
    //          String str = "";
    //          if(messageType == ContentMessageType.addService){
    //             // 创建添加服务请求
    //             str = "创建";
    //             AddServiceRequest addRequest = new AddServiceRequest();
    //             addRequest.setMessageType(messageType);
    //             addRequest.setDisplayname(new DisplayName(service.getName().getBytes()));
    //             // 正确处理网络类型转换
    //             Network networkType = service.getNetwork() != null && service.getNetwork() == 1 ?
    //                                 Network.receiver : Network.sender;
    //             addRequest.setNetwork(networkType);

    //             if (networkType == Network.sender) {
    //                 // 发送端特有字段
    //                 String proxyIp = service.getProxyIp();
    //                 logger.info("请输入代理IP: " + proxyIp);
    //                 addRequest.setProxyIp(IPUtil.createIPAddress(proxyIp));

    //                 // 正确处理整数类型转换
    //                 Integer proxyPort = service.getProxyPort();
    //                 logger.info("请输入代理端口: " + proxyPort);
    //                 addRequest.setProxyPort(new PortNumber(proxyPort != null ? proxyPort : 0));

    //                 // 设置关键字检测位图
    //                 String keyCheckHex = service.getVehiclePlateCheck().toString()+service.getIdCardCheck().toString();
    //                 logger.info("请输入内容关键字检测位图值 (十六进制，例如 03): " + keyCheckHex);
    //                 byte[] contentKeyCheck = new byte[1];
    //                 contentKeyCheck[0] = (byte) Integer.parseInt(keyCheckHex, 16);
    //                 addRequest.setContentKeyCheck(new ContentKeyCheck(contentKeyCheck));

    //                 // 设置协议过滤位图
    //                 String protocolFilterHex = service.getCrc16FormatCheck().toString()+service.getAsnFormatCheck().toString();
    //                 logger.info("请输入协议过滤位图值 (十六进制，例如 03): " + protocolFilterHex);
    //                 byte[] protocolFilter = new byte[1];
    //                 protocolFilter[0] = (byte) Integer.parseInt(protocolFilterHex, 16);
    //                 addRequest.setProtocolFilter(new ProtocolFilter(protocolFilter));

    //                 // 设置告警后处理
    //                 logger.info("请选择未通过处理方式:");
    //                 logger.info("0. ALLOW");
    //                 logger.info("1. DENY");

    //                 // 处理告警处理方式
    //                 PermissionState unpassDeal;
    //                 if (service.getUnpassDeal() != null) {
    //                     if ("deny".equalsIgnoreCase(service.getUnpassDeal())) {
    //                         unpassDeal = PermissionState.forbidden;
    //                         logger.info("请输入选择: 1");
    //                     } else {
    //                         unpassDeal = PermissionState.allow;
    //                         logger.info("请输入选择: 0");
    //                     }
    //                 } else {
    //                     unpassDeal = PermissionState.allow;
    //                     logger.info("请输入选择: 0");
    //                 }
    //                 addRequest.setUnpassDeal(unpassDeal);
    //                 } else {
    //                     // 接收端特有字段
    //                     String serverIp = service.getServerIp();
    //                     logger.info("请输入服务器IP: " + serverIp);
    //                     addRequest.setServerIp(IPUtil.createIPAddress(serverIp));

    //                     // 正确处理整数类型转换
    //                     Integer serverPort = service.getServerPort();
    //                     logger.info("请输入服务器端口: " + serverPort);
    //                     addRequest.setServerPort(new PortNumber(serverPort != null ? serverPort : 0));
    //                 }
    //                 configRequest.setAddServiceRequest(addRequest);
    //          }
    //          if(messageType == ContentMessageType.updateService){
    //             // 创建更新服务请求
    //               str = "更新";
    //             UpdateServiceRequest updateRequest = new UpdateServiceRequest();
    //             updateRequest.setMessageType(ContentMessageType.updateService);
    //             updateRequest.setServiceId(new ServiceId(service.getId()));
    //             Network networkType = service.getNetwork() != null && service.getNetwork() == 1 ?
    //                                 Network.receiver : Network.sender;
    //             updateRequest.setNetwork(networkType);

    //             if (networkType == Network.sender) {
    //                 // 发送端特有字段
    //                 String proxyIp = service.getProxyIp();
    //                 logger.info("更新代理IP: " + proxyIp);
    //                 updateRequest.setProxyIp(IPUtil.createIPAddress(proxyIp));

    //                 // 正确处理整数类型转换
    //                 Integer proxyPort = service.getProxyPort();
    //                 logger.info("更新代理端口: " + proxyPort);
    //                 updateRequest.setProxyPort(new PortNumber(proxyPort != null ? proxyPort : 0));

    //                 // 设置关键字检测位图
    //                 String keyCheckHex = service.getContentKeyCheck() != null ?
    //                                     Integer.toHexString(service.getContentKeyCheck()) : "00";
    //                 logger.info("更新内容关键字检测位图值 (十六进制，例如 03): " + keyCheckHex);
    //                 byte[] contentKeyCheck = new byte[1];
    //                 contentKeyCheck[0] = (byte) Integer.parseInt(keyCheckHex, 16);
    //                 updateRequest.setContentKeyCheck(new ContentKeyCheck(contentKeyCheck));

    //                 // 设置协议过滤位图
    //                 String protocolFilterHex = service.getProtocolFilter() != null ?
    //                                         service.getProtocolFilter() : "00";
    //                 logger.info("更新协议过滤位图值 (十六进制，例如 03): " + protocolFilterHex);
    //                 byte[] protocolFilter = new byte[1];
    //                 protocolFilter[0] = (byte) Integer.parseInt(protocolFilterHex, 16);
    //                 updateRequest.setProtocolFilter(new ProtocolFilter(protocolFilter));

    //                 // 设置告警后处理
    //                 logger.info("更新未通过处理方式:");
    //                 logger.info("0. ALLOW");
    //                 logger.info("1. DENY");

    //                 // 处理告警处理方式
    //                 PermissionState unpassDeal;
    //                 if (service.getUnpassDeal() != null) {
    //                     if ("deny".equalsIgnoreCase(service.getUnpassDeal())) {
    //                         unpassDeal = PermissionState.forbidden;
    //                         logger.info("请输入选择: 1");
    //                     } else {
    //                         unpassDeal = PermissionState.allow;
    //                         logger.info("请输入选择: 0");
    //                     }
    //                 } else {
    //                     unpassDeal = PermissionState.allow;
    //                     logger.info("请输入选择: 0");
    //                 }
    //                 updateRequest.setUnpassDeal(unpassDeal);
    //                 } else {
    //                     // 接收端特有字段
    //                     String serverIp = service.getServerIp();
    //                     logger.info("请输入服务器IP: " + serverIp);
    //                     updateRequest.setServerIp(IPUtil.createIPAddress(serverIp));

    //                     // 正确处理整数类型转换
    //                     Integer serverPort = service.getServerPort();
    //                     logger.info("请输入服务器端口: " + serverPort);
    //                     updateRequest.setServerPort(new PortNumber(serverPort != null ? serverPort : 0));
    //                 }

    //             configRequest.setUpdateServiceRequest(updateRequest);
    //          }
    //         if(messageType == ContentMessageType.deleteService){
    //                 // 创建删除服务请求
    //                   str = "删除";
    //             DeleteServiceRequest deleteRequest = new DeleteServiceRequest();
    //             deleteRequest.setMessageType(ContentMessageType.deleteService);
    //             deleteRequest.setServiceId(new ServiceId(service.getId()));
    //             logger.info("请输入要删除的服务ID: "+service.getId());
    //             configRequest.setDeleteServiceRequest(deleteRequest);
    //         }
    //         //直接创建
    //         ServiceConfigLogic serLogic = new ServiceConfigLogic();
    //         ServiceConfigResponse response = serLogic.serviceConfigRequest(configRequest);
    //         logger.info("服务"+str+"成功，服务ID: " + response.getServiceId());
    //         // // 创建消息帧
    //         // MessageRequestFrame frame = new MessageRequestFrame();
    //         // frame.setVersion(new Uint8(1));
    //         // frame.setContent(new MessageRequestFrame.Content());
    //         // frame.getContent().setServiceConfigRequest(configRequest);
    //         // MessageResponseFrame responseFrame = sendRequest(frame);

    //         // if (responseFrame.getContent().hasServiceConfigResponse()) {
    //         //     ServiceConfigResponse response = responseFrame.getContent().getServiceConfigResponse();
    //         //     logger.info("服务"+str+"成功，服务ID: " + response.getServiceId());
    //         //     serId = response.getServiceId().intValue()+"";
    //         // } else if (responseFrame.getContent().hasError()) {
    //         //     ErrorResponse errorResponse = responseFrame.getContent().getError();
    //         //     logger.info("请求失败: " + errorResponse.getErrorState());
    //         //     return "error";
    //         // }
    //     } catch (Exception e) {
    //         logger.info("发送请求失败: " + e.getMessage());
    //         e.printStackTrace();
    //         return "error";
    //     }
    //     return serId;
    // }

    // /**
    //  * 发送请求并返回响应
    //  */
    // private static MessageResponseFrame sendRequest(MessageRequestFrame request) throws IOException {

    //     // 编码消息
    //     byte[] encodedData = OssServAdapter.encodeRequest(request);

    //     logger.info("正在发送请求...");
    //     // 发送请求
    //     byte[] response = client.post("/sg", encodedData);

    //     // 解码响应
    //     MessageResponseFrame responseFrame = OssServAdapter.decodeResponse(response);
    //     logger.info("请求发送成功!");
    //     logger.info("响应版本: " + responseFrame.getVersion());
    //     logger.info("响应大小: " + response.length + " 字节");

    //     return responseFrame;
    // }

    // 更新服务
    public Optional<UdpService> updateService(Long id, UdpService updatedService) {
        // 检查名称是否已存在(排除当前服务)
        // if (isServiceNameExists(updatedService.getName(), id)) {
        //     throw new IllegalArgumentException("服务名称已存在，请使用不同的名称");
        // }
        //判断启停，停止才能修改
        // ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        // if(configXmlOperator.getIsRun(serviceId + "")){
        //     log.info("service " + serviceId + " is running");
        //      throw new IllegalArgumentException("服务运行中，不能修改");
        // }
        Optional<UdpService> serviceOpt = loadServiceConfig(id);

        if (serviceOpt.isPresent()) {
            UdpService service = serviceOpt.get();

            // 更新基本信息
            service.setName(updatedService.getName());
            service.setMessageType(updatedService.getMessageType());
            service.setNetwork(updatedService.getNetwork());
            service.setDescription(updatedService.getDescription());

            // 根据网段类型更新不同的字段
            if (service.getNetwork() != null && service.getNetwork() == 1) {
                // 接收端服务特有字段
                service.setServerIp(updatedService.getServerIp());
                service.setServerPort(updatedService.getServerPort());
                service.setSendaddrmap(updatedService.getSendaddrmap());
            } else {
                // 发送端服务特有字段
                service.setProxyIp(updatedService.getProxyIp());
                service.setProxyPort(updatedService.getProxyPort());
                service.setContentKeyCheck(updatedService.getContentKeyCheck());
                service.setContentKeywords(updatedService.getContentKeywords());
                service.setProtocolFilter(updatedService.getProtocolFilter());
                service.setUnpassDeal(updatedService.getUnpassDeal());
                service.setVehiclePlateCheck(updatedService.getVehiclePlateCheck());
                service.setIdCardCheck(updatedService.getIdCardCheck());
                service.setCrc16FormatCheck(updatedService.getCrc16FormatCheck());
                service.setAsnFormatCheck(updatedService.getAsnFormatCheck());

                 service.setMulticastIp(updatedService.getMulticastIp()); // 客户端组播地址
                 service.setSpecial_value(updatedService.getSpecial_value()); // 指定源地址
                 service.setRules(updatedService.getRules()); // 包过滤规则
                 service.setUdpFlood(updatedService.getUdpFlood()); // 是否启用抗UDP flood攻击
                 service.setIprange(updatedService.getIprange()); // 指定网段设置

                 // 兼容性支持，保留部分旧字段更新
                service.setTransportIp(updatedService.getTransportIp());
                service.setPort(updatedService.getPort());
                service.setStartPort(updatedService.getStartPort());
                service.setEndPort(updatedService.getEndPort());

                // 更新流量限制设置
                service.setTrafficLimit(updatedService.getTrafficLimit());
                service.setFlowlevel(updatedService.getFlowlevel()); // 添加流量限制字段更新
            }


            service.setSyncConfig(updatedService.getSyncConfig());

            // 更新审计设置
            service.setAudit(updatedService.getAudit()); // 添加审计字段更新

            // 更新新增的特殊参数
            service.setMulticast(updatedService.getMulticast()); // 是否支持组播功能


            // 记录特殊参数的更新
            logger.info("==== 更新关键参数 ====");
            logger.info("组播功能: {} -> {}", service.getMulticast(), updatedService.getMulticast());
            logger.info("组播IP地址: {} -> {}", service.getMulticastIp(), updatedService.getMulticastIp());
            logger.info("指定源地址: {} -> {}", service.getSpecial_value(), updatedService.getSpecial_value());
            logger.info("包过滤规则: {} -> {}", service.getRules(), updatedService.getRules());
            logger.info("UDP Flood防护: {} -> {}", service.getUdpFlood(), updatedService.getUdpFlood());
            logger.info("流量限制: {} -> {}", service.getFlowlevel(), updatedService.getFlowlevel());
            logger.info("是否审计: {} -> {}", service.getAudit(), updatedService.getAudit());
            logger.info("sendaddrmap: {} -> {}"+updatedService.getSendaddrmap());

            // 更新安全配置
            if (service.getSecurity() == null) {
                service.setSecurity(new UdpService.SecurityConfig());
            }
            if (updatedService.getSecurity() != null) {
                service.getSecurity().setFloodProtection(updatedService.getSecurity().isFloodProtection());
                service.getSecurity().setPacketRateLimit(updatedService.getSecurity().getPacketRateLimit());
                service.getSecurity().setConnectionLimit(updatedService.getSecurity().getConnectionLimit());
                service.getSecurity().setSecurityPolicy(updatedService.getSecurity().getSecurityPolicy());
            }

            // 如果直接设置了floodProtection属性
            if (updatedService.isFloodProtection() != service.isFloodProtection()) {
                service.setFloodProtection(updatedService.isFloodProtection());
            }

            // 对于接收端服务，如果updatedService已经设置了状态，则使用该状态
            // 对于发送端服务，保持原有逻辑
            if (service.getNetwork() != null && service.getNetwork() == 1) {
                // 接收端服务：使用controller传入的状态
                service.setStatus(updatedService.getStatus());
                logger.info("接收端服务状态更新为: {}", updatedService.getStatus());
            } else {
                // 发送端服务：使用controller传入的状态（controller已经处理了状态逻辑）
                service.setStatus(updatedService.getStatus());
                logger.info("发送端服务状态更新为: {}", updatedService.getStatus());
            }

            service.setUpdatedAt(LocalDateTime.now());

            // 保存服务配置
            ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(id+"");
            serviceXmlOperator.saveUdpService(service);
            //saveServiceConfig(service);

            // 更新服务列表
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                UdpServiceInfo existingService = servicesList.getServices().get(i);
                if (existingService.getId().equals(id)) {
                    // 创建简化的服务信息对象
                    UdpServiceInfo updatedServiceInfo = new UdpServiceInfo(service);
                    // 设置更新状态
                    updatedServiceInfo.setUpdateStatus("updated");

                    if (service.getNetwork() != null && service.getNetwork() == 1) {
                        logger.info("更新接收端同步状态为CONFIG_COMPLETED");
                         updatedServiceInfo.setSyncStatus("CONFIG_COMPLETED");
                    }

                    // 保留原来的运行状态信息
                    if (existingService.getUptimeInfo() != null) {
                        updatedServiceInfo.setUptimeInfo(existingService.getUptimeInfo());
                    }
                    if (existingService.getConnectionCount() != null) {
                        updatedServiceInfo.setConnectionCount(existingService.getConnectionCount());
                    }
                    if (existingService.getTrafficInfo() != null) {
                        updatedServiceInfo.setTrafficInfo(existingService.getTrafficInfo());
                    }

                    // 更新服务列表
                    servicesList.getServices().set(i, updatedServiceInfo);
                    break;
                }
            }
            saveServicesList();
            // 如果是发送端服务，同步配置到srcapps目录
            if (service.getNetwork() == null || service.getNetwork() == 0) {
                sendConfigToSrcapps(service.getId());
            }

            // 更新服务配置请求
            //sendServiceConfigRequest(service,ContentMessageType.updateService);

            return Optional.of(service);
        }

        return Optional.empty();
    }

    // 删除服务
    public boolean deleteService(Long id) {
        try {
            logger.info("开始删除服务: ID=" + id + ", 网络类型=" + network);

            // 不再使用 getServiceById(id)，而是根据 network 参数确定加载方式
            UdpService service = null;
            String serviceName = "";

            // 先从服务列表中获取服务名称，用于日志记录
            Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(id);
            if (serviceInfoOpt.isPresent()) {
                serviceName = serviceInfoOpt.get().getName();
            }

            boolean isReceiver = network != null && network == 1;

            // 创建基本服务对象，仅用于删除操作
            service = new UdpService();
            service.setId(id);
            service.setName(serviceName);
            service.setNetwork(network);

            logger.info("开始删除" + (isReceiver ? "接收端" : "发送端") + "服务: " + serviceName + " (ID: " + id + ")");

            // 1. 删除服务配置文件
            String configFilePath = getServiceFilePath(id);
            File configFile = new File(configFilePath);
            if (configFile.exists()) {
                boolean deleted = configFile.delete();
                logger.info("删除配置文件 " + configFilePath + ": " + (deleted ? "成功" : "失败"));
            } else {
                logger.info("配置文件不存在: " + configFilePath);
            }

            // 2. 删除服务属性文件
            String propertiesPath = getServicePropertiesLocation(id);
            File propertiesFile = new File(propertiesPath);
            if (propertiesFile.exists()) {
                boolean deleted = propertiesFile.delete();
                logger.info("删除属性文件 " + propertiesPath + ": " + (deleted ? "成功" : "失败"));
            } else {
                logger.info("属性文件不存在: " + propertiesPath);
            }


            // 4. 从服务列表中移除
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                if (servicesList.getServices().get(i).getId().equals(id)) {
                    servicesList.getServices().remove(i);
                    break;
                }
            }

            // 5. 保存更新后的服务列表 - 使用带network参数的方法
            saveServicesList();

            // 6. 根据网络类型处理特殊逻辑
            if (isReceiver) {
                // 接收端服务：只需保证接收端服务列表更新
                try {
                    // 从接收端配置文件中也删除服务
                    File receiverListFile = new File(getReceiverServicesListFilePath());
                    if (receiverListFile.exists()) {
                        UdpServiceList receiverList = loadServiceListFromFile(receiverListFile);
                        if (receiverList != null && receiverList.getServices() != null) {
                            receiverList.getServices().removeIf(s -> s.getId().equals(id));
                            saveServiceListToFile(receiverList, receiverListFile);
                            logger.info("已从接收端服务列表中移除服务: ID={}", id);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("从接收端服务列表移除服务时出错: {}", e.getMessage());
                }
            } else {
                // 发送端服务：同步配置到srcapps目录
                File srcappsDirFile = new File(srcappsDir);
                if (srcappsDirFile.exists()) {
                    // 同步全局配置文件(config.xml)到srcapps目录
                    String servicesListFilePath = getServicesListFilePath();
                    File configXmlFile = new File(servicesListFilePath);
                    if (configXmlFile.exists()) {
                        File destConfigFile = new File(srcappsDir + "/config.xml");
                        Files.copy(configXmlFile.toPath(), destConfigFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                        logger.info("全局配置文件已同步到srcapps目录: {}", destConfigFile.getAbsolutePath());
                    }

                    // 删除srcapps目录中的服务配置文件
                    File srcappServiceConfig = new File(srcappsDir + "/" + id + ".xml");
                    if (srcappServiceConfig.exists()) {
                        boolean deleted = srcappServiceConfig.delete();
                        logger.info("删除srcapps目录中的服务配置文件: {}, 结果: {}", srcappServiceConfig.getAbsolutePath(), deleted ? "成功" : "失败");
                    }
                }
            }
             // 删除服务配置请求
            //sendServiceConfigRequest(service,ContentMessageType.deleteService);
            System.out.println((isReceiver ? "接收端" : "发送端") + "服务删除成功: " + serviceName + " (ID: " + id + ")");
            return true;
        } catch (Exception e) {
            System.err.println("删除服务时出现异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }




    // 部署服务
    public boolean deployService(UdpService service) {
        // 保存当前的同步状态，以便在接收端部署时保留
        try {
            System.out.println("开始部署服务: ID=" + service.getId() + ", 名称=" + service.getName());
            String originalSyncStatus = null;
            // 根据网段类型检查必要的配置
            if (network != null && network == 1) {
                Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(service.getId());
                if (serviceInfoOpt.isPresent()) {
                    originalSyncStatus = serviceInfoOpt.get().getSyncStatus();
                    DebugLogger.log("接收端服务部署前的同步状态: " + originalSyncStatus);
                }
                // 接收端服务，检查serverIp
                if (service.getServerIp() == null || service.getServerIp().trim().isEmpty()) {
                    System.out.println("部署失败: 接收端服务缺少服务器IP配置");
                    return false;
                }
                if (service.getServerPort() == null) {
                    System.out.println("部署失败: 接收端服务缺少服务器端口配置");
                    return false;
                }
                //将sendaddrmap内容写入/etc/unimas/tomcat/conf/service.addr_map.conf
                try {
                    String serviceid = String.valueOf(service.getId());
                    String sendAddrInfo = service.getSendaddrmap();
                    File file = new File("/etc/unimas/tomcat/conf/"+serviceid+".addr_map.conf");
                    if (!file.exists()) {
                        file.createNewFile();
                    }
                    sendAddrInfo = sendAddrInfo.replaceAll(";", "\r\n");
                    if (!"".equals(sendAddrInfo)) {
                        sendAddrInfo += "\r\n";
                    }
                    FileOutputStream fileOutputStream = new FileOutputStream(file);
                    //将sendAddrInfo写入文件
                    fileOutputStream.write(sendAddrInfo.getBytes());
                    fileOutputStream.close();
                } catch (Exception e) {
                    System.err.println("写入sendaddrmap配置文件失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                // 发送端服务，检查proxyIp
                if (service.getProxyIp() == null || service.getProxyIp().trim().isEmpty()) {
                    System.out.println("部署失败: 发送端服务缺少监听IP配置");
                    return false;
                }
                if (service.getProxyPort() == null) {
                    System.out.println("部署失败: 发送端服务缺少监听端口配置");
                    return false;
                }
            }

            // 执行部署逻辑
            // 1. 创建配置文件
            File configFile = createServicePropertiesFile(service);
            if (configFile == null) {
                System.out.println("部署失败: 无法创建配置文件");
                return false;
            }

            // 2. 注册服务到系统
            boolean registered = registerService(service);
            if (!registered) {
                System.out.println("部署失败: 无法注册服务");
                return false;
            }

            // 3. 明确更新服务状态为 "deployed"
            service.setStatus("deployed");

            // 在这里保存服务配置
            ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(service.getId().toString());
            serviceXmlOperator.saveUdpService(service);
            System.out.println("服务配置已保存，状态: " + service.getStatus());

            // 4. 查找服务索引并更新服务列表
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                UdpServiceInfo existingService = servicesList.getServices().get(i);
                if (existingService.getId().equals(service.getId())) {
                    // 创建简化的服务信息对象
                    UdpServiceInfo updatedServiceInfo = new UdpServiceInfo(service);
                    updatedServiceInfo.setStatus(service.getStatus());
                    updatedServiceInfo.setUpdateStatus("normal");
                    if (network != null && network == 1) {
                        updatedServiceInfo.setSyncStatus(originalSyncStatus);
                    }
                    // 更新服务列表
                    servicesList.getServices().set(i, updatedServiceInfo);
                    break;
                }
            }

            // 5. 保存服务列表到XML文件
            saveServicesList();

            // 如果是发送端服务，同步配置到srcapps目录
            if (service.getNetwork() == null || service.getNetwork() == 0) {
                boolean configSent = sendConfigToSrcapps(service.getId());
                if (configSent) {
                    System.out.println("服务配置已同步到srcapps目录");
                } else {
                    System.out.println("警告: 服务配置同步到srcapps目录失败");
                }
            }

            System.out.println("服务部署成功: " + service.getName() + " (ID: " + service.getId() + ")");
            return true;
        } catch (Exception e) {
            System.err.println("服务部署异常");
            e.printStackTrace();
            return false;
        }
    }
    // 创建服务配置文件（.properties格式）
    private File createServicePropertiesFile(UdpService service) {
        try {
            // 配置文件路径
            String configFilePath = configDir + "/" + service.getId() + ".properties";

            File configFile = new File(configFilePath);

            // 确保目录存在
            configFile.getParentFile().mkdirs();

            // 创建配置文件内容
            StringBuilder content = new StringBuilder();
            content.append("# 服务配置 - ").append(service.getName()).append("\n");
            content.append("audit=").append(service.getAudit()).append("\n");
            content.append("g_cs=0").append("\n");
            content.append("g_ip=").append("\n");
            content.append("portchanl=").append((port+Integer.valueOf(service.getId().toString()))).append("\n");
            content.append("serid=").append(service.getId()).append("\n");

            // 处理指定网段设置 - 转换格式
            String iprangeForProperties = convertIprangeForProperties(service.getIprange());
            content.append("iprange=").append(iprangeForProperties).append("\n");
            logger.info("转换指定网段设置 - 原始: {}, Properties格式: {}", service.getIprange(), iprangeForProperties);

            content.append("g_group=0").append("\n");
            content.append("special_s=").append("\n");
            // 根据服务类型添加不同的配置项
            if (service.getNetwork() != null && service.getNetwork() == 1) {
                // 接收端特有配置
                content.append("hostip=").append(service.getServerIp()).append("\n");
                content.append("hostport=").append(service.getServerPort()).append("\n");
            } else {
                // 发送端特有配置
                content.append("ipproxy=").append(service.getProxyIp()).append("\n");
                content.append("portclient=").append(service.getProxyPort()).append("\n");

                content.append("asnfilter=").append(service.getAsnFormatCheck() != null ? service.getAsnFormatCheck() : "0").append("\n");
                content.append("crcfilter=").append(service.getCrc16FormatCheck() != null ? service.getCrc16FormatCheck() : "0").append("\n");
                content.append("idcheck=").append(service.getIdCardCheck() != null ? service.getIdCardCheck() : "0").append("\n");
                content.append("carcheck=").append(service.getVehiclePlateCheck() != null ? service.getVehiclePlateCheck() : "0").append("\n");
                content.append("unpassdeal=").append(service.getUnpassDeal() != null ? service.getUnpassDeal() : "0").append("\n");

            }

            // 写入配置文件
            Files.write(configFile.toPath(), content.toString().getBytes());

            System.out.println("成功创建服务配置文件: " + configFilePath);
            return configFile;
        } catch (Exception e) {
            System.err.println("创建配置文件异常");
            e.printStackTrace();
            return null;
        }
    }

    // 注册服务
    private boolean registerService(UdpService service) {
        try {
            // 在实际应用中，这里可能需要调用系统API或执行shell命令来注册服务
            // 这里简化实现，只记录日志
            System.out.println("注册服务: " + service.getName() + " (ID: " + service.getId() + ")");

            // 模拟注册过程
            Thread.sleep(500);

            return true;
        } catch (Exception e) {
            System.err.println("注册服务异常");
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将配置文件中的iprange格式转换为properties文件格式
     * 配置格式：#ip1:port1-ip1:port1#ip2:port2-ip2:port2#...#ip10:port10-ip10:port10#
     * Properties格式：#ip1-ip1#ip2-ip2#...#ip10-ip10#
     * 也支持只有一个ip段的情况
     */
    private String convertIprangeForProperties(String configIprange) {
        if (configIprange == null || configIprange.trim().isEmpty()) {
            return "#*#"; // 默认值
        }

        logger.info("开始转换iprange格式，输入: {}", configIprange);

        try {
            // 使用分割方法来解析，更加可靠
            logger.info("开始解析IP段，使用分割方法...");

            StringBuilder propertiesFormat = new StringBuilder();

            // 先移除开头和结尾可能的空#
            String cleanInput = configIprange.trim();
            if (cleanInput.startsWith("#")) {
                cleanInput = cleanInput.substring(1);
            }
            if (cleanInput.endsWith("#")) {
                cleanInput = cleanInput.substring(0, cleanInput.length() - 1);
            }

            // 使用#分割，得到所有段落
            String[] segments = cleanInput.split("#");
            logger.info("分割后得到{}个段落", segments.length);

            int count = 0;
            for (String segment : segments) {
                if (segment.trim().isEmpty()) continue;

                count++;
                logger.info("处理第{}个段落: {}", count, segment);

                String[] parts = segment.split("-");
                if (parts.length == 2) {
                    // 提取IP部分（去掉端口）
                    String ip1 = parts[0].contains(":") ? parts[0].substring(0, parts[0].indexOf(":")) : parts[0];
                    String ip2 = parts[1].contains(":") ? parts[1].substring(0, parts[1].indexOf(":")) : parts[1];

                    logger.info("第{}个段落 - 提取IP: {} -> {}", count, ip1, ip2);

                    propertiesFormat.append("#").append(ip1).append("-").append(ip2);
                } else {
                    logger.warn("第{}个段落格式不正确，跳过: {}", count, segment);
                }
            }

            // 如果解析到了内容，最后加上结束的#
            if (propertiesFormat.length() > 0) {
                propertiesFormat.append("#");
            } else {
                logger.warn("未解析到任何有效的IP段，返回默认值");
                return "#*#";
            }

            String result = propertiesFormat.toString();
            logger.info("转换完成，共解析{}个段落，输出: {}", count, result);
            return result;

        } catch (Exception e) {
            logger.error("转换iprange格式失败: {}", e.getMessage());
            return "#*#"; // 出错时返回默认值
        }
    }

    // 增强ServiceConfigurationService中的状态管理
    public boolean updateServiceRunningStatus(Long id, String status) {
        try {
            System.out.println("更新服务运行状态: ID=" + id + ", 状态=" + status);

            // 1. 更新服务配置
            Optional<UdpService> serviceOpt = loadServiceConfig(id);
            if (!serviceOpt.isPresent()) {
                System.out.println("更新状态失败: 服务不存在");
                return false;
            }

            UdpService service = serviceOpt.get();
            service.setStatus(status);

            // 使用ServiceXmlOperator保存服务配置
            ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(id.toString());
            serviceXmlOperator.saveUdpService(service);

            // 2. 更新服务列表中的状态
            boolean updated = false;
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                UdpServiceInfo existingService = servicesList.getServices().get(i);
                if (existingService.getId().equals(id)) {
                    System.out.println("在服务列表中更新状态: 原状态=" + existingService.getStatus() + ", 新状态=" + status);

                    // 创建简化的服务信息对象
                    UdpServiceInfo updatedServiceInfo = new UdpServiceInfo(service);

                    // 设置同步状态
                    if (network == 1) {
                        Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(service.getId());
                        if (serviceInfoOpt.isPresent()) {
                            String originalSyncStatus = serviceInfoOpt.get().getSyncStatus();
                            updatedServiceInfo.setSyncStatus(originalSyncStatus);
                            DebugLogger.log("接收端服务状态更新时保留同步状态: " + originalSyncStatus);
                        } else {
                            updatedServiceInfo.setSyncStatus("CONFIG_COMPLETED");
                        }
                    }

                    // 保留关键字段
                    updatedServiceInfo.setUpdateStatus(existingService.getUpdateStatus());

                    // 保留运行时信息
                    if (existingService.getUptimeInfo() != null) {
                        updatedServiceInfo.setUptimeInfo(existingService.getUptimeInfo());
                    }
                    if (existingService.getConnectionCount() != null) {
                        updatedServiceInfo.setConnectionCount(existingService.getConnectionCount());
                    }
                    if (existingService.getTrafficInfo() != null) {
                        updatedServiceInfo.setTrafficInfo(existingService.getTrafficInfo());
                    }

                    servicesList.getServices().set(i, updatedServiceInfo);
                    updated = true;
                    break;
                }
            }

            if (!updated) {
                System.out.println("警告: 在服务列表中未找到服务ID=" + id);
            }

            // 3. 保存更新后的服务列表
            saveServicesList();

            return true;
        } catch (Exception e) {
            System.err.println("更新服务状态异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }


public boolean startSMService(String sid,String trafficLimit){
        boolean ret = false;
        SMProcess smProcess = new SMProcess();
        Map<String,String> map = new HashMap<>();
        map.put(Command.APPNAME.getField(),sid);
        try {
            addAddrMap(sid,trafficLimit);
            ret = (Boolean)smProcess.process(Command.UDP_START_APP, map);
        } catch (Exception e) {
            logger.error("",e);
        }
        return ret;
    }
    public boolean stopSMService(String sid){
        boolean ret = false;
        SMProcess smProcess = new SMProcess();
        Map<String,String> map = new HashMap<>();
        map.put(Command.APPNAME.getField(),sid);
        try {
            deleteAddrMap(sid);
            ret = (Boolean)smProcess.process(Command.UDP_STOP_APP, map);
        } catch (Exception e) {
            logger.error("",e);
        }
        return ret;
    }

    //10是trafficLimit  流量限制
    private static final String ADDR_MAP_FORMAT = "127.0.0.1:%d,127.0.0.1:%d,%s,%s,null,null";

    public boolean addAddrMap(String serviceId,String trafficLimit) {
        try {
            File addrMapFile = new File(Constant.ADDR_MAP_PATH);
            if (!addrMapFile.exists()) {
                addrMapFile.createNewFile();
            }

            // 检查服务号是否已存在
            boolean exists = false;
            java.util.List<String> lines = java.nio.file.Files.readAllLines(addrMapFile.toPath());
            for (String line : lines) {
                String[] parts = line.split(",");
                if (parts.length >= 4 && parts[3].trim().equals(serviceId)) {
                    exists = true;
                    break;
                }
            }

            if (!exists) {
                int servicePort = 20560 + Integer.parseInt(serviceId);
                String newLine = String.format(ADDR_MAP_FORMAT, servicePort, servicePort,trafficLimit, serviceId);
                java.nio.file.Files.write(addrMapFile.toPath(),
                        java.util.Arrays.asList(newLine),
                        java.nio.file.StandardOpenOption.APPEND);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("添加addr.map记录失败", e);
            return false;
        }
    }

    public boolean deleteAddrMap(String serviceId) {
        try {
            File addrMapFile = new File(Constant.ADDR_MAP_PATH);
            if (!addrMapFile.exists()) {
                return false;
            }

            java.util.List<String> lines = java.nio.file.Files.readAllLines(addrMapFile.toPath());
            java.util.List<String> newLines = new java.util.ArrayList<>();
            boolean found = false;

            for (String line : lines) {
                String[] parts = line.split(",");
                if (parts.length >= 4 && !parts[3].trim().equals(serviceId)) {
                    newLines.add(line);
                } else {
                    found = true;
                }
            }

            if (found) {
                java.nio.file.Files.write(addrMapFile.toPath(), newLines);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("删除addr.map记录失败", e);
            return false;
        }
    }

    // 改进启动服务方法
    public boolean startService(Long id) {
        try {
            System.out.println("开始启动服务: ID=" + id);

            Optional<UdpService> serviceOpt = loadServiceConfig(id);
            if (!serviceOpt.isPresent()) {
                System.out.println("启动失败: 服务不存在，ID=" + id);
                return false;
            }

            UdpService service = serviceOpt.get();
            String currentStatus = service.getStatus();
            System.out.println("服务当前状态: " + (currentStatus == null ? "null" : "'" + currentStatus + "'"));
            Integer trafficLimit = service.getTrafficLimit();
            System.out.println("服务流量限制: " + (trafficLimit == null ? "null" : "'" + trafficLimit + "'"));
            // 放宽状态检查，允许空状态或deployed或stopped的服务启动
            if ("running".equals(currentStatus)) {
                System.out.println("启动失败: 服务已在运行中，当前状态: " + currentStatus);
                return false;
            }

            // 在这里添加启动服务的实际逻辑
            System.out.println("正在启动服务: " + service.getName());
            if(startSMService(id+"",trafficLimit+"")){
                System.out.println("SM启动成功");
            }else{
                System.out.println("SM启动失败");
                return false;
            }
            // 模拟服务启动过程
            Thread.sleep(1000);

            // 更新服务状态为running
            service.setStatus("running");
            service.setUpdatedAt(LocalDateTime.now());

            // 使用新的XML配置格式保存服务配置
            ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(id.toString());
            serviceXmlOperator.saveUdpService(service);

            // 更新服务列表中的状态
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                UdpServiceInfo existingService = servicesList.getServices().get(i);
                if (existingService.getId().equals(id)) {
                    existingService.setStatus("running");
                    existingService.setUpdateStatus("normal");
                    existingService.setNetwork(network);
                    if (network != null && network == 1) {
                         Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(service.getId());
                         if (serviceInfoOpt.isPresent()) {
                            String originalSyncStatus = serviceInfoOpt.get().getSyncStatus();
                            existingService.setSyncStatus(originalSyncStatus);
                            DebugLogger.log("接收端服务启动时保留同步状态: " + originalSyncStatus);
                         }
                    }
                    servicesList.getServices().set(i, existingService);
                    break;
                }
            }

            // 保存服务列表
            saveServicesList();

            // 如果是发送端服务，则同步配置文件到srcapps目录
            if (service.getNetwork() == null || service.getNetwork() == 0) {
                boolean configSent = sendConfigToSrcapps(service.getId());
                if (configSent) {
                    System.out.println("服务配置已同步到srcapps目录");
                } else {
                    System.out.println("警告: 服务配置同步到srcapps目录失败");
                }
            }

            //handleServiceControl(Integer.valueOf(id.toString()),0);

            System.out.println("服务启动成功: " + service.getName());
            return true;
        } catch (Exception e) {
            System.err.println("启动服务异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理服务启停请求
     */
    // public void handleServiceControl(int serviceId,int type) {
    //     try {

    //         ServiceControlAction action = (type == 0) ? ServiceControlAction.start : ServiceControlAction.stop;

    //         // 创建服务控制请求
    //         ServiceControlRequest controlRequest = new ServiceControlRequest();
    //         controlRequest.setMessageType(ContentMessageType.controlService);
    //         controlRequest.setServiceId(new ServiceId(serviceId));
    //         controlRequest.setServiceStartOrStop(action);

    //         // 发送请求并处理响应
    //         // 创建消息帧
    //         MessageRequestFrame frame = new MessageRequestFrame();
    //         frame.setVersion(new Uint8(1));
    //         frame.setContent(new MessageRequestFrame.Content());
    //         frame.getContent().setServiceControlRequest(controlRequest);
    //         MessageResponseFrame responseFrame = sendRequest(frame);

    //         // 输出响应信息
    //         if (responseFrame.getContent().hasServiceControlResponse()) {
    //             ServiceControlResponse response = responseFrame.getContent().getServiceControlResponse();
    //             System.out.println("服务" + (action == ServiceControlAction.start ? "启动" : "停止") + "请求成功");
    //             System.out.println("服务ID: " + response.getServiceId());
    //             System.out.println("操作: " + (response.getServiceStartOrStop() == ServiceControlAction.start ? "启动" : "停止"));
    //         } else if (responseFrame.getContent().hasError()) {
    //             ErrorResponse errorResponse = responseFrame.getContent().getError();
    //             System.out.println("请求失败: " + errorResponse.getErrorState());
    //         }
    //     } catch (Exception e) {
    //         System.err.println("服务控制请求失败: " + e.getMessage());
    //         e.printStackTrace();
    //     }
    // }

    // 优化停止服务方法
    public boolean stopService(Long id) {
        try {
            System.out.println("开始停止服务: ID=" + id);
            //handleServiceControl(Integer.valueOf(id.toString()),1);
            Optional<UdpService> serviceOpt = getServiceById(id);
            if (!serviceOpt.isPresent()) {
                System.out.println("停止失败: 服务不存在，ID=" + id);
                return false;
            }

            UdpService service = serviceOpt.get();

            // // 检查服务是否在运行中
            // if (!"running".equals(service.getStatus())) {
            //     System.out.println("停止失败: 服务未在运行中，当前状态: " + service.getStatus());
            //     return false;
            // }

            // 在这里添加停止服务的实际逻辑
            System.out.println("正在停止服务: " + service.getName());
            // 在这里添加启动服务的实际逻辑
            if(stopSMService(id+"")){
                System.out.println("SM停止成功");
            }else{
                System.out.println("SM停止失败");
                return false;
            }
            // 模拟服务停止过程
            Thread.sleep(1000);

            // 使用统一的状态更新方法
            boolean statusUpdated = updateServiceRunningStatus(id, "stopped");
            if (!statusUpdated) {
                System.out.println("警告: 服务状态更新失败");
            }

            // 如果是发送端服务，则同步配置文件到srcapps目录
            if (service.getNetwork() == null || service.getNetwork() == 0) {
                boolean configSent = sendConfigToSrcapps(id);
                if (configSent) {
                    System.out.println("服务配置已同步到srcapps目录");
                } else {
                    System.out.println("警告: 服务配置同步到srcapps目录失败");
                }
            }

            System.out.println("服务停止成功: " + service.getName());
            return true;
        } catch (Exception e) {
            System.err.println("停止服务异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }



    // 根据名称搜索服务
    public List<UdpServiceInfo> searchServicesByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return getAllServicesInfo();
        }

        String searchTerm = name.toLowerCase();
        return servicesList.getServices().stream()
                .filter(service -> service.getName().toLowerCase().contains(searchTerm))
                .collect(Collectors.toList());
    }

    /**
     * 获取服务列表
     * @return 服务信息列表
     */
    public UdpServiceList getServiceList() {
        return this.servicesList;
    }

    /**
     * 根据ID从服务列表中移除服务
     * @param id 服务ID
     * @return 是否成功移除
     */
    private boolean removeService(Long id) {
        return removeService(id, null); // 默认更新所有配置文件
    }

    /**
     * 根据ID从服务列表中移除服务
     * @param id 服务ID
     * @param network 网络类型：0表示发送端，1表示接收端，null表示更新所有
     * @return 是否成功移除
     */
    private boolean removeService(Long id, Integer network) {
        for (int i = 0; i < servicesList.getServices().size(); i++) {
            if (servicesList.getServices().get(i).getId().equals(id)) {
                servicesList.getServices().remove(i);
                saveServicesList(); // 使用带参数的保存方法
                return true;
            }
        }
        return false;
    }

    /**
     * 检查并修复服务状态的一致性
     */
    public void checkAndFixServiceStates() {
        System.out.println("开始检查并修复服务状态...");

        //List<UdpServiceInfo> services = getAllServicesInfo();
        // for (UdpServiceInfo serviceInfo : services) {
        //     Optional<UdpService> serviceOpt = getServiceById(serviceInfo.getId());
        //     if (serviceOpt.isPresent()) {
        //         UdpService service = serviceOpt.get();
        //         String infoStatus = serviceInfo.getStatus();
        //         String configStatus = service.getStatus();

        //         if (infoStatus == null && configStatus != null ||
        //             infoStatus != null && !infoStatus.equals(configStatus)) {
        //             System.out.println("发现状态不一致: 服务ID=" + serviceInfo.getId() +
        //                               ", 列表状态=" + infoStatus +
        //                               ", 配置状态=" + configStatus);

        //             // 使用配置状态更新列表状态
        //             updateServiceRunningStatus(serviceInfo.getId(), configStatus);
        //         }
        //     }
        // }

        System.out.println("服务状态检查完成");
    }

    /**
     * 发送配置文件到接收端
     * @param senderServiceId 发送端服务ID
     * @return 操作是否成功
     */
    public boolean sendConfigToReceiver(Long senderServiceId) {
        Optional<UdpService> optSenderService = getServiceById(senderServiceId);

        if (!optSenderService.isPresent()) {
            logger.error("发送配置文件到接收端失败: 发送端服务不存在, ID: {}", senderServiceId);
            return false;
        }

        UdpService senderService = optSenderService.get();

        if (!senderService.isSender()) {
            logger.error("发送配置文件到接收端失败: 指定的服务不是发送端服务, ID: {}", senderServiceId);
            return false;
        }

        try {
            // 确保接收端配置目录存在
            File receiverDir = new File(receiverConfigDir);
            if (!receiverDir.exists()) {
                receiverDir.mkdirs();
            }

            // 保存发送端配置到接收端配置目录（简单模拟配置文件发送）
            String senderConfigFileName = "sender_" + senderServiceId + ".xml";
            File targetFile = new File(Paths.get(receiverConfigDir, senderConfigFileName).toString());

            JAXBContext context = JAXBContext.newInstance(UdpService.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.marshal(senderService, targetFile);

            logger.info("成功发送配置文件到接收端: {}", targetFile.getAbsolutePath());

            // 更新发送端服务部署状态
            senderService.setDeploymentState("CONFIG_SENT");
            updateService(senderServiceId, senderService);

            return true;
        } catch (Exception e) {
            logger.error("发送配置文件到接收端失败: ", e);
            return false;
        }
    }

    /**
     * 从接收端配置目录加载发送端配置
     * @param senderServiceId 发送端服务ID
     * @return 发送端服务配置
     */
    public Optional<UdpService> loadSenderConfigFromReceiver(Long senderServiceId) {
        String senderConfigFileName = "sender_" + senderServiceId + ".xml";
        File senderConfigFile = new File(Paths.get(receiverConfigDir, senderConfigFileName).toString());

        if (!senderConfigFile.exists()) {
            logger.error("从接收端加载配置失败: 配置文件不存在, 发送端服务ID: {}", senderServiceId);
            return Optional.empty();
        }

        try {
            JAXBContext context = JAXBContext.newInstance(UdpService.class);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            UdpService senderService = (UdpService) unmarshaller.unmarshal(senderConfigFile);

            return Optional.of(senderService);
        } catch (Exception e) {
            logger.error("从接收端加载配置失败: ", e);
            return Optional.empty();
        }
    }

    /**
     * 在接收端创建服务（基于发送端配置）
     * @param senderServiceId 发送端服务ID
     * @return 创建的接收端服务
     */
    public UdpService createReceiverService(Long senderServiceId) {
        // 从接收端目录加载发送端配置
        Optional<UdpService> optSenderService = loadSenderConfigFromReceiver(senderServiceId);

        if (!optSenderService.isPresent()) {
            logger.error("创建接收端服务失败: 无法加载发送端配置, ID: {}", senderServiceId);
            return null;
        }

        UdpService senderService = optSenderService.get();

        // 创建接收端服务
        UdpService receiverService = new UdpService();
        receiverService.setName(senderService.getName());
        receiverService.setMessageType(senderService.getMessageType());
        receiverService.setNetwork(1); // 设置为接收端
        receiverService.setSenderServiceId(senderServiceId);

        // 复制部分发送端配置到接收端
        receiverService.setResourceId(senderService.getResourceId());
        receiverService.setResourceName(senderService.getResourceName());
        receiverService.setDescription(senderService.getDescription());

        // 保存接收端服务
        UdpService savedReceiverService = addService(receiverService);

        // 更新发送端配置文件的部署状态
        senderService.setDeploymentState("SERVICE_CREATED");
        try {
            // 更新接收端目录中的发送端配置文件
            String senderConfigFileName = "sender_" + senderServiceId + ".xml";
            File targetFile = new File(Paths.get(receiverConfigDir, senderConfigFileName).toString());

            JAXBContext context = JAXBContext.newInstance(UdpService.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.marshal(senderService, targetFile);
        } catch (Exception e) {
            logger.error("更新发送端配置文件状态失败: ", e);
        }

        return savedReceiverService;
    }

    /**
     * 接收端更新服务配置（基于发送端更新的配置）
     * @param receiverServiceId 接收端服务ID
     * @return 操作是否成功
     */
    public boolean updateReceiverService(Long receiverServiceId) {
        Optional<UdpService> optReceiverService = getServiceById(receiverServiceId);

        if (!optReceiverService.isPresent()) {
            logger.error("更新接收端服务失败: 接收端服务不存在, ID: {}", receiverServiceId);
            return false;
        }

        UdpService receiverService = optReceiverService.get();

        if (!receiverService.isReceiver()) {
            logger.error("更新接收端服务失败: 指定的服务不是接收端服务, ID: {}", receiverServiceId);
            return false;
        }

        Long senderServiceId = receiverService.getSenderServiceId();
        if (senderServiceId == null) {
            logger.error("更新接收端服务失败: 接收端服务未关联发送端服务, ID: {}", receiverServiceId);
            return false;
        }

        // 从接收端目录加载发送端配置
        Optional<UdpService> optSenderService = loadSenderConfigFromReceiver(senderServiceId);

        if (!optSenderService.isPresent()) {
            logger.error("更新接收端服务失败: 无法加载发送端配置, ID: {}", senderServiceId);
            return false;
        }

        UdpService senderService = optSenderService.get();

        // 更新接收端服务配置（保留接收端特有的配置）
        receiverService.setName(senderService.getName());
        receiverService.setMessageType(senderService.getMessageType());
        receiverService.setResourceId(senderService.getResourceId());
        receiverService.setResourceName(senderService.getResourceName());
        receiverService.setDescription(senderService.getDescription());

        // 保存更新后的接收端服务
        updateService(receiverServiceId, receiverService);

        // 更新发送端配置文件的部署状态
        senderService.setDeploymentState("SERVICE_CREATED");
        try {
            // 更新接收端目录中的发送端配置文件
            String senderConfigFileName = "sender_" + senderServiceId + ".xml";
            File targetFile = new File(Paths.get(receiverConfigDir, senderConfigFileName).toString());

            JAXBContext context = JAXBContext.newInstance(UdpService.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.marshal(senderService, targetFile);
        } catch (Exception e) {
            logger.error("更新发送端配置文件状态失败: ", e);
        }

        return true;
    }

    /**
     * 删除接收端服务
     * @param receiverServiceId 接收端服务ID
     * @return 操作是否成功
     */
    public boolean deleteReceiverService(Long receiverServiceId) {
        Optional<UdpService> optReceiverService = getServiceById(receiverServiceId);

        if (!optReceiverService.isPresent()) {
            logger.error("删除接收端服务失败: 接收端服务不存在, ID: {}", receiverServiceId);
            return false;
        }

        UdpService receiverService = optReceiverService.get();

        if (!receiverService.isReceiver()) {
            logger.error("删除接收端服务失败: 指定的服务不是接收端服务, ID: {}", receiverServiceId);
            return false;
        }

        // 获取关联的发送端服务ID
        Long senderServiceId = receiverService.getSenderServiceId();

        // 删除服务
        boolean deleted = deleteService(receiverServiceId);

        if (deleted && senderServiceId != null) {
            try {
                // 从接收端目录加载发送端配置
                Optional<UdpService> optSenderService = loadSenderConfigFromReceiver(senderServiceId);
                if (optSenderService.isPresent()) {
                    UdpService senderService = optSenderService.get();
                    // 重置发送端配置状态
                    senderService.setDeploymentState("NEW");

                    // 更新接收端目录中的发送端配置文件
                    String senderConfigFileName = "sender_" + senderServiceId + ".xml";
                    File targetFile = new File(Paths.get(receiverConfigDir, senderConfigFileName).toString());

                    JAXBContext context = JAXBContext.newInstance(UdpService.class);
                    Marshaller marshaller = context.createMarshaller();
                    marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
                    marshaller.marshal(senderService, targetFile);
                }
            } catch (Exception e) {
                logger.error("更新发送端配置文件状态失败: ", e);
            }
        }

        return deleted;
    }

    /**
     * 更新发送端服务并同步到接收端
     * @param senderServiceId 发送端服务ID
     * @param updatedService 更新后的服务信息
     * @return 更新后的服务
     */
    public Optional<UdpService> updateSenderServiceAndSync(Long senderServiceId, UdpService updatedService) {
        // 先更新发送端服务
        Optional<UdpService> result = updateService(senderServiceId, updatedService);

        if (result.isPresent()) {
            UdpService senderService = result.get();

            // 设置部署状态为配置已更新
            senderService.setDeploymentState("CONFIG_UPDATED");
            updateService(senderServiceId, senderService);

            // 发送配置到接收端
            sendConfigToReceiver(senderServiceId);
        }

        return result;
    }

    public boolean updateServiceInfoStatus(Long id, String status, String uptimeInfo, String connectionCount, String trafficInfo) {
        Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(id);

        if (serviceInfoOpt.isPresent()) {
            UdpServiceInfo serviceInfo = serviceInfoOpt.get();
            serviceInfo.setStatus(status);
            serviceInfo.setUptimeInfo(uptimeInfo);
            serviceInfo.setConnectionCount(connectionCount);
            serviceInfo.setTrafficInfo(trafficInfo);

            // 更新服务列表中的状态
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                UdpServiceInfo existingService = servicesList.getServices().get(i);
                if (existingService.getId().equals(id)) {
                    servicesList.getServices().set(i, serviceInfo);
                    break;
                }
            }

            // 保存服务列表
            saveServicesList();
            return true;
        }

        return false;
    }

    // 检查服务配置文件路径
    public String getServiceConfigLocation(Long serviceId) {
        Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(serviceId);
        if (serviceInfoOpt.isPresent()) {
            UdpServiceInfo serviceInfo = serviceInfoOpt.get();
            if (serviceInfo.getNetwork() != null && serviceInfo.getNetwork() == 1) {
                // 接收端服务
                return Paths.get(receiverConfigDir.trim(), serviceId + ".xml").toString();
            } else {
                // 发送端服务
                return Paths.get(configDir.trim(), serviceId + ".xml").toString();
            }
        }
        return "未找到服务";
    }

    // 检查服务属性文件路径
    public String getServicePropertiesLocation(Long serviceId) {
        String propertiesPath = configDir + "/services/" + serviceId + ".properties";
        if(network!=null && network==1){
            propertiesPath = receiverConfigDir + "/services/" + serviceId + ".properties";
        }
        return propertiesPath;
    }



    /**
     * 手动强制重新加载服务列表
     * 可以在发现服务显示问题时调用此方法刷新
     */
    public void forceReloadServicesList() {
        System.out.println("强制重新加载服务列表...");
        // 清空现有服务列表
        servicesList.getServices().clear();
        // 重新加载服务列表
        loadServicesList();
        System.out.println("服务列表重新加载完成，当前服务数量: " + servicesList.getServices().size());
    }

    /**
     * 验证服务列表完整性并修复问题
     * 检查列表中的服务是否都有对应的配置文件，清理不存在的服务
     */
    public void validateAndFixServicesList() {
        System.out.println("开始验证服务列表完整性...");
        List<UdpServiceInfo> invalidServices = new ArrayList<>();

        // 检查每个服务的配置文件是否存在
        for (UdpServiceInfo serviceInfo : servicesList.getServices()) {
            String configFilePath = getServiceFilePath(serviceInfo.getId());
            File configFile = new File(configFilePath);

            if (!configFile.exists()) {
                System.out.println("服务 " + serviceInfo.getId() + "(" + serviceInfo.getName() + ") 配置文件不存在: " + configFilePath);
                invalidServices.add(serviceInfo);
            }
        }

        // 从列表中移除无效服务
        if (!invalidServices.isEmpty()) {
            System.out.println("从服务列表中移除 " + invalidServices.size() + " 个无效服务");
            servicesList.getServices().removeAll(invalidServices);
            saveServicesList();
        }

        System.out.println("服务列表验证完成，当前有效服务数量: " + servicesList.getServices().size());
    }

    /**
     * 获取服务详细信息
     * @param serviceId 服务ID
     * @return 包含详细信息的服务对象
     */
    public UdpServiceInfo getServiceDetailInfo(Long serviceId) {
        Optional<UdpService> serviceOpt = getServiceById(serviceId);
        Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(serviceId);

        if (serviceOpt.isPresent() && serviceInfoOpt.isPresent()) {
            UdpService service = serviceOpt.get();
            UdpServiceInfo serviceInfo = serviceInfoOpt.get();

            // 创建新的服务信息对象，包含详细信息
            UdpServiceInfo detailInfo = new UdpServiceInfo();
            detailInfo.fillDetailInfo(service);

            // 保留运行时信息
            detailInfo.setUptimeInfo(serviceInfo.getUptimeInfo());
            detailInfo.setConnectionCount(serviceInfo.getConnectionCount());
            detailInfo.setTrafficInfo(serviceInfo.getTrafficInfo());
            detailInfo.setUpdateStatus(serviceInfo.getUpdateStatus());

            return detailInfo;
        }

        return null;
    }

    // 添加部署服务的方法
    public boolean deployService(Long serviceId) {
        Optional<UdpService> serviceOpt = getServiceById(serviceId);
        if (!serviceOpt.isPresent()) {
            System.out.println("部署失败: 服务不存在，ID=" + serviceId);
            return false;
        }

        UdpService service = serviceOpt.get();
        return deployService(service);
    }

    /**
     * 查看服务详细信息，包括配置和运行状态
     * 此API方法用于前端调用，展示服务详情
     * @param serviceId 服务ID
     * @return 服务详情信息
     */
    public Map<String, Object> getServiceDetails(Long serviceId) {
        Map<String, Object> result = new HashMap<>();

        // 获取服务配置
        Optional<UdpService> serviceOpt = getServiceById(serviceId);
        if (!serviceOpt.isPresent()) {
            result.put("success", false);
            result.put("message", "服务不存在");
            return result;
        }

        UdpService service = serviceOpt.get();

        // 获取服务运行信息
        Optional<UdpServiceInfo> serviceInfoOpt = getServiceInfoById(serviceId);
        if (!serviceInfoOpt.isPresent()) {
            result.put("success", false);
            result.put("message", "服务信息不存在");
            return result;
        }

        UdpServiceInfo serviceInfo = serviceInfoOpt.get();

        // 组装详细信息
        result.put("success", true);
        result.put("id", service.getId());
        result.put("name", service.getName());
        result.put("status", service.getStatus());
        result.put("network", service.getNetwork());
        result.put("messageType", service.getMessageType());
        result.put("description", service.getDescription());
        result.put("auditOption", service.getAuditOption());
        result.put("createdAt", service.getCreatedAt());
        result.put("updatedAt", service.getUpdatedAt());

        // 运行状态信息
        Map<String, Object> statusInfo = new HashMap<>();
        statusInfo.put("uptime", serviceInfo.getUptimeInfo());
        statusInfo.put("connections", serviceInfo.getConnectionCount());
        statusInfo.put("traffic", serviceInfo.getTrafficInfo());
        statusInfo.put("updateStatus", serviceInfo.getUpdateStatus());
        result.put("statusInfo", statusInfo);

        // 根据网段类型添加不同的详细信息
        if (service.getNetwork() != null && service.getNetwork() == 1) {
            // 接收端服务特有信息
            Map<String, Object> receiverInfo = new HashMap<>();
            receiverInfo.put("serverIp", service.getServerIp());
            receiverInfo.put("serverPort", service.getServerPort());
            result.put("receiverInfo", receiverInfo);
        } else {
            // 发送端服务特有信息
            Map<String, Object> senderInfo = new HashMap<>();
            senderInfo.put("proxyIp", service.getProxyIp());
            senderInfo.put("proxyPort", service.getProxyPort());
            senderInfo.put("contentKeyCheck", service.getContentKeyCheck());
            senderInfo.put("contentKeywords", service.getContentKeywords());
            senderInfo.put("protocolFilter", service.getProtocolFilter());
            senderInfo.put("unpassDeal", service.getUnpassDeal());

            // 添加告警处理相关字段
            senderInfo.put("vehiclePlateCheck", service.getVehiclePlateCheck());
            senderInfo.put("idCardCheck", service.getIdCardCheck());
            senderInfo.put("crc16FormatCheck", service.getCrc16FormatCheck());
            senderInfo.put("asnFormatCheck", service.getAsnFormatCheck());

            result.put("senderInfo", senderInfo);
        }

        // 配置文件信息
        Map<String, String> configInfo = new HashMap<>();
        configInfo.put("configFilePath", getServiceFilePath(serviceId));
        configInfo.put("propertiesFilePath", getServicePropertiesLocation(serviceId));
        result.put("configInfo", configInfo);

        return result;
    }

    /**
     * 将配置文件发送到srcapps目录
     * @param serviceId 服务ID
     * @return 操作是否成功
     */
    public boolean sendConfigToSrcapps(Long serviceId) {
        try {
            // 获取服务
            Optional<UdpService> serviceOpt = getServiceById(serviceId);
            if (!serviceOpt.isPresent()) {
                logger.error("发送配置文件失败: 服务不存在, ID: {}", serviceId);
                return false;
            }

            // 定义srcapps目录路径
            File srcappsDirFile = new File(srcappsDir);
            if (!srcappsDirFile.exists()) {
                srcappsDirFile.mkdirs();
            }

            // 准备服务配置文件
            String serviceConfigFilePath = getServiceFilePath(serviceId);
            File serviceConfigFile = new File(serviceConfigFilePath);
            if (!serviceConfigFile.exists()) {
                logger.error("发送配置文件失败: 服务配置文件不存在: {}", serviceConfigFilePath);
                return false;
            }

            // 读取服务配置文件内容
            byte[] serviceConfigData = Files.readAllBytes(serviceConfigFile.toPath());

            // 准备全局配置文件
            String servicesListFilePath = getServicesListFilePath();
            File configFile = new File(servicesListFilePath);
            if (!configFile.exists()) {
                logger.error("发送配置文件失败: 全局配置文件不存在: {}", servicesListFilePath);
                return false;
            }

            // 读取全局配置文件内容
            byte[] configData = Files.readAllBytes(configFile.toPath());

            // 目标地址和端口
            String destIp = "*************";
            int destPort = 6666;

            try {
                // 创建UDP Socket
                DatagramSocket client = new DatagramSocket(6666);
                client.setBroadcast(true);
                InetAddress addr = InetAddress.getByName(destIp);

                // 为服务配置文件添加文件名前缀
                String serviceFilename = serviceId + ".xml";
                String serviceFilePrefix = "FILE:" + serviceFilename + "|";
                byte[] serviceFileHeader = serviceFilePrefix.getBytes();

                // 创建带前缀的数据包
                byte[] serviceDataWithHeader = new byte[serviceFileHeader.length + serviceConfigData.length];
                System.arraycopy(serviceFileHeader, 0, serviceDataWithHeader, 0, serviceFileHeader.length);
                System.arraycopy(serviceConfigData, 0, serviceDataWithHeader, serviceFileHeader.length, serviceConfigData.length);

                // 发送服务配置文件
                logger.info("开始通过UDP发送服务配置文件到接收端: {}:{}", destIp, destPort);
                DatagramPacket sendPacket = new DatagramPacket(serviceDataWithHeader, 0, serviceDataWithHeader.length, addr, destPort);
                client.setSoTimeout(10);
                client.send(sendPacket);
                logger.info("服务配置文件 {}.xml 已发送", serviceId);

                // 为全局配置文件添加文件名前缀
                String configFilename = "config.xml";
                String configFilePrefix = "FILE:" + configFilename + "|";
                byte[] configFileHeader = configFilePrefix.getBytes();

                // 创建带前缀的数据包
                byte[] configDataWithHeader = new byte[configFileHeader.length + configData.length];
                System.arraycopy(configFileHeader, 0, configDataWithHeader, 0, configFileHeader.length);
                System.arraycopy(configData, 0, configDataWithHeader, configFileHeader.length, configData.length);

                // 发送全局配置文件
                Thread.sleep(100); // 稍微延时确保接收方准备好
                sendPacket = new DatagramPacket(configDataWithHeader, 0, configDataWithHeader.length, addr, destPort);
                client.send(sendPacket);
                logger.info("全局配置文件 config.xml 已发送");

                client.close();

                logger.info("配置文件已通过UDP成功发送到接收端 {}:{}", destIp, destPort);

                // 同时保存到本地srcapps目录作为备份
                File destFile = new File(srcappsDir + "/" + serviceId + ".xml");
                Files.copy(serviceConfigFile.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                logger.info("服务配置文件已备份到本地srcapps目录: {}", destFile.getAbsolutePath());

                File destConfigFile = new File(srcappsDir + "/config.xml");
                Files.copy(configFile.toPath(), destConfigFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                logger.info("全局配置文件已备份到本地srcapps目录: {}", destConfigFile.getAbsolutePath());

                return true;
            } catch (Exception e) {
                logger.error("通过UDP发送配置文件失败", e);
                return false;
            }
        } catch (Exception e) {
            logger.error("发送配置文件到srcapps目录失败", e);
            return false;
        }
    }

    /**
     * 将config.xml配置文件发送到srcapps目录
     * @return 操作是否成功
     */
    public boolean sendConfigToSrcapps() {
        try {
            // 准备全局配置文件
            String servicesListFilePath = getServicesListFilePath();
            File configFile = new File(servicesListFilePath);
            if (!configFile.exists()) {
                logger.error("发送配置文件失败: 全局配置文件不存在: {}", servicesListFilePath);
                return false;
            }

            // 读取全局配置文件内容
            byte[] configData = Files.readAllBytes(configFile.toPath());

            // 目标地址和端口
            String destIp = "*************";
            int destPort = 6666;

            try {
                // 创建UDP Socket
                DatagramSocket client = new DatagramSocket(6666);
                client.setBroadcast(true);
                InetAddress addr = InetAddress.getByName(destIp);

                // 为全局配置文件添加文件名前缀
                String configFilename = "config.xml";
                String configFilePrefix = "FILE:" + configFilename + "|";
                byte[] configFileHeader = configFilePrefix.getBytes();

                // 创建带前缀的数据包
                byte[] configDataWithHeader = new byte[configFileHeader.length + configData.length];
                System.arraycopy(configFileHeader, 0, configDataWithHeader, 0, configFileHeader.length);
                System.arraycopy(configData, 0, configDataWithHeader, configFileHeader.length, configData.length);

                // 发送全局配置文件
                Thread.sleep(100); // 稍微延时确保接收方准备好
                DatagramPacket sendPacket = new DatagramPacket(configDataWithHeader, 0, configDataWithHeader.length, addr, destPort);
                client.send(sendPacket);
                logger.info("全局配置文件 config.xml 已发送");

                client.close();

                logger.info("配置文件已通过UDP成功发送到接收端 {}:{}", destIp, destPort);


                File destConfigFile = new File(srcappsDir + "/config.xml");
                Files.copy(configFile.toPath(), destConfigFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                logger.info("全局配置文件已备份到本地srcapps目录: {}", destConfigFile.getAbsolutePath());

                return true;
            } catch (Exception e) {
                logger.error("通过UDP发送配置文件失败", e);
                return false;
            }
        } catch (Exception e) {
            logger.error("发送配置文件到srcapps目录失败", e);
            return false;
        }
    }

    /**
     * 接收端同步状态枚举
     */
    public static enum ReceiverSyncStatus {
        SENDER_ADDED_NO_CONFIG("发送端新增服务，但配置文件未发送"),
        SENDER_ADDED_NO_SERVICE("发送端新增服务，但接收端未创建服务"),
        SENDER_ADDED_NO_CONFIG_SERVICE("发送端新增服务，但接收端未配置服务"),
        SENDER_MODIFIED_NO_CONFIG("发送端已经修改服务，但配置文件未发送"),
        SENDER_MODIFIED_NO_UPDATE("发送端已经修改服务，但接收端未更新配置服务"),
        SENDER_DELETED_RECEIVER_EXIST("发送端已经删除服务，但接收端未删除服务"),
        CONFIG_COMPLETED("配置已经完成");

        private String description;

        ReceiverSyncStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 从srcapps目录读取config.xml更新到接收端config.xml
     * @return 操作是否成功
     */
    public boolean syncConfigFromSrcapps() {
        System.out.println("开始从srcapps同步配置...");
        DebugLogger.log("开始从srcapps同步配置...");
        try {
            // 定义srcapps目录路径
            File srcappsDirFile = new File(srcappsDir);
            if (!srcappsDirFile.exists()) {
                System.out.println("同步配置失败: srcapps目录不存在");
                DebugLogger.log("同步配置失败: srcapps目录不存在");
                return false;
            }

            // 检查srcapps中的config.xml文件
            File srcConfigFile = new File(srcappsDir + "/config.xml");
            if (!srcConfigFile.exists()) {
                System.out.println("同步配置失败: srcapps中不存在config.xml文件");
                DebugLogger.log("同步配置失败: srcapps中不存在config.xml文件");
                return false;
            }

            // 确保接收端配置目录存在
            File receiverDir = new File(receiverConfigDir);
            if (!receiverDir.exists()) {
                receiverDir.mkdirs();
                System.out.println("创建接收端配置目录: " + receiverDir.getAbsolutePath());
                DebugLogger.log("创建接收端配置目录: " + receiverDir.getAbsolutePath());
            }

            // 读取srcapps中的服务列表
            UdpServiceList srcServicesList = loadServiceListFromFile(srcConfigFile);
            if (srcServicesList == null || srcServicesList.getServices() == null) {
                System.out.println("无法从srcapps读取服务列表");
                DebugLogger.log("无法从srcapps读取服务列表");
                return false;
            }

            int srcServicesCount = srcServicesList.getServices().size();
            System.out.println("从srcapps读取到 " + srcServicesCount + " 个服务");
            DebugLogger.log("从srcapps读取到 " + srcServicesCount + " 个服务");

            // 创建要保存到接收端的服务列表
            UdpServiceList receiverServicesList = new UdpServiceList();

            // 读取接收端现有的服务列表，避免覆盖已创建的服务
            File receiverConfigFile = new File(getReceiverServicesListFilePath());
            UdpServiceList existingReceiverList = null;
            if (receiverConfigFile.exists()) {
                existingReceiverList = loadServiceListFromFile(receiverConfigFile);
                System.out.println("读取到现有接收端服务: " +
                    (existingReceiverList != null && existingReceiverList.getServices() != null ?
                     existingReceiverList.getServices().size() : 0) + " 个");
                DebugLogger.log("读取到现有接收端服务: " +
                    (existingReceiverList != null && existingReceiverList.getServices() != null ?
                     existingReceiverList.getServices().size() : 0) + " 个");

                // 先将所有现有的服务添加到保存列表中，确保不丢失任何服务信息
                if (existingReceiverList != null && existingReceiverList.getServices() != null) {
                    receiverServicesList.getServices().addAll(existingReceiverList.getServices());
                }
            }

            // 创建存储已处理过的服务ID的集合，用于删除不存在的服务
            Set<Long> processedIds = new HashSet<>();

            // 处理srcapps中的每个服务
            for (UdpServiceInfo srcService : srcServicesList.getServices()) {
                // 记录ID为已处理
                processedIds.add(srcService.getId());

                // 只处理发送端服务
                if (srcService.getNetwork() != null && srcService.getNetwork() == 1) {
                    System.out.println("跳过srcapps中的接收端服务: " + srcService.getId() + " - " + srcService.getName());
                    DebugLogger.log("跳过srcapps中的接收端服务: " + srcService.getId() + " - " + srcService.getName());
                    continue;
                }

                System.out.println("处理发送端服务: " + srcService.getId() + " - " + srcService.getName());
                DebugLogger.log("处理发送端服务: " + srcService.getId() + " - " + srcService.getName());

                // 检查服务对应的XML配置文件是否存在
                File serviceXmlFile = new File(srcappsDir + "/" + srcService.getId() + ".xml");
                String syncStatus;

                if (serviceXmlFile.exists()) {
                    System.out.println("发送端服务配置文件存在: " + serviceXmlFile.getAbsolutePath());
                    DebugLogger.log("发送端服务配置文件存在: " + serviceXmlFile.getAbsolutePath());
                    syncStatus = ReceiverSyncStatus.SENDER_ADDED_NO_SERVICE.name();
                } else {
                    System.out.println("发送端服务配置文件不存在: " + serviceXmlFile.getAbsolutePath());
                    DebugLogger.log("发送端服务配置文件不存在: " + serviceXmlFile.getAbsolutePath());
                    syncStatus = ReceiverSyncStatus.SENDER_ADDED_NO_CONFIG.name();
                }

                // 检查接收端是否已有此服务，避免覆盖状态
                boolean serviceExists = false;
                boolean serviceConfigured = false;
                UdpServiceInfo existingService = null;

                if (existingReceiverList != null && existingReceiverList.getServices() != null) {
                    for (UdpServiceInfo existing : existingReceiverList.getServices()) {
                        if (existing.getId().equals(srcService.getId())) {
                            serviceExists = true;
                            existingService = existing;
                            // 检查服务是否已配置
                            File receiverServiceFile = new File(receiverConfigDir + "/" + srcService.getId() + ".xml");
                            serviceConfigured = receiverServiceFile.exists();
                            break;
                        }
                    }
                }
                 // 创建或更新接收端服务
                UdpServiceInfo receiverService = new UdpServiceInfo();
                // 如果服务已存在且已配置，保留其同步状态
                DebugLogger.log("接收端服务状态: " +srcService.getId()+ " " + receiverService.getStatus());
                if (serviceExists && serviceConfigured) {
                    DebugLogger.log("接收端服务已配置，保留原同步状态: " + existingService.getSyncStatus());
                    syncStatus = existingService.getSyncStatus();
                    receiverService.setStatus(existingService.getStatus());
                }else{
                    // 接收端服务创建时为未配置状态，只有编辑保存后才是已配置状态
                    receiverService.setStatus(serviceConfigured ? "configured" : "");
                }


                receiverService.setId(srcService.getId());
                receiverService.setName(srcService.getName());
                receiverService.setDescription(srcService.getDescription());
                receiverService.setNetwork(1); // 必须设置为接收端
                receiverService.setMessageType(srcService.getMessageType());
                receiverService.setCreatedAt(srcService.getCreatedAt());
                receiverService.setUpdatedAt(LocalDateTime.now());
                receiverService.setSyncStatus(syncStatus);

                // 检查是否需要替换列表中的服务
                boolean replaced = false;
                if (serviceExists) {
                    for (int i = 0; i < receiverServicesList.getServices().size(); i++) {
                        if (receiverServicesList.getServices().get(i).getId().equals(srcService.getId())) {
                            DebugLogger.log("替换接收端服务列表中的服务: " + srcService.getId());
                            receiverServicesList.getServices().set(i, receiverService);
                            replaced = true;
                            break;
                        }
                    }
                }

                // 如果没有替换，添加到接收端服务列表
                if (!replaced) {
                    DebugLogger.log("添加新的接收端服务: " + receiverService.getId() +
                        ", 同步状态: " + syncStatus +
                        ", 网络类型: " + receiverService.getNetwork());
                    receiverServicesList.getServices().add(receiverService);
                }
            }

            // 移除srcapps中不存在的服务，但保留接收端已配置的服务
            List<UdpServiceInfo> servicesToRemove = new ArrayList<>();
            for (UdpServiceInfo service : receiverServicesList.getServices()) {
                if (!processedIds.contains(service.getId())) {
                    // 检查是否是接收端特有的服务（例如手动创建的）
                    // 这里我们可以根据需要决定是否移除
                    DebugLogger.log("检测到srcapps中不存在的服务: " + service.getId() + ", 同步状态: " + service.getSyncStatus());


                    // 如果是已配置的服务，更新其同步状态为已删除
                    if ("SENDER_ADDED_NO_CONFIG".equals(service.getSyncStatus()) ||
                        "SENDER_ADDED_NO_SERVICE".equals(service.getSyncStatus())) {
                        DebugLogger.log("标记要移除的服务: " + service.getId());
                        servicesToRemove.add(service);
                    } else {
                        DebugLogger.log("更新已删除服务的同步状态: " + service.getId());
                        service.setSyncStatus(ReceiverSyncStatus.SENDER_DELETED_RECEIVER_EXIST.name());
                        service.setUpdatedAt(LocalDateTime.now());
                    }
                }
            }

            // 移除标记的服务
            if (!servicesToRemove.isEmpty()) {
                DebugLogger.log("从接收端配置文件中移除 " + servicesToRemove.size() + " 个服务");
                receiverServicesList.getServices().removeAll(servicesToRemove);
            }

            // 保存到接收端config.xml
            saveServiceListToFile(receiverServicesList, receiverConfigFile);

            System.out.println("已保存 " + receiverServicesList.getServices().size() +
                " 个接收端服务到 " + receiverConfigFile.getAbsolutePath());
            DebugLogger.log("已保存 " + receiverServicesList.getServices().size() +
                " 个接收端服务到 " + receiverConfigFile.getAbsolutePath());

            // 重新加载服务列表
            servicesList.getServices().clear();
            loadServicesList();
            DebugLogger.log("已重新加载服务列表");

            return true;
        } catch (Exception e) {
            System.out.println("从srcapps同步配置失败: " + e.getMessage());
            e.printStackTrace();
            DebugLogger.error("从srcapps同步配置失败", e);
            return false;
        }
    }

    // 从文件加载服务列表辅助方法
    private UdpServiceList loadServiceListFromFile(File file) {
        try {
            DebugLogger.log("开始从文件加载服务列表: " + file.getAbsolutePath());
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(file.getAbsolutePath());
            UdpServiceList serviceList = configXmlOperator.parseServicesToUdpServiceList();

            // 添加日志，打印加载的服务状态
            if (serviceList != null && serviceList.getServices() != null) {
                DebugLogger.log("成功加载服务列表，共 " + serviceList.getServices().size() + " 个服务");

                if (serviceList.getServices().size() <= 10) { // 避免日志过多
                    for (UdpServiceInfo service : serviceList.getServices()) {
                        DebugLogger.log("- 服务: ID=" + service.getId() +
                            ", 名称=" + service.getName() +
                            ", 网络类型=" + service.getNetwork() +
                            ", 同步状态=" + service.getSyncStatus());
                    }
                } else {
                    DebugLogger.log("服务列表中包含较多服务，不打印详细信息");
                }
            } else {
                DebugLogger.log("加载的服务列表为空或无服务");
            }

            return serviceList;
        } catch (Exception e) {
            DebugLogger.error("加载服务列表文件失败: " + file.getAbsolutePath(), e);
            return null;
        }
    }

    // 保存服务列表到文件辅助方法
    private boolean saveServiceListToFile(UdpServiceList serviceList, File file) {
        try {
            // 确保父目录存在
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 先打印要保存的服务列表状态
            DebugLogger.log("准备保存服务列表到文件: " + file.getAbsolutePath() + ", 服务数量: " +
                (serviceList != null && serviceList.getServices() != null ? serviceList.getServices().size() : 0));

            if (serviceList != null && serviceList.getServices() != null) {
                for (UdpServiceInfo service : serviceList.getServices()) {
                    DebugLogger.log("- 准备保存服务: ID=" + service.getId() +
                        ", 名称=" + service.getName() +
                        ", 同步状态=" + service.getSyncStatus());
                }
            }

            // 创建临时文件进行写入
            File tempFile = new File(file.getAbsolutePath() + ".tmp");

            // 使用ConfigXmlOperator保存服务列表
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(tempFile.getAbsolutePath());
            configXmlOperator.saveServiceList(serviceList);

            DebugLogger.log("服务列表已写入临时文件: " + tempFile.getAbsolutePath());

            // 验证临时文件
            boolean validTempFile = false;
            try {
                UdpServiceList verifyList = loadServiceListFromFile(tempFile);
                if (verifyList != null && verifyList.getServices() != null) {
                    DebugLogger.log("验证临时文件成功，读取到 " + verifyList.getServices().size() + " 个服务");
                    validTempFile = true;

                    // 验证特定服务的状态
                    for (UdpServiceInfo service : serviceList.getServices()) {
                        for (UdpServiceInfo verifyService : verifyList.getServices()) {
                            if (service.getId().equals(verifyService.getId())) {
                                if (!Objects.equals(service.getSyncStatus(), verifyService.getSyncStatus())) {
                                    DebugLogger.log("警告: 服务[" + service.getId() + "]的同步状态不匹配 - 原始: " +
                                                  service.getSyncStatus() + ", 验证: " + verifyService.getSyncStatus());
                                    validTempFile = false;
                                    break;
                                }
                            }
                        }
                        if (!validTempFile) break;
                    }
                } else {
                    DebugLogger.log("验证临时文件失败，无法读取服务列表");
                }
            } catch (Exception e) {
                DebugLogger.error("验证临时文件时出错", e);
                validTempFile = false;
            }

            if (!validTempFile) {
                DebugLogger.log("临时文件验证失败，尝试直接写入目标文件");
                // 直接写入目标文件
                ConfigXmlOperator directOperator = new ConfigXmlOperator(file.getAbsolutePath());
                directOperator.saveServiceList(serviceList);
            } else {
                // 如果目标文件存在，先备份
                if (file.exists()) {
                    File backupFile = new File(file.getAbsolutePath() + ".bak");
                    if (backupFile.exists()) {
                        backupFile.delete();
                    }
                    file.renameTo(backupFile);
                    DebugLogger.log("已备份原文件到: " + backupFile.getAbsolutePath());
                }

                // 重命名临时文件为目标文件
                if (file.exists()) {
                    file.delete();
                }
                boolean renamed = tempFile.renameTo(file);

                if (!renamed) {
                    DebugLogger.log("重命名临时文件失败，尝试复制内容");

                    // 如果重命名失败，尝试复制内容
                    ConfigXmlOperator fallbackOperator = new ConfigXmlOperator(file.getAbsolutePath());
                    fallbackOperator.saveServiceList(serviceList);
                    tempFile.delete(); // 删除临时文件
                }
            }

            // 最终验证
            UdpServiceList finalVerifyList = loadServiceListFromFile(file);
            if (finalVerifyList != null && finalVerifyList.getServices() != null) {
                DebugLogger.log("最终验证成功，保存的文件中有 " + finalVerifyList.getServices().size() + " 个服务");
                return true;
            } else {
                DebugLogger.log("最终验证失败，无法从保存的文件中读取服务列表");
                return false;
            }
        } catch (Exception e) {
            DebugLogger.error("保存服务列表文件失败: " + file.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 获取服务同步状态的描述
     * @param syncStatus 同步状态码
     * @return 同步状态描述
     */
    public String getSyncStatusDescription(String syncStatus) {
        if (syncStatus == null || syncStatus.isEmpty()) {
            return "";
        }

        try {
            ReceiverSyncStatus status = ReceiverSyncStatus.valueOf(syncStatus);
            return status.getDescription();
        } catch (IllegalArgumentException e) {
            return "未知状态";
        }
    }

    /**
     * 创建接收端服务配置
     * @param serviceId 服务ID
     * @return 是否创建成功
     */
    public boolean createReceiverServiceConfig(Long serviceId) {
        try {
            DebugLogger.log("开始创建接收端服务配置，ID: " + serviceId);

            // 获取服务信息
            UdpServiceInfo service = getReceiverServiceInfoById(serviceId).orElse(null);
            if (service == null) {
                DebugLogger.log("通过接收端配置文件未找到服务，尝试从内存中获取或创建新服务");

                service = getServiceInfoById(serviceId).orElse(null);
                if (service == null) {
                    DebugLogger.log("内存中也未找到服务，尝试创建新服务");

                    // 从发送端配置文件获取基本信息，创建一个新的接收端服务
                    File senderConfigFile = new File("./srcapps/" + serviceId + ".xml");
                    if (!senderConfigFile.exists() || !senderConfigFile.isFile()) {
                        DebugLogger.log("创建接收端服务配置失败：发送端配置文件不存在: " + senderConfigFile.getAbsolutePath());
                        return false;
                    }

                    try {
                        // 使用新的XML操作类加载发送端服务配置
                        ServiceXmlOperator xmlOperator = new ServiceXmlOperator(serviceId.toString());
                        UdpService senderService = xmlOperator.loadUdpService();

                        if (senderService != null) {
                            service = new UdpServiceInfo();
                            service.setId(serviceId);
                            service.setName(senderService.getName());
                            service.setDescription(senderService.getDescription());
                            service.setNetwork(1); // 设置为接收端
                            service.setMessageType(senderService.getMessageType());
                            service.setSyncStatus("SENDER_ADDED_NO_SERVICE");
                            service.setStatus("created");
                            service.setCreatedAt(LocalDateTime.now());
                            service.setUpdatedAt(LocalDateTime.now());

                            // 将新服务添加到内存中的服务列表
                            servicesList.getServices().add(service);
                            saveServicesList(); // 保存更新后的列表
                            DebugLogger.log("已创建新的接收端服务信息: " + serviceId);
                        } else {
                            DebugLogger.log("从发送端配置文件无法获取有效服务信息");
                            return false;
                        }
                    } catch (Exception e) {
                        DebugLogger.error("创建新接收端服务时出错", e);
                        return false;
                    }
                } else {
                    // 已找到服务，确保网络类型正确
                    service.setNetwork(1);
                    // 如果同步状态为null，设置默认值
                    if (service.getSyncStatus() == null) {
                        service.setSyncStatus("SENDER_ADDED_NO_SERVICE");
                    }
                    DebugLogger.log("已从内存中找到服务并设置为接收端: " + serviceId);
                }
            }

            // 记录当前同步状态供后续对比
            String originalSyncStatus = service.getSyncStatus();
            DebugLogger.log("创建接收端服务配置 - 原始同步状态: " + originalSyncStatus);

            // 再次确认是接收端服务
            if (service.getNetwork() != 1) {
                DebugLogger.log("服务网络类型不是接收端, ID: " + serviceId + ", 网络类型: " + service.getNetwork());

                // 设置为接收端
                service.setNetwork(1);
                boolean updated = updateServiceInfo(service);
                if (!updated) {
                    DebugLogger.log("无法将服务设置为接收端，但将继续尝试创建");
                } else {
                    DebugLogger.log("已将服务设置为接收端(network=1), ID: " + serviceId);
                }
            }

            // 检查同步状态 - 允许多种状态
            boolean validStatus = "SENDER_ADDED_NO_SERVICE".equals(service.getSyncStatus())
                || "SENDER_ADDED_NO_CONFIG".equals(service.getSyncStatus())
                || "SENDER_ADDED_NO_CONFIG_SERVICE".equals(service.getSyncStatus())
                || service.getSyncStatus() == null;

            if (!validStatus) {
                DebugLogger.log("创建接收端服务配置可能不适合当前同步状态，但将继续尝试: " + service.getSyncStatus());
            }

            // 获取发送端服务配置作为参考 - 使用新的XML操作类
            ServiceXmlOperator senderXmlOperator = new ServiceXmlOperator(Constant.SERVICE_PRE_PATH+"srcapps/"+serviceId.toString()+ Constant.SERVICE_FILE_EXT,serviceId.toString());
            UdpService senderService = senderXmlOperator.loadUdpService();

            if (senderService == null) {
                DebugLogger.log("创建接收端服务配置失败：发送端服务配置为空");
                return false;
            }

            // 创建新的UdpService对象作为接收端服务
            UdpService receiverService = new UdpService();
            receiverService.setId(serviceId);
            receiverService.setName(senderService.getName());
            receiverService.setDescription(senderService.getDescription());
            receiverService.setNetwork(1); // 设置为接收端
            receiverService.setMessageType(senderService.getMessageType());
            receiverService.setCreatedAt(LocalDateTime.now());
            receiverService.setUpdatedAt(LocalDateTime.now());
            receiverService.setStatus(""); // 接收端服务创建时为未配置状态，编辑保存后才是已配置状态

            // 从发送端复制组播相关设置
            receiverService.setMulticast(senderService.getMulticast());
            if (senderService.getMulticast() != null && senderService.getMulticast().equals("true")) {
                receiverService.setMulticastIp(senderService.getMulticastIp());
                receiverService.setSpecial_value(senderService.getSpecial_value());
            }

            // 设置接收端特有的属性
            receiverService.setServerIp("0.0.0.0"); // 默认绑定所有网卡

            // 设置地址映射
            receiverService.setSendaddrmap(senderService.getSendaddrmap());

            // 保存接收端服务配置 - 使用新的XML操作类
            ServiceXmlOperator receiverXmlOperator = new ServiceXmlOperator(serviceId.toString());
            receiverXmlOperator.saveUdpService(receiverService);


            //DebugLogger.log("已保存接收端服务配置: " + receiverXmlOperator.getConfigFilePath());

            // 更新同步状态 - 只更新接收端的单个服务，不影响其他服务
            boolean statusUpdated = updateReceiverServiceList(serviceId, "SENDER_ADDED_NO_CONFIG_SERVICE");
            DebugLogger.log("更新服务同步状态结果: " + (statusUpdated ? "成功" : "失败"));

            // 如果通过新方法更新失败，尝试使用旧方法
            if (!statusUpdated) {
                DebugLogger.log("通过新方法更新同步状态失败，尝试使用旧方法");

                // 使用旧方法更新
                statusUpdated = updateServiceSyncStatus(serviceId, "SENDER_ADDED_NO_CONFIG_SERVICE");
                DebugLogger.log("使用旧方法更新结果: " + (statusUpdated ? "成功" : "失败"));

                // 如果旧方法也失败，尝试直接更新文件
                if (!statusUpdated) {
                    DebugLogger.log("通过旧方法更新同步状态也失败，尝试直接更新文件");

                    // 加载接收端配置文件
                    File receiverConfigFile = new File(getReceiverServicesListFilePath());
                    try {
                        JAXBContext context = JAXBContext.newInstance(UdpServiceList.class);
                        UdpServiceList receiverList = null;

                        if (receiverConfigFile.exists()) {
                            // 文件存在，读取现有内容
                            Unmarshaller unmarshaller = context.createUnmarshaller();
                            receiverList = (UdpServiceList) unmarshaller.unmarshal(receiverConfigFile);
                        } else {
                            // 文件不存在，创建新的
                            receiverList = new UdpServiceList();
                        }

                        if (receiverList.getServices() == null) {
                            receiverList.setServices(new ArrayList<>());
                        }

                        // 查找服务
                        boolean found = false;
                        for (UdpServiceInfo serviceInfo : receiverList.getServices()) {
                            if (serviceInfo.getId().equals(serviceId)) {
                                serviceInfo.setSyncStatus("SENDER_ADDED_NO_CONFIG_SERVICE");
                                serviceInfo.setNetwork(1); // 确保是接收端
                                found = true;
                                DebugLogger.log("在接收端配置文件中找到并更新了服务: " + serviceId);
                                break;
                            }
                        }

                        // 如果没找到，添加新服务
                        if (!found) {
                            UdpServiceInfo newService = new UdpServiceInfo();
                            newService.setId(serviceId);
                            newService.setName(receiverService.getName());
                            newService.setDescription(receiverService.getDescription());
                            newService.setNetwork(1);
                            newService.setStatus("configured");
                            newService.setSyncStatus("SENDER_ADDED_NO_CONFIG_SERVICE");
                            newService.setCreatedAt(LocalDateTime.now());
                            newService.setUpdatedAt(LocalDateTime.now());

                            receiverList.getServices().add(newService);
                            DebugLogger.log("服务未在接收端配置文件中找到，已添加新服务: " + serviceId);
                        }

                        // 保存更新后的配置
                        Marshaller marshaller = context.createMarshaller();
                        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
                        marshaller.marshal(receiverList, receiverConfigFile);

                        DebugLogger.log("已直接更新接收端配置文件，服务ID: " + serviceId);
                        statusUpdated = true;
                    } catch (Exception e) {
                        DebugLogger.error("直接更新接收端配置文件失败", e);
                    }
                }
            }

            // 还需要更新内存中的服务列表中对应服务的状态
            boolean memoryUpdated = false;
            for (UdpServiceInfo existingService : servicesList.getServices()) {
                if (existingService.getId().equals(serviceId)) {
                    existingService.setSyncStatus("SENDER_ADDED_NO_CONFIG_SERVICE");
                    existingService.setStatus(""); // 接收端服务创建时为未配置状态
                    existingService.setUpdatedAt(LocalDateTime.now());
                    memoryUpdated = true;
                    DebugLogger.log("已更新内存中服务的同步状态: " + serviceId);
                    break;
                }
            }

            if (!memoryUpdated) {
                // 如果内存中没有找到该服务，尝试加载它
                loadServiceById(serviceId);
                DebugLogger.log("尝试从文件加载服务到内存: " + serviceId);
            }

            // 强制保存服务列表，确保状态被正确保存
            saveServicesList();

            // 检查最终状态，与开始状态对比
            Optional<UdpServiceInfo> finalServiceInfo = getServiceInfoById(serviceId);
            if (finalServiceInfo.isPresent()) {
                String finalSyncStatus = finalServiceInfo.get().getSyncStatus();
                DebugLogger.log("创建接收端服务配置完成 - 最终同步状态: " + finalSyncStatus +
                    ", 原始状态: " + originalSyncStatus);

                // 如果状态未改变，强制最后一次尝试
                if (originalSyncStatus != null && originalSyncStatus.equals(finalSyncStatus) &&
                    !"SENDER_ADDED_NO_CONFIG_SERVICE".equals(finalSyncStatus)) {
                    DebugLogger.log("警告: 同步状态未发生变化，进行最后一次强制更新");

                    // 直接更新内存中的服务
                    finalServiceInfo.get().setSyncStatus("SENDER_ADDED_NO_CONFIG_SERVICE");

                    // 最后一次保存
                    saveServicesList();
                    DebugLogger.log("已完成最后一次强制状态更新");
                }
            }

            // 强制重新加载服务列表，确保内存中的状态与文件同步
            loadServicesList();
            DebugLogger.log("已强制重新加载服务列表，确保状态同步");

            // 再次强制执行同步操作，确保接收端服务状态正确
            syncConfigFromSrcapps();
            loadServicesList();
            DebugLogger.log("已完成强制同步，确保接收端服务状态正确");

            DebugLogger.log("接收端服务配置创建成功，ID: " + serviceId);
            return true;
        } catch (Exception e) {
            DebugLogger.error("创建接收端服务配置时发生错误", e);
            return false;
        }
    }

    /**
     * 加载单个服务并更新服务列表，避免重新加载整个列表
     * @param serviceId 服务ID
     */
    private void loadServiceById(Long serviceId) {
        try {
            DebugLogger.log("开始加载单个服务: " + serviceId);

            // 先尝试加载接收端服务配置文件，获取最新的同步状态
            File receiverConfigFile = new File(getReceiverServicesListFilePath());
            if (receiverConfigFile.exists()) {
                try {
                    UdpServiceList receiverList = loadServiceListFromFile(receiverConfigFile);
                    if (receiverList != null && receiverList.getServices() != null) {
                        for (UdpServiceInfo receiverService : receiverList.getServices()) {
                            if (receiverService.getId().equals(serviceId)) {
                                DebugLogger.log("从接收端配置文件找到服务: " + serviceId +
                                    ", 同步状态: " + receiverService.getSyncStatus());

                                // 加载完整的服务配置
                                Optional<UdpService> serviceOpt = loadServiceConfig(serviceId);
                                if (serviceOpt.isPresent()) {
                                    UdpService service = serviceOpt.get();

                                    // 更新服务列表中对应的服务信息
                                    boolean found = false;
                                    for (int i = 0; i < servicesList.getServices().size(); i++) {
                                        UdpServiceInfo existingService = servicesList.getServices().get(i);
                                        if (existingService.getId().equals(serviceId)) {
                                            // 创建更新后的服务信息
                                            UdpServiceInfo updatedInfo = new UdpServiceInfo(service);
                                            // 保留接收端配置文件中的同步状态
                                            updatedInfo.setSyncStatus(receiverService.getSyncStatus());
                                            servicesList.getServices().set(i, updatedInfo);
                                            found = true;
                                            DebugLogger.log("已更新服务列表中的服务信息，同步状态: " + updatedInfo.getSyncStatus());
                                            break;
                                        }
                                    }

                                    // 如果没找到服务，添加到列表
                                    if (!found) {
                                        UdpServiceInfo newInfo = new UdpServiceInfo(service);
                                        // 设置接收端配置文件中的同步状态
                                        newInfo.setSyncStatus(receiverService.getSyncStatus());
                                        servicesList.getServices().add(newInfo);
                                        DebugLogger.log("已添加新服务到服务列表，同步状态: " + newInfo.getSyncStatus());
                                    }

                                    return;
                                } else {
                                    DebugLogger.log("无法加载服务配置: " + serviceId);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    DebugLogger.error("从接收端配置文件加载服务信息时出错", e);
                }
            }

            // 如果从接收端配置文件找不到，尝试从服务配置文件直接加载
            DebugLogger.log("从服务配置文件加载: " + serviceId);
            Optional<UdpService> serviceOpt = loadServiceConfig(serviceId);
            if (serviceOpt.isPresent()) {
                UdpService service = serviceOpt.get();

                // 更新服务列表中对应的服务信息
                boolean found = false;
                for (int i = 0; i < servicesList.getServices().size(); i++) {
                    UdpServiceInfo existingService = servicesList.getServices().get(i);
                    if (existingService.getId().equals(serviceId)) {
                        // 更新现有服务信息
                        UdpServiceInfo updatedInfo = new UdpServiceInfo(service);
                        // 保持原有同步状态，如果没有则设置为已完成
                        if (existingService.getSyncStatus() != null) {
                            updatedInfo.setSyncStatus(existingService.getSyncStatus());
                        } else {
                            updatedInfo.setSyncStatus("CONFIG_COMPLETED");
                        }
                        servicesList.getServices().set(i, updatedInfo);
                        found = true;
                        DebugLogger.log("已更新服务列表中的服务信息(从服务配置): " + serviceId);
                        break;
                    }
                }

                // 如果没找到服务，添加到列表
                if (!found) {
                    UdpServiceInfo newInfo = new UdpServiceInfo(service);
                    newInfo.setSyncStatus("CONFIG_COMPLETED"); // 假设配置已完成
                    servicesList.getServices().add(newInfo);
                    DebugLogger.log("已添加新服务到服务列表(从服务配置): " + serviceId);
                }
            } else {
                DebugLogger.log("无法加载服务配置文件: " + serviceId);
            }
        } catch (Exception e) {
            DebugLogger.error("加载单个服务时发生错误", e);
        }
    }

    /**
     * 更新服务的同步状态
     * @param serviceId 服务ID
     * @param syncStatus 要设置的同步状态
     * @return 操作是否成功
     */
    private boolean updateServiceSyncStatus(Long serviceId, String syncStatus) {
        try {
            DebugLogger.log("更新服务同步状态，ID: " + serviceId + ", 新状态: " + syncStatus);

            // 获取接收端服务列表文件
            File receiverConfigFile = new File(getReceiverServicesListFilePath());
            if (!receiverConfigFile.exists() || !receiverConfigFile.isFile()) {
                DebugLogger.log("更新服务同步状态失败：接收端配置文件不存在");
                return false;
            }

            // 加载接收端服务列表
            UdpServiceList receiverServices = loadServiceListFromFile(receiverConfigFile);
            if (receiverServices == null || receiverServices.getServices() == null) {
                DebugLogger.log("更新服务同步状态失败：无法加载接收端服务列表");
                return false;
            }

            DebugLogger.log("当前接收端服务列表有 " + receiverServices.getServices().size() + " 个服务");

            // 查找目标服务
            boolean found = false;
            for (UdpServiceInfo service : receiverServices.getServices()) {
                DebugLogger.log("检查服务: ID=" + service.getId() + ", 同步状态=" + service.getSyncStatus());
                if (service.getId().equals(serviceId)) {
                    DebugLogger.log("找到匹配服务，准备更新同步状态。原状态: " + service.getSyncStatus());
                    service.setSyncStatus(syncStatus);
                    service.setUpdatedAt(LocalDateTime.now());
                    found = true;
                    DebugLogger.log("已更新服务同步状态为: " + syncStatus);
                    break;
                }
            }

            if (!found) {
                DebugLogger.log("更新服务同步状态失败：在接收端服务列表中未找到指定服务");

                // 如果接收端配置文件中未找到此服务，我们可以从全局服务列表中获取并添加到接收端配置文件
                boolean foundInGlobal = false;
                for (UdpServiceInfo service : servicesList.getServices()) {
                    if (service.getId().equals(serviceId)) {
                        // 创建新的服务信息对象，避免直接引用
                        UdpServiceInfo newService = new UdpServiceInfo();
                        newService.setId(serviceId);
                        newService.setName(service.getName());
                        newService.setDescription(service.getDescription());
                        newService.setMessageType(service.getMessageType());
                        newService.setNetwork(1); // 确保是接收端服务
                        newService.setCreatedAt(service.getCreatedAt() != null ? service.getCreatedAt() : LocalDateTime.now());
                        newService.setUpdatedAt(LocalDateTime.now());
                        newService.setStatus("created");
                        newService.setSyncStatus(syncStatus);

                        // 添加到接收端服务列表
                        receiverServices.getServices().add(newService);
                        foundInGlobal = true;
                        DebugLogger.log("从全局服务列表中找到服务并添加到接收端配置文件: " + serviceId);
                        break;
                    }
                }

                if (!foundInGlobal) {
                    DebugLogger.log("在全局服务列表中也未找到指定服务，无法更新同步状态: " + serviceId);
                    return false;
                }
            }

            // 保存更新后的接收端服务列表 - 直接保存，不再尝试从文件中重新加载和合并
            try {
                // 确保父目录存在
                File parentDir = receiverConfigFile.getParentFile();
                if (!parentDir.exists()) {
                    parentDir.mkdirs();
                }

                // 直接将修改后的服务列表保存到文件
                JAXBContext context = JAXBContext.newInstance(UdpServiceList.class);
                Marshaller marshaller = context.createMarshaller();
                marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
                marshaller.marshal(receiverServices, receiverConfigFile);

                // 验证是否保存成功
                boolean verified = false;
                UdpServiceList verifyList = loadServiceListFromFile(receiverConfigFile);
                if (verifyList != null && verifyList.getServices() != null) {
                    for (UdpServiceInfo s : verifyList.getServices()) {
                        if (s.getId().equals(serviceId)) {
                            verified = syncStatus.equals(s.getSyncStatus());
                            DebugLogger.log("验证服务同步状态: ID=" + s.getId() +
                                ", 保存状态=" + s.getSyncStatus() +
                                ", 期望状态=" + syncStatus +
                                ", 验证结果: " + (verified ? "成功" : "失败"));
                            break;
                        }
                    }
                }

                if (verified) {
                    DebugLogger.log("已直接保存更新后的接收端服务列表，验证成功");
                    return true;
                } else {
                    DebugLogger.log("已保存更新后的接收端服务列表，但验证失败");
                    return false;
                }
            } catch (Exception e) {
                DebugLogger.error("直接保存接收端服务列表时发生错误", e);
                return false;
            }
        } catch (Exception e) {
            DebugLogger.error("更新服务同步状态时发生错误", e);
            return false;
        }
    }

    /**
     * 更新服务信息对象
     * @param serviceInfo 要更新的服务信息
     * @return 更新是否成功
     */
    public boolean updateServiceInfo(UdpServiceInfo serviceInfo) {
        if (serviceInfo == null || serviceInfo.getId() == null) {
            logger.error("更新服务信息失败: 无效的服务信息");
            return false;
        }

        try {
            // 查找并更新服务列表中的服务信息
            boolean found = false;
            for (int i = 0; i < servicesList.getServices().size(); i++) {
                UdpServiceInfo existingService = servicesList.getServices().get(i);
                if (existingService.getId().equals(serviceInfo.getId())) {
                    // 只更新网络类型和同步状态，保留其他信息
                    existingService.setNetwork(serviceInfo.getNetwork());
                    existingService.setSyncStatus(serviceInfo.getSyncStatus());

                    // 更新服务列表
                    servicesList.getServices().set(i, existingService);
                    found = true;
                    break;
                }
            }

            if (!found) {
                logger.error("更新服务信息失败: 未找到ID为{}的服务", serviceInfo.getId());
                return false;
            }

            // 保存更新后的服务列表
            saveServicesList();

            logger.info("服务信息已更新 - ID: {}, 网络类型: {}, 同步状态: {}",
                        serviceInfo.getId(), serviceInfo.getNetwork(), serviceInfo.getSyncStatus());

            return true;
        } catch (Exception e) {
            logger.error("更新服务信息时发生错误", e);
            return false;
        }
    }

    /**
     * 从接收端配置文件中获取服务信息
     * 这个方法确保从接收端config.xml文件获取最新状态，而不是从内存中获取
     * @param serviceId 服务ID
     * @return 接收端服务信息
     */
    public Optional<UdpServiceInfo> getReceiverServiceInfoById(Long serviceId) {
        DebugLogger.log("从接收端配置文件获取服务信息: " + serviceId);

        // 使用ConfigXmlOperator从新的配置文件格式中获取服务信息
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        UdpServiceList receiverList = configXmlOperator.parseServicesToUdpServiceList();

        if (receiverList != null && receiverList.getServices() != null) {
            for (UdpServiceInfo serviceInfo : receiverList.getServices()) {
                if (serviceInfo.getId().equals(serviceId)) {
                    // 确保网络类型正确设置为接收端
                    if (serviceInfo.getNetwork() == null) {
                        serviceInfo.setNetwork(1);
                    }
                    DebugLogger.log("从接收端配置文件成功获取服务: ID=" + serviceId +
                        ", 网络类型=" + serviceInfo.getNetwork() +
                        ", 同步状态=" + serviceInfo.getSyncStatus());
                    return Optional.of(serviceInfo);
                }
            }
        }

        // 如果接收端配置文件中没有找到，尝试从内存中获取并确保正确的网络类型
        Optional<UdpServiceInfo> serviceFromMemory = getServiceInfoById(serviceId);
        if (serviceFromMemory.isPresent()) {
            UdpServiceInfo serviceInfo = serviceFromMemory.get();
            // 确保网络类型正确设置为接收端
            serviceInfo.setNetwork(1);

            // 如果同步状态为null，设置为默认值
            if (serviceInfo.getSyncStatus() == null) {
                serviceInfo.setSyncStatus("SENDER_ADDED_NO_SERVICE");
                DebugLogger.log("服务同步状态为null，已设置为SENDER_ADDED_NO_SERVICE");
            }

            DebugLogger.log("从内存中获取服务: ID=" + serviceId +
                ", 网络类型=" + serviceInfo.getNetwork() +
                ", 同步状态=" + serviceInfo.getSyncStatus());
            return Optional.of(serviceInfo);
        }

        DebugLogger.log("未找到接收端服务: " + serviceId);
        return Optional.empty();
    }

    /**
     * 更新接收端配置文件中的服务状态
     * 与syncConfigFromSrcapps不同，这个方法只更新接收端配置文件中的服务状态，不会从srcapps同步其他配置
     * @param serviceId 服务ID
     * @param newStatus 新的同步状态
     * @return 是否更新成功
     */
    public boolean updateReceiverServiceList(Long serviceId, String newStatus) {
        DebugLogger.log("开始更新接收端服务配置文件，服务ID: " + serviceId + ", 新状态: " + newStatus);

        try {
            // 获取接收端配置文件
            File receiverConfigFile = new File(getReceiverServicesListFilePath());
            if (!receiverConfigFile.exists()) {
                DebugLogger.log("接收端配置文件不存在: " + receiverConfigFile.getAbsolutePath());
                return false;
            }

            // 加载接收端服务列表
            UdpServiceList receiverServicesList = loadServiceListFromFile(receiverConfigFile);
            if (receiverServicesList == null || receiverServicesList.getServices() == null) {
                DebugLogger.log("无法加载接收端服务列表或列表为空");
                return false;
            }

            DebugLogger.log("已加载接收端服务列表，共 " + receiverServicesList.getServices().size() + " 个服务");

            // 查找指定服务
            boolean found = false;
            for (UdpServiceInfo service : receiverServicesList.getServices()) {
                if (service.getId().equals(serviceId)) {
                    DebugLogger.log("找到服务: ID=" + serviceId + ", 当前同步状态=" + service.getSyncStatus() + ", 将更新为: " + newStatus);
                    if(newStatus!=null&&!"".equals(newStatus)){
                        service.setSyncStatus(newStatus);
                    }
                    service.setUpdatedAt(java.time.LocalDateTime.now());
                    found = true;
                    break;
                }
            }

            if (!found) {
                DebugLogger.log("在接收端配置文件中未找到服务: " + serviceId);

                // 尝试从内存中获取服务信息
                Optional<UdpServiceInfo> serviceOpt = getServiceInfoById(serviceId);
                if (serviceOpt.isPresent()) {
                    UdpServiceInfo service = serviceOpt.get();

                    // 创建新的服务信息对象添加到接收端列表
                    UdpServiceInfo newService = new UdpServiceInfo();
                    newService.setId(service.getId());
                    newService.setName(service.getName());
                    newService.setDescription(service.getDescription());
                    newService.setMessageType(service.getMessageType());
                    newService.setNetwork(1); // 设置为接收端
                    newService.setCreatedAt(service.getCreatedAt() != null ? service.getCreatedAt() : java.time.LocalDateTime.now());
                    newService.setUpdatedAt(java.time.LocalDateTime.now());
                    newService.setStatus("created");
                    newService.setSyncStatus(newStatus);

                    receiverServicesList.getServices().add(newService);
                    DebugLogger.log("已从内存中找到服务并添加到接收端配置文件: " + serviceId);
                    found = true;
                } else {
                    DebugLogger.log("内存中也未找到服务: " + serviceId);
                    return false;
                }
            }

            if (found) {
                // 保存更新后的接收端服务列表
                if (saveServiceListToFile(receiverServicesList, receiverConfigFile)) {
                    DebugLogger.log("已成功更新接收端配置文件中的服务状态: " + serviceId + " -> " + newStatus);
                    return true;
                } else {
                    DebugLogger.log("保存接收端配置文件失败");
                    return false;
                }
            }

            return false;
        } catch (Exception e) {
            DebugLogger.error("更新接收端服务列表时出错", e);
            return false;
        }
    }

    /**
     * 从主服务列表中获取发送端服务信息
     * 这个方法确保从主config.xml文件获取最新状态，而不是从内存中获取
     * @param serviceId 服务ID
     * @return 发送端服务信息
     */
    public Optional<UdpServiceInfo> getSenderServiceInfoById(Long serviceId) {
        DebugLogger.log("从主服务列表获取发送端服务信息: " + serviceId);

        // 加载主服务列表
        String mainFilePath = getServicesListFilePath();
        File mainFile = new File(mainFilePath);

        if (mainFile.exists()) {
            try {
                JAXBContext context = JAXBContext.newInstance(UdpServiceList.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                UdpServiceList mainList = (UdpServiceList) unmarshaller.unmarshal(mainFile);

                if (mainList != null && mainList.getServices() != null) {
                    for (UdpServiceInfo serviceInfo : mainList.getServices()) {
                        if (serviceInfo.getId().equals(serviceId)) {
                            // 检查网络类型是否为发送端或为null（默认为发送端）
                            if (serviceInfo.getNetwork() == null || serviceInfo.getNetwork() == 0) {
                                // 确保网络类型正确设置为发送端
                                serviceInfo.setNetwork(0);
                                DebugLogger.log("成功获取发送端服务: ID=" + serviceId +
                                    ", 网络类型=" + serviceInfo.getNetwork());
                                return Optional.of(serviceInfo);
                            } else {
                                DebugLogger.log("ID=" + serviceId + " 的服务网络类型=" +
                                    serviceInfo.getNetwork() + "，不是发送端服务");
                            }
                        }
                    }
                }
            } catch (Exception e) {
                DebugLogger.error("从主服务列表获取发送端服务信息失败", e);
            }
        }

        DebugLogger.log("未找到ID为" + serviceId + "的发送端服务");
        return Optional.empty();
    }

    /**
     * 从接收端配置文件获取完整的服务配置
     * @param serviceId 服务ID
     * @return 接收端服务配置
     */
    public Optional<UdpService> getReceiverServiceById(Long serviceId) {
        DebugLogger.log("尝试从接收端配置文件获取完整服务配置: " + serviceId);

        // 首先检查服务是否存在于接收端配置文件中
        Optional<UdpServiceInfo> receiverInfoOpt = getReceiverServiceInfoById(serviceId);
        if (!receiverInfoOpt.isPresent()) {
            DebugLogger.log("接收端服务信息不存在，ID: " + serviceId);
            return Optional.empty();
        }

        // 接收端服务配置文件路径 - 根据saveServiceConfig方法中的逻辑，
        // 接收端服务配置文件存储在receiverConfigDir目录下，文件名为"服务ID.xml"
        String receiverConfigFilePath = Paths.get(receiverConfigDir.trim(), serviceId + ".xml").toString();
        File receiverConfigFile = new File(receiverConfigFilePath);

        DebugLogger.log("尝试从路径加载接收端服务配置: " + receiverConfigFilePath);

        if (receiverConfigFile.exists()) {
            try {
                JAXBContext context = JAXBContext.newInstance(UdpService.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                UdpService receiverService = (UdpService) unmarshaller.unmarshal(receiverConfigFile);

                // 确保网络类型正确设置为接收端
                receiverService.setNetwork(1);

                DebugLogger.log("成功从接收端配置文件加载服务配置: ID=" + serviceId +
                    ", 服务器IP=" + receiverService.getServerIp() +
                    ", 服务器端口=" + receiverService.getServerPort());
                return Optional.of(receiverService);
            } catch (Exception e) {
                DebugLogger.error("从接收端配置文件加载服务配置失败", e);
            }
        } else {
            DebugLogger.log("接收端配置文件不存在: " + receiverConfigFilePath);

            // 尝试在主配置目录查找
            String mainConfigFilePath = Paths.get(configDir.trim(), serviceId + ".xml").toString();
            File mainConfigFile = new File(mainConfigFilePath);

            DebugLogger.log("尝试从主配置目录加载接收端服务: " + mainConfigFilePath);

            if (mainConfigFile.exists()) {
                try {
                    JAXBContext context = JAXBContext.newInstance(UdpService.class);
                    Unmarshaller unmarshaller = context.createUnmarshaller();
                    UdpService receiverService = (UdpService) unmarshaller.unmarshal(mainConfigFile);

                    // 确保网络类型正确设置为接收端
                    receiverService.setNetwork(1);

                    DebugLogger.log("从主配置目录成功加载接收端服务: ID=" + serviceId +
                        ", 服务器IP=" + receiverService.getServerIp() +
                        ", 服务器端口=" + receiverService.getServerPort());
                    return Optional.of(receiverService);
                } catch (Exception e) {
                    DebugLogger.error("从主配置目录加载接收端服务失败", e);
                }
            } else {
                DebugLogger.log("在主配置目录中也未找到接收端服务配置: " + mainConfigFilePath);
            }
        }

        // 如果仍然无法找到，尝试从loadServiceConfig方法加载
        DebugLogger.log("尝试使用通用方法loadServiceConfig加载接收端服务: " + serviceId);
        Optional<UdpService> serviceOpt = loadServiceConfig(serviceId);
        if (serviceOpt.isPresent()) {
            UdpService service = serviceOpt.get();
            service.setNetwork(1);
            DebugLogger.log("使用通用方法成功加载接收端服务: ID=" + serviceId +
                ", 服务器IP=" + service.getServerIp() +
                ", 服务器端口=" + service.getServerPort());
            return Optional.of(service);
        }

        DebugLogger.log("无法加载接收端服务配置: " + serviceId);
        return Optional.empty();
    }

    /**
     * 获取配置目录路径
     * @return 配置目录路径
     */
    public String getConfigDirectory() {
        return receiverConfigDir;
    }


}