package com.udpproxy.repository;

import com.udpproxy.model.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface AuditRepository extends JpaRepository<AuditLog, Long> {
    
    @Query("SELECT a FROM AuditLog a WHERE (:serviceId IS NULL OR a.serviceId = :serviceId) " +
           "AND (:clientIp IS NULL OR :clientIp = '' OR a.clientIp = :clientIp) " +
           "AND (:beginTime IS NULL OR a.beginTime >= :beginTime) " +
           "AND (:endTime IS NULL OR a.beginTime <= :endTime) " +
           "ORDER BY a.beginTime DESC")
    Page<AuditLog> findBySearchCriteria(
            @Param("serviceId") Long serviceId,
            @Param("clientIp") String clientIp,
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);
} 