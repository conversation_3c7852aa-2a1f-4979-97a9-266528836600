package com.example.udp.service.impl;

import com.example.udp.model.LicenseModule;
import com.example.udp.service.LicenseService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.io.BufferedReader;
import java.io.FileReader;
import java.util.Map;
import java.util.HashMap;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 引入第三方授权包
import com.unimassystem.main.License;

/**
 * 授权服务实现类
 */
@Service
public class LicenseServiceImpl implements LicenseService {

    private static final Logger logger = LoggerFactory.getLogger(LicenseServiceImpl.class);
    private static final String CFCARD_PATH = "/boot/cfcard";
    private static final String BASE_MODEL = "baseModel";
    private static final String BASE_MODEL_NAME = "基础授权";
    private static final String TRIAL_LICENSE_FILE = "/opt/unimas/license/.license_test";


    @Override
    public String getHardwareSerial() {
        try {
            // 使用License.getSystemDevCode()获取硬件序列码
            License license = new License();
            String systemCode = license.getSystemDevCode();
            logger.info("硬件序列码: {}", systemCode);
            return systemCode;
        } catch (Exception e) {
            logger.error("获取硬件序列码失败: {}", e.getMessage(), e);
            
            // 如果出错，尝试从/boot/cfcard文件中读取devid作为备用方案
            try {
                Path cfcardPath = Paths.get(CFCARD_PATH);
                if (Files.exists(cfcardPath)) {
                    try (BufferedReader reader = new BufferedReader(new FileReader(CFCARD_PATH))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.startsWith("devid=")) {
                                return line.substring(6).trim();
                            }
                        }
                    }
                }
            } catch (Exception ex) {
                logger.error("从配置文件获取硬件序列码失败: {}", ex.getMessage(), ex);
            }
            
            // 如果上述方法都失败，返回配置文件中设置的默认值
            return "";
        }
    }

    @Override
    public List<LicenseModule> getBaseModules() {
        List<LicenseModule> modules = new ArrayList<>();
        
        try {
            // 创建License实例
            License license = new License();
            
            // 检查基础授权模块状态
            boolean isOpen = license.isOpenModel(BASE_MODEL);
            boolean isValid = license.isValidTime(BASE_MODEL);
            
            String status = "inactive";
            String startDate = "";
            String endDate = "";
            
            if (isOpen) {
                if (isValid) {
                    status = "active";
                } else {
                    status = "expired";
                }
                
                // 尝试获取授权时间信息
                Map<String, Map<String, String>> map = license.getProduceInfo();
                if (map != null && map.get(BASE_MODEL) != null) {
                    Map<String, String> time = map.get(BASE_MODEL);
                    if (time != null) {
                        String sTime = time.get("sTime");
                        String eTime = time.get("eTime");
                        
                        if (sTime != null && !sTime.isEmpty()) {
                            startDate = sTime;
                        }
                        
                        if (eTime != null && !eTime.isEmpty()) {
                            endDate = eTime;
                            if(eTime.equals("0")){
                                endDate = "永久";
                            }

                        }
                    }
                }
            }
            
            // 创建基础授权模块信息
            modules.add(new LicenseModule(
                BASE_MODEL,
                BASE_MODEL_NAME,
                startDate,
                endDate,
                status,
                "基础授权模块"
            ));
            
        } catch (Exception e) {
            logger.error("获取授权信息时发生错误: {}", e.getMessage(), e);
            // 发生错误时添加一个默认的未激活模块
            modules.add(new LicenseModule(
                BASE_MODEL,
                BASE_MODEL_NAME,
                null,
                null,
                "inactive",
                "基础授权模块"
            ));
        }
        
        return modules;
    }

    @Override
    public List<LicenseModule> getLicenseModules() {
        // 简化处理，不额外解析授权文件内容
        return new ArrayList<>();
    }
    
    /**
     * 获取授权状态提示信息
     */
    public String getLicenseStatusMessage() {
        StringBuilder licensedate = new StringBuilder();
        
        try {
            License license = new License();
            
            if (license.isOpenModel(BASE_MODEL) && license.isValidTime(BASE_MODEL)) {
                int dates = license.getRestDays(BASE_MODEL);
                if (dates > 0 && dates < 31) {
                    licensedate.append("您的【").append(BASE_MODEL_NAME).append("】试用授权即将到期，距离过期只剩").append(dates).append("天，");
                }
            } else {
                if (license.isOpenModel(BASE_MODEL)) {
                    licensedate.append("您的【").append(BASE_MODEL_NAME).append("】试用授权已到期，");
                } else {
                    licensedate.append("您的【").append(BASE_MODEL_NAME).append("】授权未激活，");
                }
            }
        } catch (Exception e) {
            logger.error("获取授权状态信息时发生错误: {}", e.getMessage(), e);
            licensedate.append("授权状态检查发生错误，");
        }
        
        return licensedate.toString();
    }

    @Override
    public boolean importLicense(MultipartFile file) {
        String tempFilePath = null;
        try {
            // 保存上传的文件到临时位置
            String originalFilename = file.getOriginalFilename();
            String tempDir = System.getProperty("java.io.tmpdir");
            tempFilePath = tempDir + File.separator + "license_" + System.currentTimeMillis();
            Path tempPath = Paths.get(tempFilePath);
            Files.copy(file.getInputStream(), tempPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
            
            // 使用License类导入授权文件
            License license = new License();
            license.importLicensefile(tempFilePath);
            
            // 导入成功，可以删除临时文件
            logger.info("授权文件导入成功");
            return true;
        } catch (Exception e) {
            logger.error("导入授权文件时发生错误: {}", e.getMessage(), e);
            
            // 处理可能的错误码
            String detailMsg = e.getLocalizedMessage();
            String msg = e.getMessage();
            
            if ("010001".equals(detailMsg)) {
                msg = "设备不一致";
                logger.error("授权导入失败: 设备不一致");
            } else if ("010002".equals(detailMsg)) {
                msg = "授权文件不正确";
                logger.error("授权导入失败: 授权文件不正确");
            } else if ("010003".equals(detailMsg)) {
                msg = "不在有效期内";
                logger.error("授权导入失败: 不在有效期内");
            } else if ("010004".equals(detailMsg)) {
                msg = "源文件不存在";
                logger.error("授权导入失败: 源文件不存在");
            } else if ("010006".equals(detailMsg)) {
                msg = "导入品牌或产品不匹配";
                logger.error("授权导入失败: 导入品牌或产品不匹配");
            }
            
            // 清理临时文件
            if (tempFilePath != null && !tempFilePath.isEmpty()) {
                try {
                    File delFile = new File(tempFilePath);
                    delFile.delete();
                } catch (Exception ex) {
                    logger.warn("删除临时文件失败: {}", ex.getMessage());
                }
            }
            
            return false;
        }
    }

    @Override
    public boolean enableTrialLicense() {
        try {
            License license = new License();
            
            // 判断是否可以首次使用试用授权
            if (license.isFirstUseTestLicense()) {
                // 调用importLicensefile方法导入试用授权文件
                license.importLicensefile(TRIAL_LICENSE_FILE);
                logger.info("试用授权激活成功，导入授权文件: {}", TRIAL_LICENSE_FILE);
                return true;
            } else {
                logger.warn("已使用过试用授权，无法再次启用");
                return false;
            }
        } catch (Exception e) {
            logger.error("启用试用授权时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 判断是否已使用过试用授权
     * @return true表示已使用过试用授权，false表示未使用过
     */
    public boolean isTrialLicenseUsed() {
        try {
            License license = new License();
            return !license.isFirstUseTestLicense();
        } catch (Exception e) {
            logger.error("检查试用授权状态失败: {}", e.getMessage(), e);
            return false; // 异常情况下默认未使用过
        }
    }

    /**
     * 检查基础授权是否有效（已激活且未过期）
     * @return true表示有效，false表示无效
     */
    public boolean isBaseLicenseValid() {
        try {
            License license = new License();
            // 检查基础模块是否激活且未过期
            return license.isOpenModel(BASE_MODEL) && license.isValidTime(BASE_MODEL);
        } catch (Exception e) {
            logger.error("检查基础授权状态时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取授权无效的提示消息
     * @return 提示消息
     */
    public String getLicenseInvalidMessage() {
        try {
            License license = new License();
            
            if (!license.isOpenModel(BASE_MODEL)) {
                return "未启用试用授权或者未导入正式授权。";
            } else if (!license.isValidTime(BASE_MODEL)) {
                return "您的试用授权计可已经到期，请及时申请新的授权许可证导入到系统中。";
            }
            
            return "授权无效";
        } catch (Exception e) {
            logger.error("获取授权状态信息时发生错误: {}", e.getMessage(), e);
            return "授权状态检查发生错误";
        }
    }
} 