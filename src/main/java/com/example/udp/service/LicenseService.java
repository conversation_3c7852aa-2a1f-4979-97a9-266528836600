package com.example.udp.service;

import com.example.udp.model.LicenseModule;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 授权服务接口
 */
public interface LicenseService {
    /**
     * 获取硬件序列码
     */
    String getHardwareSerial();

    /**
     * 获取基础模块列表
     */
    List<LicenseModule> getBaseModules();

    /**
     * 获取授权模块列表
     */
    List<LicenseModule> getLicenseModules();

    /**
     * 导入授权文件
     */
    boolean importLicense(MultipartFile file);

    /**
     * 启用试用授权
     */
    boolean enableTrialLicense();
} 