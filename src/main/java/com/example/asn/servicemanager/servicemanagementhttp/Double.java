/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.example.asn.servicemanager.Servicemanager */
/* Created: <PERSON><PERSON> Mar 25 17:12:18 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc C:/Users/<USER>/Desktop/service.asn
 */


package com.example.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Double ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Real
 */

public class Double extends Real {
    
    /**
     * The default constructor.
     */
    public Double()
    {
    }
    
    /**
     * Construct from a float type.
     * @param value the float object to set this object to.
     */
    public Double(float value)
    {
	super(value);
    }
    
    /**
     * Construct from a double type.
     * @param value the double object to set this object to.
     */
    public Double(double value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final RealInfo c_typeinfo = new RealInfo (
	new Tags (
	    new short[] {
		0x0009
	    }
	),
	new QName (
	    "com.example.asn.servicemanager.servicemanagementhttp",
	    "Double"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Double"
	),
	536603,
	null,
	8,
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' com.example.asn.servicemanager.servicemanagementhttp.Double object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' com.example.asn.servicemanager.servicemanagementhttp.Double object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Double
