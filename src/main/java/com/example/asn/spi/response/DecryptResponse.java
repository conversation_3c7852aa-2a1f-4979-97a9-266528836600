package com.example.asn.spi.response;

/**
 * 私钥解密响应类
 * 对应MSG_TYPE_SM2_DECRYPT响应
 */
public class DecryptResponse extends AbstractSpiResponse {
    
    private final byte[] plainData;  // 明文数据
    
    /**
     * 构造函数
     * @param statusCode 状态码
     * @param rawData 原始响应数据
     */
    public DecryptResponse(byte statusCode, byte[] rawData) {
        super(statusCode, rawData);
        
        // 解析数据
        if (isSuccess() && rawData != null && rawData.length > 3) {
            // 状态码(1) + 明文数据(N)
            int plainLength = rawData.length - 3;
            this.plainData = new byte[plainLength];
            System.arraycopy(rawData, 3, this.plainData, 0, plainLength);
        } else {
            // 初始化默认值
            this.plainData = new byte[0];
        }
    }

    /**
     * 获取明文数据
     * @return 明文数据
     */
    public byte[] getPlainData() {
        return plainData;
    }
    
    @Override
    public String toString() {
        return "DecryptResponse{" +
                "statusCode=" + getStatusCode() +
                ", statusMessage='" + getStatusMessage() + '\'' +
                ", plainLength=" + plainData.length +
                '}';
    }
}
