/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ToBeSignedCertificate ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Sequence
 */

public class ToBeSignedCertificate extends Sequence {
    
    /**
     * The default constructor.
     */
    public ToBeSignedCertificate()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public ToBeSignedCertificate(CertificateId id, HashedId3 cracaId, 
		    CrlSeries crlSeries, ValidityPeriod validityPeriod, 
		    GeographicRegion region, SubjectAssurance assuranceLevel, 
		    SequenceOfAidSsp appPermissions, 
		    SequenceOfAidGroupPermissions certIssuePermissions, 
		    SequenceOfAidGroupPermissions certRequestPermissions, 
		    Null canRequestRollover, 
		    PublicEncryptionKey encryptionKey, 
		    VerificationKeyIndicator verifyKeyIndicator)
    {
	setId(id);
	setCracaId(cracaId);
	setCrlSeries(crlSeries);
	setValidityPeriod(validityPeriod);
	setRegion(region);
	setAssuranceLevel(assuranceLevel);
	setAppPermissions(appPermissions);
	setCertIssuePermissions(certIssuePermissions);
	setCertRequestPermissions(certRequestPermissions);
	setCanRequestRollover(canRequestRollover);
	setEncryptionKey(encryptionKey);
	setVerifyKeyIndicator(verifyKeyIndicator);
    }
    
    /**
     * Construct with required components.
     */
    public ToBeSignedCertificate(CertificateId id, HashedId3 cracaId, 
		    CrlSeries crlSeries, ValidityPeriod validityPeriod, 
		    VerificationKeyIndicator verifyKeyIndicator)
    {
	setId(id);
	setCracaId(cracaId);
	setCrlSeries(crlSeries);
	setValidityPeriod(validityPeriod);
	setVerifyKeyIndicator(verifyKeyIndicator);
    }
    
    public void initComponents()
    {
	mComponents[0] = new CertificateId();
	mComponents[1] = new HashedId3();
	mComponents[2] = new CrlSeries();
	mComponents[3] = new ValidityPeriod();
	mComponents[4] = new GeographicRegion();
	mComponents[5] = new SubjectAssurance();
	mComponents[6] = new SequenceOfAidSsp();
	mComponents[7] = new SequenceOfAidGroupPermissions();
	mComponents[8] = new SequenceOfAidGroupPermissions();
	mComponents[9] = new Null();
	mComponents[10] = new PublicEncryptionKey();
	mComponents[11] = new VerificationKeyIndicator();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[12];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new CertificateId();
	    case 1:
		return new HashedId3();
	    case 2:
		return new CrlSeries();
	    case 3:
		return new ValidityPeriod();
	    case 4:
		return new GeographicRegion();
	    case 5:
		return new SubjectAssurance();
	    case 6:
		return new SequenceOfAidSsp();
	    case 7:
		return new SequenceOfAidGroupPermissions();
	    case 8:
		return new SequenceOfAidGroupPermissions();
	    case 9:
		return new Null();
	    case 10:
		return new PublicEncryptionKey();
	    case 11:
		return new VerificationKeyIndicator();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "id"
    public CertificateId getId()
    {
	return (CertificateId)mComponents[0];
    }
    
    public void setId(CertificateId id)
    {
	mComponents[0] = id;
    }
    
    
    // Methods for field "cracaId"
    public HashedId3 getCracaId()
    {
	return (HashedId3)mComponents[1];
    }
    
    public void setCracaId(HashedId3 cracaId)
    {
	mComponents[1] = cracaId;
    }
    
    
    // Methods for field "crlSeries"
    public CrlSeries getCrlSeries()
    {
	return (CrlSeries)mComponents[2];
    }
    
    public void setCrlSeries(CrlSeries crlSeries)
    {
	mComponents[2] = crlSeries;
    }
    
    
    // Methods for field "validityPeriod"
    public ValidityPeriod getValidityPeriod()
    {
	return (ValidityPeriod)mComponents[3];
    }
    
    public void setValidityPeriod(ValidityPeriod validityPeriod)
    {
	mComponents[3] = validityPeriod;
    }
    
    
    // Methods for field "region"
    public GeographicRegion getRegion()
    {
	return (GeographicRegion)mComponents[4];
    }
    
    public void setRegion(GeographicRegion region)
    {
	mComponents[4] = region;
    }
    
    public boolean hasRegion()
    {
	return componentIsPresent(4);
    }
    
    public void deleteRegion()
    {
	setComponentAbsent(4);
    }
    
    
    // Methods for field "assuranceLevel"
    public SubjectAssurance getAssuranceLevel()
    {
	return (SubjectAssurance)mComponents[5];
    }
    
    public void setAssuranceLevel(SubjectAssurance assuranceLevel)
    {
	mComponents[5] = assuranceLevel;
    }
    
    public boolean hasAssuranceLevel()
    {
	return componentIsPresent(5);
    }
    
    public void deleteAssuranceLevel()
    {
	setComponentAbsent(5);
    }
    
    
    // Methods for field "appPermissions"
    public SequenceOfAidSsp getAppPermissions()
    {
	return (SequenceOfAidSsp)mComponents[6];
    }
    
    public void setAppPermissions(SequenceOfAidSsp appPermissions)
    {
	mComponents[6] = appPermissions;
    }
    
    public boolean hasAppPermissions()
    {
	return componentIsPresent(6);
    }
    
    public void deleteAppPermissions()
    {
	setComponentAbsent(6);
    }
    
    
    // Methods for field "certIssuePermissions"
    public SequenceOfAidGroupPermissions getCertIssuePermissions()
    {
	return (SequenceOfAidGroupPermissions)mComponents[7];
    }
    
    public void setCertIssuePermissions(SequenceOfAidGroupPermissions certIssuePermissions)
    {
	mComponents[7] = certIssuePermissions;
    }
    
    public boolean hasCertIssuePermissions()
    {
	return componentIsPresent(7);
    }
    
    public void deleteCertIssuePermissions()
    {
	setComponentAbsent(7);
    }
    
    
    // Methods for field "certRequestPermissions"
    public SequenceOfAidGroupPermissions getCertRequestPermissions()
    {
	return (SequenceOfAidGroupPermissions)mComponents[8];
    }
    
    public void setCertRequestPermissions(SequenceOfAidGroupPermissions certRequestPermissions)
    {
	mComponents[8] = certRequestPermissions;
    }
    
    public boolean hasCertRequestPermissions()
    {
	return componentIsPresent(8);
    }
    
    public void deleteCertRequestPermissions()
    {
	setComponentAbsent(8);
    }
    
    
    // Methods for field "canRequestRollover"
    public Null getCanRequestRollover()
    {
	return (Null)mComponents[9];
    }
    
    public void setCanRequestRollover(Null canRequestRollover)
    {
	mComponents[9] = canRequestRollover;
    }
    
    public boolean hasCanRequestRollover()
    {
	return componentIsPresent(9);
    }
    
    public void deleteCanRequestRollover()
    {
	setComponentAbsent(9);
    }
    
    
    // Methods for field "encryptionKey"
    public PublicEncryptionKey getEncryptionKey()
    {
	return (PublicEncryptionKey)mComponents[10];
    }
    
    public void setEncryptionKey(PublicEncryptionKey encryptionKey)
    {
	mComponents[10] = encryptionKey;
    }
    
    public boolean hasEncryptionKey()
    {
	return componentIsPresent(10);
    }
    
    public void deleteEncryptionKey()
    {
	setComponentAbsent(10);
    }
    
    
    // Methods for field "verifyKeyIndicator"
    public VerificationKeyIndicator getVerifyKeyIndicator()
    {
	return (VerificationKeyIndicator)mComponents[11];
    }
    
    public void setVerifyKeyIndicator(VerificationKeyIndicator verifyKeyIndicator)
    {
	mComponents[11] = verifyKeyIndicator;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "ToBeSignedCertificate"
	),
	new QName (
	    "ProgrammingTypes",
	    "ToBeSignedCertificate"
	),
	536607,
	new Unions (
	    new Constraints[] {
		new MultipleTypeConstraint (
		    new InnerTypeConstraint[] {
			new InnerTypeConstraint (
			    1,
			    6,	// appPermissions
			    null
			)
		    },
		    1	// PartialSpecification
		),
		new MultipleTypeConstraint (
		    new InnerTypeConstraint[] {
			new InnerTypeConstraint (
			    1,
			    7,	// certIssuePermissions
			    null
			)
		    },
		    1	// PartialSpecification
		),
		new MultipleTypeConstraint (
		    new InnerTypeConstraint[] {
			new InnerTypeConstraint (
			    1,
			    8,	// certRequestPermissions
			    null
			)
		    },
		    1	// PartialSpecification
		)
	    }
	),
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"CertificateId"
			    ),
			    new QName (
				"ProgrammingTypes",
				"CertificateId"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "CertificateId"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1),
				    new TagDecoderElement((short)0x8002, 2),
				    new TagDecoderElement((short)0x8003, 3)
				}
			    )
			)
		    ),
		    "id",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"HashedId3"
			    ),
			    new QName (
				"ProgrammingTypes",
				"HashedId3"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(3)
				)
			    ),
			    new Bounds (
				Long.valueOf(3),
				Long.valueOf(3)
			    )
			)
		    ),
		    "cracaId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"CrlSeries"
			    ),
			    new QName (
				"ProgrammingTypes",
				"CrlSeries"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new CrlSeries(0), 
				    new CrlSeries(65535),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(0),
				Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "crlSeries",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"ValidityPeriod"
			    ),
			    new QName (
				"ProgrammingTypes",
				"ValidityPeriod"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "ValidityPeriod"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "ValidityPeriod"
				)
			    ),
			    0
			)
		    ),
		    "validityPeriod",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"GeographicRegion"
			    ),
			    new QName (
				"ProgrammingTypes",
				"GeographicRegion"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "GeographicRegion"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1),
				    new TagDecoderElement((short)0x8002, 2),
				    new TagDecoderElement((short)0x8003, 3)
				}
			    )
			)
		    ),
		    "region",
		    4,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SubjectAssurance"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SubjectAssurance"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(1)
				)
			    ),
			    new Bounds (
				Long.valueOf(1),
				Long.valueOf(1)
			    )
			)
		    ),
		    "assuranceLevel",
		    5,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8006
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfAidSsp"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfAidSsp"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "AidSsp"
				)
			    )
			)
		    ),
		    "appPermissions",
		    6,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8007
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfAidGroupPermissions"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfAidGroupPermissions"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "AidGroupPermissions"
				)
			    )
			)
		    ),
		    "certIssuePermissions",
		    7,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8008
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfAidGroupPermissions"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfAidGroupPermissions"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "AidGroupPermissions"
				)
			    )
			)
		    ),
		    "certRequestPermissions",
		    8,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new TypeInfo (
			    new Tags (
				new short[] {
				    (short)0x8009
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"Null"
			    ),
			    new QName (
				"builtin",
				"NULL"
			    ),
			    1585179,
			    null
			)
		    ),
		    "canRequestRollover",
		    9,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x800a
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"PublicEncryptionKey"
			    ),
			    new QName (
				"ProgrammingTypes",
				"PublicEncryptionKey"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "PublicEncryptionKey"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "PublicEncryptionKey"
				)
			    ),
			    0
			)
		    ),
		    "encryptionKey",
		    10,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x800b
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"VerificationKeyIndicator"
			    ),
			    new QName (
				"ProgrammingTypes",
				"VerificationKeyIndicator"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "VerificationKeyIndicator"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "verifyKeyIndicator",
		    11,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4),
			new TagDecoderElement((short)0x8005, 5),
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5),
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x800a, 10),
			new TagDecoderElement((short)0x800b, 11)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x800b, 11)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ToBeSignedCertificate object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ToBeSignedCertificate object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ToBeSignedCertificate
