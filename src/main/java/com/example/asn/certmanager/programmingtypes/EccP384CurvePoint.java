/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the EccP384CurvePoint ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Choice
 */

public class EccP384CurvePoint extends Choice {
    
    /**
     * The default constructor.
     */
    public EccP384CurvePoint()
    {
    }
    
    public static final  int  x_only_chosen = 1;
    public static final  int  fill_chosen = 2;
    public static final  int  compressed_y_0_chosen = 3;
    public static final  int  compressed_y_1_chosen = 4;
    public static final  int  uncompressedP384_chosen = 5;
    
    // Methods for field "x_only"
    public static EccP384CurvePoint createEccP384CurvePointWithX_only(OctetString x_only)
    {
	EccP384CurvePoint __object = new EccP384CurvePoint();

	__object.setX_only(x_only);
	return __object;
    }
    
    public boolean hasX_only()
    {
	return getChosenFlag() == x_only_chosen;
    }
    
    public OctetString getX_only()
    {
	if (hasX_only())
	    return (OctetString)mChosenValue;
	else
	    return null;
    }
    
    public void setX_only(OctetString x_only)
    {
	setChosenValue(x_only);
	setChosenFlag(x_only_chosen);
    }
    
    
    // Methods for field "fill"
    public static EccP384CurvePoint createEccP384CurvePointWithFill(Null fill)
    {
	EccP384CurvePoint __object = new EccP384CurvePoint();

	__object.setFill(fill);
	return __object;
    }
    
    public boolean hasFill()
    {
	return getChosenFlag() == fill_chosen;
    }
    
    public Null getFill()
    {
	if (hasFill())
	    return (Null)mChosenValue;
	else
	    return null;
    }
    
    public void setFill(Null fill)
    {
	setChosenValue(fill);
	setChosenFlag(fill_chosen);
    }
    
    
    // Methods for field "compressed_y_0"
    public static EccP384CurvePoint createEccP384CurvePointWithCompressed_y_0(OctetString compressed_y_0)
    {
	EccP384CurvePoint __object = new EccP384CurvePoint();

	__object.setCompressed_y_0(compressed_y_0);
	return __object;
    }
    
    public boolean hasCompressed_y_0()
    {
	return getChosenFlag() == compressed_y_0_chosen;
    }
    
    public OctetString getCompressed_y_0()
    {
	if (hasCompressed_y_0())
	    return (OctetString)mChosenValue;
	else
	    return null;
    }
    
    public void setCompressed_y_0(OctetString compressed_y_0)
    {
	setChosenValue(compressed_y_0);
	setChosenFlag(compressed_y_0_chosen);
    }
    
    
    // Methods for field "compressed_y_1"
    public static EccP384CurvePoint createEccP384CurvePointWithCompressed_y_1(OctetString compressed_y_1)
    {
	EccP384CurvePoint __object = new EccP384CurvePoint();

	__object.setCompressed_y_1(compressed_y_1);
	return __object;
    }
    
    public boolean hasCompressed_y_1()
    {
	return getChosenFlag() == compressed_y_1_chosen;
    }
    
    public OctetString getCompressed_y_1()
    {
	if (hasCompressed_y_1())
	    return (OctetString)mChosenValue;
	else
	    return null;
    }
    
    public void setCompressed_y_1(OctetString compressed_y_1)
    {
	setChosenValue(compressed_y_1);
	setChosenFlag(compressed_y_1_chosen);
    }
    
    
    // Methods for field "uncompressedP384"
    public static EccP384CurvePoint createEccP384CurvePointWithUncompressedP384(UncompressedP384 uncompressedP384)
    {
	EccP384CurvePoint __object = new EccP384CurvePoint();

	__object.setUncompressedP384(uncompressedP384);
	return __object;
    }
    
    public boolean hasUncompressedP384()
    {
	return getChosenFlag() == uncompressedP384_chosen;
    }
    
    public UncompressedP384 getUncompressedP384()
    {
	if (hasUncompressedP384())
	    return (UncompressedP384)mChosenValue;
	else
	    return null;
    }
    
    public void setUncompressedP384(UncompressedP384 uncompressedP384)
    {
	setChosenValue(uncompressedP384);
	setChosenFlag(uncompressedP384_chosen);
    }
    
    
    
    /**
     * Define the _seq2 ASN.1 type included in the ProgrammingTypes ASN.1 module.
     * @see Sequence
     */
    public static class UncompressedP384 extends Sequence {
	
	/**
	 * The default constructor.
	 */
	public UncompressedP384()
	{
	}
	
	/**
	 * Construct with AbstractData components.
	 */
	public UncompressedP384(OctetString x, OctetString y)
	{
	    setX(x);
	    setY(y);
	}
	
	public void initComponents()
	{
	    mComponents[0] = new OctetString();
	    mComponents[1] = new OctetString();
	}
	
	// Instance initializer
	{
	    mComponents = new AbstractData[2];
	}
	
	// Method to create a specific component instance
	public AbstractData createInstance(int index)
	{
	    switch (index) {
		case 0:
		    return new OctetString();
		case 1:
		    return new OctetString();
		default:
		    throw new InternalError("AbstractCollection.createInstance()");
	    }
	    
	}
	
	
	// Methods for field "x"
	public OctetString getX()
	{
	    return (OctetString)mComponents[0];
	}
	
	public void setX(OctetString x)
	{
	    mComponents[0] = x;
	}
	
	
	// Methods for field "y"
	public OctetString getY()
	{
	    return (OctetString)mComponents[1];
	}
	
	public void setY(OctetString y)
	{
	    mComponents[1] = y;
	}
	
	
	/**
	 * Initialize the type descriptor.
	 */
	private static final SequenceInfo c_typeinfo = new SequenceInfo (
	    new Tags (
		new short[] {
		    (short)0x8004
		}
	    ),
	    new QName (
		"com.example.asn.certmanager.programmingtypes",
		"EccP384CurvePoint$UncompressedP384"
	    ),
	    new QName (
		"builtin",
		"SEQUENCE"
	    ),
	    536603,
	    null,
	    new FieldsList (
		new SequenceFieldInfo[] {
		    new SequenceFieldInfo (
			new TypeInfoRef (
			    new VectorInfo (
				new Tags (
				    new short[] {
					(short)0x8000
				    }
				),
				new QName (
				    "com.oss.asn1",
				    "OctetString"
				),
				new QName (
				    "builtin",
				    "OCTET STRING"
				),
				536603,
				new SizeConstraint (
				    new SingleValueConstraint (
					new INTEGER(48)
				    )
				),
				new Bounds (
				    Long.valueOf(48),
				    Long.valueOf(48)
				)
			    )
			),
			"x",
			0,
			2,
			null
		    ),
		    new SequenceFieldInfo (
			new TypeInfoRef (
			    new VectorInfo (
				new Tags (
				    new short[] {
					(short)0x8001
				    }
				),
				new QName (
				    "com.oss.asn1",
				    "OctetString"
				),
				new QName (
				    "builtin",
				    "OCTET STRING"
				),
				536603,
				new SizeConstraint (
				    new SingleValueConstraint (
					new INTEGER(48)
				    )
				),
				new Bounds (
				    Long.valueOf(48),
				    Long.valueOf(48)
				)
			    )
			),
			"y",
			1,
			2,
			null
		    )
		}
	    ),
	    0,
	    new TagDecoders (
		new TagDecoder[] {
		    new TagDecoder (
			new TagDecoderElement[] {
			    new TagDecoderElement((short)0x8000, 0)
			}
		    ),
		    new TagDecoder (
			new TagDecoderElement[] {
			    new TagDecoderElement((short)0x8001, 1)
			}
		    )
		}
	    ),
	    0
	);
	
	/**
	 * Get the type descriptor (TypeInfo) of 'this' UncompressedP384 object.
	 */
	public TypeInfo getTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Get the static type descriptor (TypeInfo) of 'this' UncompressedP384 object.
	 */
	public static TypeInfo getStaticTypeInfo()
	{
	    return c_typeinfo;
	}
	
    } // End class definition for UncompressedP384

    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case x_only_chosen:
		return new OctetString();
	    case fill_chosen:
		return new Null();
	    case compressed_y_0_chosen:
		return new OctetString();
	    case compressed_y_1_chosen:
		return new OctetString();
	    case uncompressedP384_chosen:
		return new UncompressedP384();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "EccP384CurvePoint"
	),
	new QName (
	    "ProgrammingTypes",
	    "EccP384CurvePoint"
	),
	536603,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"OctetString"
			    ),
			    new QName (
				"builtin",
				"OCTET STRING"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(48)
				)
			    ),
			    new Bounds (
				Long.valueOf(48),
				Long.valueOf(48)
			    )
			)
		    ),
		    "x-only",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new TypeInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"Null"
			    ),
			    new QName (
				"builtin",
				"NULL"
			    ),
			    1585179,
			    null
			)
		    ),
		    "fill",
		    1,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"OctetString"
			    ),
			    new QName (
				"builtin",
				"OCTET STRING"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(48)
				)
			    ),
			    new Bounds (
				Long.valueOf(48),
				Long.valueOf(48)
			    )
			)
		    ),
		    "compressed-y-0",
		    2,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"OctetString"
			    ),
			    new QName (
				"builtin",
				"OCTET STRING"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(48)
				)
			    ),
			    new Bounds (
				Long.valueOf(48),
				Long.valueOf(48)
			    )
			)
		    ),
		    "compressed-y-1",
		    3,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new QName (
			    "com.example.asn.certmanager.programmingtypes",
			    "EccP384CurvePoint$UncompressedP384"
			)
		    ),
		    "uncompressedP384",
		    4,
		    2
		)
	    }
	),
	0,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1),
		new TagDecoderElement((short)0x8002, 2),
		new TagDecoderElement((short)0x8003, 3),
		new TagDecoderElement((short)0x8004, 4)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' EccP384CurvePoint object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' EccP384CurvePoint object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for EccP384CurvePoint
