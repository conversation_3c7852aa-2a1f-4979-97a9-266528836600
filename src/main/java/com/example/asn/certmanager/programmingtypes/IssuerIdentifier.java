/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the IssuerIdentifier ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Choice
 */

public class IssuerIdentifier extends Choice {
    
    /**
     * The default constructor.
     */
    public IssuerIdentifier()
    {
    }
    
    public static final  int  sha256AndDigest_chosen = 1;
    public static final  int  self_chosen = 2;
    public static final  int  sha384AndDigest_chosen = 3;
    public static final  int  sm3AndDigest_chosen = 4;
    
    // Methods for field "sha256AndDigest"
    public static IssuerIdentifier createIssuerIdentifierWithSha256AndDigest(HashedId8 sha256AndDigest)
    {
	IssuerIdentifier __object = new IssuerIdentifier();

	__object.setSha256AndDigest(sha256AndDigest);
	return __object;
    }
    
    public boolean hasSha256AndDigest()
    {
	return getChosenFlag() == sha256AndDigest_chosen;
    }
    
    public HashedId8 getSha256AndDigest()
    {
	if (hasSha256AndDigest())
	    return (HashedId8)mChosenValue;
	else
	    return null;
    }
    
    public void setSha256AndDigest(HashedId8 sha256AndDigest)
    {
	setChosenValue(sha256AndDigest);
	setChosenFlag(sha256AndDigest_chosen);
    }
    
    
    // Methods for field "self"
    public static IssuerIdentifier createIssuerIdentifierWithSelf(HashAlgorithm self)
    {
	IssuerIdentifier __object = new IssuerIdentifier();

	__object.setSelf(self);
	return __object;
    }
    
    public boolean hasSelf()
    {
	return getChosenFlag() == self_chosen;
    }
    
    public HashAlgorithm getSelf()
    {
	if (hasSelf())
	    return (HashAlgorithm)mChosenValue;
	else
	    return null;
    }
    
    public void setSelf(HashAlgorithm self)
    {
	setChosenValue(self);
	setChosenFlag(self_chosen);
    }
    
    
    // Methods for field "sha384AndDigest"
    public static IssuerIdentifier createIssuerIdentifierWithSha384AndDigest(HashedId8 sha384AndDigest)
    {
	IssuerIdentifier __object = new IssuerIdentifier();

	__object.setSha384AndDigest(sha384AndDigest);
	return __object;
    }
    
    public boolean hasSha384AndDigest()
    {
	return getChosenFlag() == sha384AndDigest_chosen;
    }
    
    public HashedId8 getSha384AndDigest()
    {
	if (hasSha384AndDigest())
	    return (HashedId8)mChosenValue;
	else
	    return null;
    }
    
    public void setSha384AndDigest(HashedId8 sha384AndDigest)
    {
	setChosenValue(sha384AndDigest);
	setChosenFlag(sha384AndDigest_chosen);
    }
    
    
    // Methods for field "sm3AndDigest"
    public static IssuerIdentifier createIssuerIdentifierWithSm3AndDigest(HashedId8 sm3AndDigest)
    {
	IssuerIdentifier __object = new IssuerIdentifier();

	__object.setSm3AndDigest(sm3AndDigest);
	return __object;
    }
    
    public boolean hasSm3AndDigest()
    {
	return getChosenFlag() == sm3AndDigest_chosen;
    }
    
    public HashedId8 getSm3AndDigest()
    {
	if (hasSm3AndDigest())
	    return (HashedId8)mChosenValue;
	else
	    return null;
    }
    
    public void setSm3AndDigest(HashedId8 sm3AndDigest)
    {
	setChosenValue(sm3AndDigest);
	setChosenFlag(sm3AndDigest_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case sha256AndDigest_chosen:
		return new HashedId8();
	    case self_chosen:
		return HashAlgorithm.sha256;
	    case sha384AndDigest_chosen:
		return new HashedId8();
	    case sm3AndDigest_chosen:
		return new HashedId8();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "IssuerIdentifier"
	),
	new QName (
	    "ProgrammingTypes",
	    "IssuerIdentifier"
	),
	536607,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"HashedId8"
			    ),
			    new QName (
				"ProgrammingTypes",
				"HashedId8"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(8)
				)
			    ),
			    new Bounds (
				Long.valueOf(8),
				Long.valueOf(8)
			    )
			)
		    ),
		    "sha256AndDigest",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"HashAlgorithm"
			    ),
			    new QName (
				"ProgrammingTypes",
				"HashAlgorithm"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"sha256",
					0
				    ),
				    new MemberListElement (
					"sha384",
					1
				    ),
				    new MemberListElement (
					"sm3",
					2
				    )
				}
			    ),
			    2,
			    HashAlgorithm.sha256
			)
		    ),
		    "self",
		    1,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"HashedId8"
			    ),
			    new QName (
				"ProgrammingTypes",
				"HashedId8"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(8)
				)
			    ),
			    new Bounds (
				Long.valueOf(8),
				Long.valueOf(8)
			    )
			)
		    ),
		    "sha384AndDigest",
		    2,
		    10
		),
		new FieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"HashedId8"
			    ),
			    new QName (
				"ProgrammingTypes",
				"HashedId8"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(8)
				)
			    ),
			    new Bounds (
				Long.valueOf(8),
				Long.valueOf(8)
			    )
			)
		    ),
		    "sm3AndDigest",
		    3,
		    10
		)
	    }
	),
	2,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1),
		new TagDecoderElement((short)0x8002, 2),
		new TagDecoderElement((short)0x8003, 3)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' IssuerIdentifier object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' IssuerIdentifier object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Check the current selection on unknown extension
     */
    public final boolean hasUnknownExtension()
    {
	return getChosenFlag() > 4;
    }
    
} // End class definition for IssuerIdentifier
