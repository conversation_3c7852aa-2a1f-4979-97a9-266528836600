/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the RegionAndSubregions ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Sequence
 */

public class RegionAndSubregions extends Sequence {
    
    /**
     * The default constructor.
     */
    public RegionAndSubregions()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public RegionAndSubregions(Uint8 region, SequenceOfUint16 subregions)
    {
	setRegion(region);
	setSubregions(subregions);
    }
    
    public void initComponents()
    {
	mComponents[0] = new Uint8();
	mComponents[1] = new SequenceOfUint16();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new Uint8();
	    case 1:
		return new SequenceOfUint16();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "region"
    public Uint8 getRegion()
    {
	return (Uint8)mComponents[0];
    }
    
    public void setRegion(Uint8 region)
    {
	mComponents[0] = region;
    }
    
    
    // Methods for field "subregions"
    public SequenceOfUint16 getSubregions()
    {
	return (SequenceOfUint16)mComponents[1];
    }
    
    public void setSubregions(SequenceOfUint16 subregions)
    {
	mComponents[1] = subregions;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "RegionAndSubregions"
	),
	new QName (
	    "ProgrammingTypes",
	    "RegionAndSubregions"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"Uint8"
			    ),
			    new QName (
				"ProgrammingTypes",
				"Uint8"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint8(0), 
				    new Uint8(255),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(0),
				Long.valueOf(255)
			    ),
			    null,
			    1
			)
		    ),
		    "region",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfUint16"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfUint16"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "Uint16"
				)
			    )
			)
		    ),
		    "subregions",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' RegionAndSubregions object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' RegionAndSubregions object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for RegionAndSubregions
