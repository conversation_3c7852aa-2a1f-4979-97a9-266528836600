/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the AidGroupPermissions ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Sequence
 */

public class AidGroupPermissions extends Sequence {
    
    /**
     * The default constructor.
     */
    public AidGroupPermissions()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public AidGroupPermissions(SubjectPermissions subjectPermissions, 
		    INTEGER minChainLength, INTEGER chainLengthRange, 
		    EndEntityType eeType)
    {
	setSubjectPermissions(subjectPermissions);
	setMinChainLength(minChainLength);
	setChainLengthRange(chainLengthRange);
	setEeType(eeType);
    }
    
    /**
     * Construct with components.
     */
    public AidGroupPermissions(SubjectPermissions subjectPermissions, 
		    long minChainLength, long chainLengthRange, 
		    EndEntityType eeType)
    {
	this(subjectPermissions, new INTEGER(minChainLength), 
	     new INTEGER(chainLengthRange), eeType);
    }
    
    /**
     * Construct with required components.
     */
    public AidGroupPermissions(SubjectPermissions subjectPermissions)
    {
	setSubjectPermissions(subjectPermissions);
    }
    
    public void initComponents()
    {
	mComponents[0] = new SubjectPermissions();
	mComponents[1] = new INTEGER();
	mComponents[2] = new INTEGER();
	mComponents[3] = new EndEntityType();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[4];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return new SubjectPermissions();
	    case 1:
		return new INTEGER();
	    case 2:
		return new INTEGER();
	    case 3:
		return new EndEntityType();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    // Default Values
    public static final INTEGER minChainLength__default =
	new INTEGER(1);
    public static final INTEGER chainLengthRange__default =
	new INTEGER(0);
    public static final EndEntityType eeType__default = 
	new EndEntityType (
	    new byte[]
	    {
		(byte)0x80
	    },
	    8
	);
    
    // Methods for field "subjectPermissions"
    public SubjectPermissions getSubjectPermissions()
    {
	return (SubjectPermissions)mComponents[0];
    }
    
    public void setSubjectPermissions(SubjectPermissions subjectPermissions)
    {
	mComponents[0] = subjectPermissions;
    }
    
    
    // Methods for field "minChainLength"
    public long getMinChainLength()
    {
	if (hasMinChainLength())
	    return ((INTEGER)mComponents[1]).longValue();
	else
	    return minChainLength__default.longValue();
    }
    
    public void setMinChainLength(long minChainLength)
    {
	setMinChainLength(new INTEGER(minChainLength));
    }
    
    public void setMinChainLength(INTEGER minChainLength)
    {
	mComponents[1] = minChainLength;
    }
    
    public void setMinChainLengthToDefault()
    {
	setMinChainLength(minChainLength__default);
    }
    
    public boolean hasDefaultMinChainLength()
    {
	return true;
    }
    
    public boolean hasMinChainLength()
    {
	return componentIsPresent(1);
    }
    
    public void deleteMinChainLength()
    {
	setComponentAbsent(1);
    }
    
    
    // Methods for field "chainLengthRange"
    public long getChainLengthRange()
    {
	if (hasChainLengthRange())
	    return ((INTEGER)mComponents[2]).longValue();
	else
	    return chainLengthRange__default.longValue();
    }
    
    public void setChainLengthRange(long chainLengthRange)
    {
	setChainLengthRange(new INTEGER(chainLengthRange));
    }
    
    public void setChainLengthRange(INTEGER chainLengthRange)
    {
	mComponents[2] = chainLengthRange;
    }
    
    public void setChainLengthRangeToDefault()
    {
	setChainLengthRange(chainLengthRange__default);
    }
    
    public boolean hasDefaultChainLengthRange()
    {
	return true;
    }
    
    public boolean hasChainLengthRange()
    {
	return componentIsPresent(2);
    }
    
    public void deleteChainLengthRange()
    {
	setComponentAbsent(2);
    }
    
    
    // Methods for field "eeType"
    public EndEntityType getEeType()
    {
	if (hasEeType())
	    return (EndEntityType)mComponents[3];
	else
	    return (EndEntityType)eeType__default.clone();
    }
    
    public void setEeType(EndEntityType eeType)
    {
	mComponents[3] = eeType;
    }
    
    public void setEeTypeToDefault()
    {
	setEeType(eeType__default);
    }
    
    public boolean hasDefaultEeType()
    {
	return true;
    }
    
    public boolean hasEeType()
    {
	return componentIsPresent(3);
    }
    
    public void deleteEeType()
    {
	setComponentAbsent(3);
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "AidGroupPermissions"
	),
	new QName (
	    "ProgrammingTypes",
	    "AidGroupPermissions"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SubjectPermissions"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SubjectPermissions"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "SubjectPermissions"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "subjectPermissions",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"INTEGER"
			    ),
			    new QName (
				"builtin",
				"INTEGER"
			    ),
			    536603,
			    null,
			    null,
			    null,
			    0
			)
		    ),
		    "minChainLength",
		    1,
		    3,
		    minChainLength__default
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"INTEGER"
			    ),
			    new QName (
				"builtin",
				"INTEGER"
			    ),
			    536603,
			    null,
			    null,
			    null,
			    0
			)
		    ),
		    "chainLengthRange",
		    2,
		    3,
		    chainLengthRange__default
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new BitStringInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"EndEntityType"
			    ),
			    new QName (
				"ProgrammingTypes",
				"EndEntityType"
			    ),
			    536603,
			    new Intersection (
				new SizeConstraint (
				    new SingleValueConstraint (
					new INTEGER(8)
				    )
				),
				new AllExcept (
				    new SingleValueConstraint (
					new EndEntityType (
					    new byte[]
					    {
						(byte)0x00
					    },
					    8
					)
				    )
				)
			    ),
			    new Bounds (
				Long.valueOf(8),
				Long.valueOf(8)
			    ),
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"app",
					0
				    ),
				    new MemberListElement (
					"enroll",
					1
				    )
				}
			    )
			)
		    ),
		    "eeType",
		    3,
		    3,
		    eeType__default
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1),
			new TagDecoderElement((short)0x8002, 2),
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2),
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' AidGroupPermissions object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' AidGroupPermissions object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for AidGroupPermissions
