/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Int32 ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see INTEGER
 */

public class Int32 extends INTEGER {
    
    /**
     * The default constructor.
     */
    public Int32()
    {
    }
    
    public Int32(short value)
    {
	super(value);
    }
    
    public Int32(int value)
    {
	super(value);
    }
    
    public Int32(long value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final IntegerInfo c_typeinfo = new IntegerInfo (
	new Tags (
	    new short[] {
		0x0002
	    }
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "Int32"
	),
	new QName (
	    "ProgrammingTypes",
	    "Int32"
	),
	536603,
	new ValueRangeConstraint (
	    new AbstractBounds(
		new Int32(-2147483648), 
		new Int32(2147483647),
		0
	    )
	),
	new Bounds (
	    Long.valueOf(-2147483648),
	    Long.valueOf(2147483647)
	),
	null,
	4
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' Int32 object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' Int32 object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Int32
