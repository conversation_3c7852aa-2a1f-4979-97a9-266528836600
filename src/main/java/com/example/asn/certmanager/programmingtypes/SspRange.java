/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.programmingtypes;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the SspRange ASN.1 type included in the ProgrammingTypes ASN.1 module.
 * @see Choice
 */

public class SspRange extends Choice {
    
    /**
     * The default constructor.
     */
    public SspRange()
    {
    }
    
    public static final  int  opaque_chosen = 1;
    public static final  int  all_chosen = 2;
    public static final  int  bitmapSspRange_chosen = 3;
    
    // Methods for field "opaque"
    public static SspRange createSspRangeWithOpaque(SequenceOfOctetString opaque)
    {
	SspRange __object = new SspRange();

	__object.setOpaque(opaque);
	return __object;
    }
    
    public boolean hasOpaque()
    {
	return getChosenFlag() == opaque_chosen;
    }
    
    public SequenceOfOctetString getOpaque()
    {
	if (hasOpaque())
	    return (SequenceOfOctetString)mChosenValue;
	else
	    return null;
    }
    
    public void setOpaque(SequenceOfOctetString opaque)
    {
	setChosenValue(opaque);
	setChosenFlag(opaque_chosen);
    }
    
    
    // Methods for field "all"
    public static SspRange createSspRangeWithAll(Null all)
    {
	SspRange __object = new SspRange();

	__object.setAll(all);
	return __object;
    }
    
    public boolean hasAll()
    {
	return getChosenFlag() == all_chosen;
    }
    
    public Null getAll()
    {
	if (hasAll())
	    return (Null)mChosenValue;
	else
	    return null;
    }
    
    public void setAll(Null all)
    {
	setChosenValue(all);
	setChosenFlag(all_chosen);
    }
    
    
    // Methods for field "bitmapSspRange"
    public static SspRange createSspRangeWithBitmapSspRange(BitmapSspRange bitmapSspRange)
    {
	SspRange __object = new SspRange();

	__object.setBitmapSspRange(bitmapSspRange);
	return __object;
    }
    
    public boolean hasBitmapSspRange()
    {
	return getChosenFlag() == bitmapSspRange_chosen;
    }
    
    public BitmapSspRange getBitmapSspRange()
    {
	if (hasBitmapSspRange())
	    return (BitmapSspRange)mChosenValue;
	else
	    return null;
    }
    
    public void setBitmapSspRange(BitmapSspRange bitmapSspRange)
    {
	setChosenValue(bitmapSspRange);
	setChosenFlag(bitmapSspRange_chosen);
    }
    
    
    // Method to create a specific choice instance
    public AbstractData createInstance(int chosen)
    {
	switch (chosen) {
	    case opaque_chosen:
		return new SequenceOfOctetString();
	    case all_chosen:
		return new Null();
	    case bitmapSspRange_chosen:
		return new BitmapSspRange();
	    default:
		throw new InternalError("Choice.createInstance()");
	}
	
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final ChoiceInfo c_typeinfo = new ChoiceInfo (
	new Tags (
	    null
	),
	new QName (
	    "com.example.asn.certmanager.programmingtypes",
	    "SspRange"
	),
	new QName (
	    "ProgrammingTypes",
	    "SspRange"
	),
	536607,
	null,
	new FieldsList (
	    new FieldInfo[] {
		new FieldInfo (
		    new TypeInfoRef (
			new ContainerInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SequenceOfOctetString"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SequenceOfOctetString"
			    ),
			    536603,
			    null,
			    null,
			    new TypeInfoRef (
				new QName (
				    "com.oss.asn1",
				    "OctetString"
				)
			    )
			)
		    ),
		    "opaque",
		    0,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new TypeInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"Null"
			    ),
			    new QName (
				"builtin",
				"NULL"
			    ),
			    1585179,
			    null
			)
		    ),
		    "all",
		    1,
		    2
		),
		new FieldInfo (
		    new TypeInfoRef (
			new SequenceInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"BitmapSspRange"
			    ),
			    new QName (
				"ProgrammingTypes",
				"BitmapSspRange"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "BitmapSspRange"
				)
			    ),
			    0,
			    new TagDecodersRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "BitmapSspRange"
				)
			    ),
			    0
			)
		    ),
		    "bitmapSspRange",
		    2,
		    10
		)
	    }
	),
	1,
	new TagDecoder (
	    new TagDecoderElement[] {
		new TagDecoderElement((short)0x8000, 0),
		new TagDecoderElement((short)0x8001, 1),
		new TagDecoderElement((short)0x8002, 2)
	    }
	)
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' SspRange object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' SspRange object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Check the current selection on unknown extension
     */
    public final boolean hasUnknownExtension()
    {
	return getChosenFlag() > 3;
    }
    
} // End class definition for SspRange
