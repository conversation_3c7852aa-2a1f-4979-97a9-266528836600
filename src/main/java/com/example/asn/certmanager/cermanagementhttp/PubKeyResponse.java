/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.cermanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the PubKeyResponse ASN.1 type included in the CerManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class PubKeyResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public PubKeyResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public PubKeyResponse(CertApplyType certType, OctetString cryptoModuleId, 
		    com.example.asn.certmanager.programmingtypes.VerificationKeyIndicator verifyKeyInfo, 
		    com.example.asn.certmanager.programmingtypes.SignatureValue signatureValue)
    {
	setCertType(certType);
	setCryptoModuleId(cryptoModuleId);
	setVerifyKeyInfo(verifyKeyInfo);
	setSignatureValue(signatureValue);
    }
    
    public void initComponents()
    {
	mComponents[0] = CertApplyType.registerCertType;
	mComponents[1] = new OctetString();
	mComponents[2] = new com.example.asn.certmanager.programmingtypes.VerificationKeyIndicator();
	mComponents[3] = new com.example.asn.certmanager.programmingtypes.SignatureValue();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[4];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return CertApplyType.registerCertType;
	    case 1:
		return new OctetString();
	    case 2:
		return new com.example.asn.certmanager.programmingtypes.VerificationKeyIndicator();
	    case 3:
		return new com.example.asn.certmanager.programmingtypes.SignatureValue();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "certType"
    public CertApplyType getCertType()
    {
	return (CertApplyType)mComponents[0];
    }
    
    public void setCertType(CertApplyType certType)
    {
	mComponents[0] = certType;
    }
    
    
    // Methods for field "cryptoModuleId"
    public OctetString getCryptoModuleId()
    {
	return (OctetString)mComponents[1];
    }
    
    public void setCryptoModuleId(OctetString cryptoModuleId)
    {
	mComponents[1] = cryptoModuleId;
    }
    
    
    // Methods for field "verifyKeyInfo"
    public com.example.asn.certmanager.programmingtypes.VerificationKeyIndicator getVerifyKeyInfo()
    {
	return (com.example.asn.certmanager.programmingtypes.VerificationKeyIndicator)mComponents[2];
    }
    
    public void setVerifyKeyInfo(com.example.asn.certmanager.programmingtypes.VerificationKeyIndicator verifyKeyInfo)
    {
	mComponents[2] = verifyKeyInfo;
    }
    
    
    // Methods for field "signatureValue"
    public com.example.asn.certmanager.programmingtypes.SignatureValue getSignatureValue()
    {
	return (com.example.asn.certmanager.programmingtypes.SignatureValue)mComponents[3];
    }
    
    public void setSignatureValue(com.example.asn.certmanager.programmingtypes.SignatureValue signatureValue)
    {
	mComponents[3] = signatureValue;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.cermanagementhttp",
	    "PubKeyResponse"
	),
	new QName (
	    "CerManagementHTTP",
	    "PubKeyResponse"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.cermanagementhttp",
				"CertApplyType"
			    ),
			    new QName (
				"CerManagementHTTP",
				"CertApplyType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"registerCertType",
					0
				    ),
				    new MemberListElement (
					"applicationCertType",
					1
				    ),
				    new MemberListElement (
					"registerCaCertType",
					2
				    ),
				    new MemberListElement (
					"applicationCaCertType",
					3
				    )
				}
			    ),
			    0,
			    CertApplyType.registerCertType
			)
		    ),
		    "certType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"OctetString"
			    ),
			    new QName (
				"builtin",
				"OCTET STRING"
			    ),
			    536603,
			    new SizeConstraint (
				new SingleValueConstraint (
				    new INTEGER(16)
				)
			    ),
			    new Bounds (
				Long.valueOf(16),
				Long.valueOf(16)
			    )
			)
		    ),
		    "cryptoModuleId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"VerificationKeyIndicator"
			    ),
			    new QName (
				"ProgrammingTypes",
				"VerificationKeyIndicator"
			    ),
			    536607,
			    new MultipleTypeConstraint (
				new InnerTypeConstraint[] {
				    new InnerTypeConstraint (
					3,
					0,	// verificationKey
					null
				    )
				},
				0	// FullSpecification
			    ),
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "VerificationKeyIndicator"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "verifyKeyInfo",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"SignatureValue"
			    ),
			    new QName (
				"ProgrammingTypes",
				"SignatureValue"
			    ),
			    536607,
			    null,
			    new FieldsRef (
				new QName (
				    "com.example.asn.certmanager.programmingtypes",
				    "SignatureValue"
				)
			    ),
			    3,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1),
				    new TagDecoderElement((short)0x8002, 2),
				    new TagDecoderElement((short)0x8003, 3),
				    new TagDecoderElement((short)0x8004, 4)
				}
			    )
			)
		    ),
		    "signatureValue",
		    3,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' PubKeyResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' PubKeyResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for PubKeyResponse
