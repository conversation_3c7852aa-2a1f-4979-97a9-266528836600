/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.cermanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ErrorResponse ASN.1 type included in the CerManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class ErrorResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public ErrorResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public ErrorResponse(CertProcessErrorState errorState)
    {
	setErrorState(errorState);
    }
    
    public void initComponents()
    {
	mComponents[0] = CertProcessErrorState.messageStructureError;
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[1];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return CertProcessErrorState.messageStructureError;
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "errorState"
    public CertProcessErrorState getErrorState()
    {
	return (CertProcessErrorState)mComponents[0];
    }
    
    public void setErrorState(CertProcessErrorState errorState)
    {
	mComponents[0] = errorState;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.cermanagementhttp",
	    "ErrorResponse"
	),
	new QName (
	    "CerManagementHTTP",
	    "ErrorResponse"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.cermanagementhttp",
				"CertProcessErrorState"
			    ),
			    new QName (
				"CerManagementHTTP",
				"CertProcessErrorState"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"messageStructureError",
					0
				    ),
				    new MemberListElement (
					"certStorageUpperLimitError",
					1
				    ),
				    new MemberListElement (
					"publicKeyMismatchingError",
					2
				    ),
				    new MemberListElement (
					"certIndexOutOfRangeError",
					3
				    ),
				    new MemberListElement (
					"certIndexEmptyError",
					4
				    ),
				    new MemberListElement (
					"certTypeError",
					5
				    ),
				    new MemberListElement (
					"certCaSignatureError",
					6
				    ),
				    new MemberListElement (
					"certIssureError",
					7
				    ),
				    new MemberListElement (
					"certOutOfValidityError",
					8
				    ),
				    new MemberListElement (
					"certIdMismatchingError",
					9
				    ),
				    new MemberListElement (
					"messageSignatureVerificationError",
					10
				    ),
				    new MemberListElement (
					"certRevokedError",
					11
				    ),
				    new MemberListElement (
					"ciphertextError",
					12
				    ),
				    new MemberListElement (
					"certError",
					13
				    ),
				    new MemberListElement (
					"unknownError",
					14
				    )
				}
			    ),
			    1,
			    CertProcessErrorState.messageStructureError
			)
		    ),
		    "errorState",
		    0,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ErrorResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ErrorResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ErrorResponse
