/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: unimas (Trial), License 88804Z 88804Z. */
/* Abstract syntax: com */
/* ASN.1 Java project: com.example.asn.certmanager.Certmanager */
/* Created: Sat Mar 22 11:49:21 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.example.asn.certmanager -coer -root -sampleCode pdus
 * -messageFormat msvc
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.cert.asn
 * C:/ProgramData/OSS Nokalva/asn1studio/winx64.trial/samples/basic/com.unimas.manager.asn
 */


package com.example.asn.certmanager.cermanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the CertInquiryRequest ASN.1 type included in the CerManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class CertInquiryRequest extends Sequence {
    
    /**
     * The default constructor.
     */
    public CertInquiryRequest()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public CertInquiryRequest(CertApplyType certType, 
		    com.example.asn.certmanager.programmingtypes.Uint8 certIndex)
    {
	setCertType(certType);
	setCertIndex(certIndex);
    }
    
    public void initComponents()
    {
	mComponents[0] = CertApplyType.registerCertType;
	mComponents[1] = new com.example.asn.certmanager.programmingtypes.Uint8();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return CertApplyType.registerCertType;
	    case 1:
		return new com.example.asn.certmanager.programmingtypes.Uint8();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "certType"
    public CertApplyType getCertType()
    {
	return (CertApplyType)mComponents[0];
    }
    
    public void setCertType(CertApplyType certType)
    {
	mComponents[0] = certType;
    }
    
    
    // Methods for field "certIndex"
    public com.example.asn.certmanager.programmingtypes.Uint8 getCertIndex()
    {
	return (com.example.asn.certmanager.programmingtypes.Uint8)mComponents[1];
    }
    
    public void setCertIndex(com.example.asn.certmanager.programmingtypes.Uint8 certIndex)
    {
	mComponents[1] = certIndex;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.example.asn.certmanager.cermanagementhttp",
	    "CertInquiryRequest"
	),
	new QName (
	    "CerManagementHTTP",
	    "CertInquiryRequest"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.cermanagementhttp",
				"CertApplyType"
			    ),
			    new QName (
				"CerManagementHTTP",
				"CertApplyType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"registerCertType",
					0
				    ),
				    new MemberListElement (
					"applicationCertType",
					1
				    ),
				    new MemberListElement (
					"registerCaCertType",
					2
				    ),
				    new MemberListElement (
					"applicationCaCertType",
					3
				    )
				}
			    ),
			    0,
			    CertApplyType.registerCertType
			)
		    ),
		    "certType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.example.asn.certmanager.programmingtypes",
				"Uint8"
			    ),
			    new QName (
				"ProgrammingTypes",
				"Uint8"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new com.example.asn.certmanager.programmingtypes.Uint8(0), 
				    new com.example.asn.certmanager.programmingtypes.Uint8(255),
				    0
				)
			    ),
			    new Bounds (
				Long.valueOf(0),
				Long.valueOf(255)
			    ),
			    null,
			    1
			)
		    ),
		    "certIndex",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' CertInquiryRequest object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' CertInquiryRequest object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for CertInquiryRequest
