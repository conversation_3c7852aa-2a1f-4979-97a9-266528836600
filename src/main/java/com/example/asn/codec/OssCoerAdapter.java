/*
 * OSS Nokalva COER Adapter for ASN.1 encoding/decoding
 */
package com.example.asn.codec;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import com.oss.asn1.*;
import com.example.asn.certmanager.Certmanager;

/**
 * This adapter class provides utilities to convert between Java objects and COER encoded data
 * using OSS Nokalva's ASN.1 Tools.
 */
public class OssCoerAdapter {
    
    /**
     * Encodes an ASN.1 object using COER encoding rules with OSS Nokalva
     * 
     * @param asnObject The object to encode (must be an OSS Nokalva generated class)
     * @return A byte array containing the COER-encoded data
     * @throws IOException If encoding fails
     */
    public static byte[] encode(AbstractData asnObject) throws IOException {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            Certmanager.getCOERCoder().encode(asnObject, baos);
            return baos.toByteArray();
        } catch (Exception e) {
            throw new IOException("Error encoding with OSS Nokalva COER: " + e.getMessage(), e);
        }
    }
    
    /**
     * Decodes a COER-encoded byte array to an ASN.1 object using OSS Nokalva
     * 
     * @param coerData The COER-encoded data
     * @param asnClass The class of the ASN.1 object to create
     * @return The decoded ASN.1 object
     * @throws IOException If decoding fails
     */
    public static <T extends AbstractData> T decode(byte[] coerData, Class<T> asnClass) throws IOException {
        try {
            ByteArrayInputStream bais = new ByteArrayInputStream(coerData);
            // Create an instance and decode into it
            T instance = null;
            
            try {
                // Try to create an instance
                instance = asnClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new IOException("Unable to create instance of type " + asnClass.getName() + ": " + e.getMessage(), e);
            }
            
            // Decode into the instance
            instance = Certmanager.getCOERCoder().decode(bais, instance);
            return instance;
        } catch (Exception e) {
            throw new IOException("Error decoding with OSS Nokalva COER: " + e.getMessage(), e);
        }
    }
    
    /**
     * Decodes a COER-encoded byte array into an existing ASN.1 object
     * 
     * @param coerData The COER-encoded data
     * @param asnObject The ASN.1 object to decode into
     * @throws IOException If decoding fails
     */
    public static void decodeInto(byte[] coerData, AbstractData asnObject) throws IOException {
        try {
            ByteArrayInputStream bais = new ByteArrayInputStream(coerData);
            Certmanager.getCOERCoder().decode(bais, asnObject);
        } catch (Exception e) {
            throw new IOException("Error decoding with OSS Nokalva COER: " + e.getMessage(), e);
        }
    }
}
