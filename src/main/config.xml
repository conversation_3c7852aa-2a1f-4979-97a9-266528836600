<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<preferences EXTERNAL_XML_VERSION="1.0">
  <root type="system">
    <map/>
    <node name="apps">
      <map>
        <entry key="servicenum" value="80"/>
        <entry key="servicecount" value="0"/>
        <entry key="isSrc" value="true"/>
        <entry key="appcount" value="BTM0_app1"/>
        <entry key="channelcount" value="0"/>
      </map>
      <node name="BTM0_app1">
        <map>
          <entry key="departmentName" value="unimas"/>
          <entry key="competentPhone" value="123456"/>
          <entry key="isImport" value="false"/>
          <entry key="competentName" value="unimas"/>
          <entry key="displayname" value="unimas"/>
        </map>
      <node name="11">
          <map>
            <entry key="scheduleid" value="11"/>
            <entry key="displayname" value="unimas_test"/>
            <entry key="status" value="stopped"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
            <entry key="network" value="0"/>
          </map>
        </node>
        <node name="13">
          <map>
            <entry key="scheduleid" value="13"/>
            <entry key="displayname" value="ttt1111"/>
            <entry key="status" value="configured"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
        <node name="14">
          <map>
            <entry key="scheduleid" value="14"/>
            <entry key="displayname" value="udp123"/>
            <entry key="status" value="stopped"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
        <node name="15">
          <map>
            <entry key="scheduleid" value="15"/>
            <entry key="displayname" value="test_udp"/>
            <entry key="status" value="stopped"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
        <node name="16">
          <map>
            <entry key="scheduleid" value="16"/>
            <entry key="displayname" value="test16"/>
            <entry key="status" value="stopped"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
        <node name="17">
          <map>
            <entry key="scheduleid" value="17"/>
            <entry key="displayname" value="test17"/>
            <entry key="status" value="deployed"/>
            <entry key="syncStatus" value="SENDER_ADDED_NO_SERVICE"/>
            <entry key="isRun" value="false"/>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
      <node name="18">
          <map>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="displayname" value="test18"/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="secstate" value="1"/>
            <entry key="isRun" value="false"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="scheduleid" value="18"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
      <node name="19">
          <map>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="displayname" value="test_unimas111"/>
            <entry key="secstate" value="1"/>
            <entry key="isRun" value="false"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="scheduleid" value="19"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
      <node name="20">
          <map>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="displayname" value="test123"/>
            <entry key="secstate" value="1"/>
            <entry key="isRun" value="false"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="scheduleid" value="20"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
      <node name="21">
          <map>
            <entry key="creator" value="unimas"/>
            <entry key="isAudit" value="true"/>
            <entry key="templateid" value=""/>
            <entry key="type" value="11"/>
            <entry key="importServiceId" value=""/>
            <entry key="servicetype" value="udp"/>
            <entry key="configedtime" value="1"/>
            <entry key="displayname" value="TESTADS&#0;"/>
            <entry key="secstate" value="1"/>
            <entry key="isRun" value="false"/>
            <entry key="istemplate" value="false"/>
            <entry key="seclevel" value="4"/>
            <entry key="scheduleid" value="21"/>
            <entry key="flowlevel" value="10"/>
          </map>
        </node>
      </node>
    </node>
  </root>
</preferences>
