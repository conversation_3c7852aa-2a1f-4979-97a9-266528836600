<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>网络配置管理</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <link rel="stylesheet" href="/css/network.css">
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('network')"></div>

    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-ethernet"></i> 网络配置管理</h2>
        </div>

        <!-- 消息容器 -->
        <div id="message-container">
            <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i> <span th:text="${success}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i> <span th:text="${error}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>

        <!-- 网络接口列表 -->
        <div class="card">
            <div class="card-header">
                <i class="bi bi-list"></i> 网络接口列表
            </div>
            <div class="card-body">
                <div th:if="${interfaces == null || interfaces.isEmpty()}" class="alert alert-info">
                    <i class="bi bi-info-circle-fill me-2"></i> 暂无网络接口配置，请点击"新增网络配置"按钮添加。
                </div>

                <div th:if="${interfaces != null && !interfaces.isEmpty()}" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>网卡名称</th>
                                <th>IP地址</th>
                                <th>子网掩码</th>
                                <th>网关</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="iface : ${interfaces}">
                                <td th:text="${iface.interfaceName}">eth0</td>
                                <td th:text="${iface.ipAddress}">***********</td>
                                <td th:text="${iface.subnetMask}">*************</td>
                                <td th:text="${iface.gateway}">*************</td>
                                <td>
                                    <span th:if="${iface.enabled}" class="badge bg-success status-enabled">启用</span>
                                    <span th:unless="${iface.enabled}" class="badge bg-secondary status-disabled">禁用</span>
                                </td>
                                <td th:text="${#temporals.format(iface.updatedAt, 'yyyy-MM-dd HH:mm:ss')}">2025-03-18 15:00:00</td>
                                <td class="actions-column">
                                    <div class="btn-group" role="group" th:if="${iface.ipAddress != '*************' && iface.ipAddress != '*************'}">
                                        <!-- eth0不可编辑 -->
                                        <a th:href="@{/network/edit/{id}(id=${iface.id})}" class="btn btn-sm btn-info"
                                           th:if="${iface.interfaceName != 'eth0'}">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </a>
                                        <button th:if="${!iface.enabled}" class="btn btn-sm btn-success toggle-status"
                                                th:data-id="${iface.id}" th:data-enabled="true">
                                            <i class="bi bi-play-fill"></i> 启用
                                        </button>
                                        <button th:if="${iface.enabled && iface.interfaceName != 'eth0'}" class="btn btn-sm btn-warning toggle-status"
                                                th:data-id="${iface.id}" th:data-enabled="false">
                                            <i class="bi bi-pause-fill"></i> 禁用
                                        </button>
                                        <button class="btn btn-sm btn-primary virtual-interfaces"
                                                th:data-id="${iface.id}" th:data-name="${iface.interfaceName}">
                                            <i class="bi bi-layers"></i> 虚拟网卡
                                        </button>
                                        <button class="btn btn-sm btn-danger delete-interface"
                                                th:data-id="${iface.id}" th:data-name="${iface.interfaceName}" th:if="${iface.interfaceName != 'eth0'}">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 路由配置管理 -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0"><i class="bi bi-signpost-2"></i> 路由设置</h5>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="addRouteBtn">
                    <i class="bi bi-plus-circle"></i> 新增路由
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="routeTable">
                        <thead>
                            <tr>
                                <th>目标网络</th>
                                <th>子网掩码</th>
                                <th>路由地址</th>
                                <th>网卡设备</th>
                                <th>优先级</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="route : ${routes}">
                                <td th:text="${route.targetNetwork}">***********</td>
                                <td th:text="${route.netmask}">*************</td>
                                <td th:text="${route.gateway}">***********</td>
                                <td th:text="${route.deviceName}">eth0</td>
                                <td th:text="${route.priority != null ? route.priority : ''}"></td>
                                <td th:text="${#temporals.format(route.updatedAt, 'yyyy-MM-dd HH:mm:ss')}">2025-01-30 15:09:34</td>
                                <td>
                                    <!-- 只有via方式的路由才可以编辑删除 -->
                                    <div th:if="${route.routeType == 'via'}">
                                        <button type="button" class="btn btn-warning btn-sm edit-route"
                                                th:data-id="${route.id}"
                                                th:data-target="${route.targetNetwork}"
                                                th:data-netmask="${route.netmask}"
                                                th:data-gateway="${route.gateway}"
                                                th:data-device="${route.deviceName}"
                                                th:data-priority="${route.priority}">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm delete-route"
                                                th:data-id="${route.id}">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                    <!-- 非via方式的路由显示不可操作 -->
                                    <div th:unless="${route.routeType == 'via'}">
                                        <span class="text-muted">
                                            <i class="bi bi-lock"></i> 不可编辑
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div th:if="${routes == null || routes.isEmpty()}" class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i> 暂无路由配置，请点击"新增路由"按钮添加。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 虚拟网卡配置模态框 -->
    <div class="modal fade" id="virtualInterfaceModal" tabindex="-1" aria-labelledby="virtualInterfaceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="virtualInterfaceModalLabel"><i class="bi bi-layers"></i> 虚拟网卡配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 id="parentInterfaceName">物理网卡: </h6>
                        <button class="btn btn-sm btn-success" id="addVirtualInterfaceBtn">
                            <i class="bi bi-plus-circle"></i> 新增虚拟网卡
                        </button>
                    </div>

                    <!-- 虚拟网卡表格 -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="virtualInterfaceTable">
                            <thead>
                                <tr>
                                    <th>网卡名称</th>
                                    <th>IP地址</th>
                                    <th>子网掩码</th>
                                    <th>网关</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 虚拟网卡列表将通过Ajax动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="alert alert-info" id="noVirtualInterfaces" style="display:none;">
                        <i class="bi bi-info-circle-fill me-2"></i> 暂无虚拟网卡配置，请点击"新增虚拟网卡"按钮添加。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 虚拟网卡编辑模态框 -->
    <div class="modal fade" id="virtualInterfaceEditModal" tabindex="-1" aria-labelledby="virtualInterfaceEditModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="virtualInterfaceEditModalLabel"><i class="bi bi-layers"></i> <span id="editFormTitle">新增虚拟网卡</span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="virtualInterfaceForm">
                        <input type="hidden" id="parentId" name="parentId">
                        <input type="hidden" id="virtualId" name="virtualId">
                        <input type="hidden" id="isNewVirtual" name="isNewVirtual" value="true">

                        <div class="mb-3">
                            <label for="virtualInterfaceName" class="form-label">网卡名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="virtualInterfaceName" name="interfaceName" required
                                   placeholder="例如: eth0:1" readonly>
                            <div class="form-text">虚拟网卡名称将自动生成，格式为"物理网卡名:编号"</div>
                        </div>

                        <div class="mb-3">
                            <label for="virtualIpAddress" class="form-label">IP地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="virtualIpAddress" name="ipAddress" required
                                   pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的IPv4地址"
                                   placeholder="例如: ***********" autocomplete="off">
                        </div>

                        <div class="mb-3">
                            <label for="virtualSubnetMask" class="form-label">子网掩码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="virtualSubnetMask" name="subnetMask" required
                                   pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的子网掩码"
                                   placeholder="例如: *************" autocomplete="off">
                        </div>

                        <!-- 网关字段，只有eth0:1可以配置 -->
                        <div class="mb-3" id="virtualGatewayField" style="display: none;">
                            <label for="virtualGateway" class="form-label">网关</label>
                            <input type="text" class="form-control" id="virtualGateway" name="gateway"
                                   pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的IPv4地址"
                                   placeholder="例如: ***********" autocomplete="off">
                            <div class="form-text">只有eth0:1可以配置网关地址</div>
                        </div>

                        <div class="mb-3" id="virtualEnabledField">
                            <label for="virtualEnabled" class="form-label">状态</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="virtualEnabled" name="enabled" checked>
                                <label class="form-check-label" for="virtualEnabled">启用</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveVirtualInterfaceBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 路由配置模态框 -->
    <div class="modal fade" id="routeConfigModal" tabindex="-1" aria-labelledby="routeConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="routeConfigModalLabel"><i class="bi bi-signpost-2"></i> <span id="routeFormTitle">新增路由配置</span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="routeConfigForm">
                        <input type="hidden" id="routeId" name="routeId">
                        <input type="hidden" id="isNewRoute" name="isNewRoute" value="true">

                        <div class="mb-3">
                            <label for="targetNetwork" class="form-label">目标网络 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="targetNetwork" name="targetNetwork" required
                                   pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的IPv4地址"
                                   placeholder="例如: ***********" autocomplete="off">
                            <div class="form-text">新增可填，修改不可编辑</div>
                        </div>

                        <div class="mb-3">
                            <label for="netmask" class="form-label">子网掩码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="netmask" name="netmask" required
                                   pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的子网掩码"
                                   placeholder="例如: *************" autocomplete="off">
                            <div class="form-text">新增可填，修改不可编辑</div>
                        </div>

                        <div class="mb-3">
                            <label for="gateway" class="form-label">路由地址 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="gateway" name="gateway" required
                                   pattern="^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                                   title="请输入有效的IPv4地址"
                                   placeholder="例如: ***********" autocomplete="off">
                            <div class="form-text">新增修改都允许更改</div>
                        </div>

                        <div class="mb-3">
                            <label for="deviceName" class="form-label">网卡设备 <span class="text-danger">*</span></label>
                            <select class="form-select" id="deviceName" name="deviceName" required>
                                <option value="">请选择网卡设备</option>
                            </select>
                            <div class="form-text">根据已有的网卡进行下拉选择</div>
                        </div>

                        <div class="mb-3">
                            <label for="priority" class="form-label">优先级 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="priority" name="priority"
                                   min="0" max="65535" value="100" required>
                            <div class="form-text">路由优先级，范围0-65535，数值越小优先级越高，默认100</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <!-- 错误信息显示区域 -->
                    <div id="routeErrorMessage" class="alert alert-danger d-none me-auto" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <span id="routeErrorText"></span>
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveRouteBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:9999;">
        <div style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); background-color:white; padding:20px; border-radius:5px;">
            <div class="d-flex align-items-center">
                <div class="spinner-border text-primary me-3" role="status"></div>
                <span id="loading-message">正在处理...</span>
            </div>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>

    <!-- JavaScript 代码已经正确实现 -->
    <script>
        // 获取eth0的网关地址
        function getEth0Gateway() {
            // 从当前页面的网卡列表中查找eth0的网关
            let eth0Gateway = null;
            $('tbody tr').each(function() {
                const interfaceName = $(this).find('td:first').text().trim();
                if (interfaceName === 'eth0') {
                    const gatewayCell = $(this).find('td:nth-child(4)').text().trim(); // 网关列
                    if (gatewayCell && gatewayCell !== '-') {
                        eth0Gateway = gatewayCell;
                    }
                    return false; // 找到后退出循环
                }
            });
            return eth0Gateway;
        }

        $(document).ready(function() {
            // 初始化事件绑定
            bindNetworkConfigEvents();

            // 检测网卡按钮点击事件
            $('#detectInterfacesBtn').click(function(e) {
                e.preventDefault();
                showLoading('正在检测网卡...');

                $.ajax({
                    url: '/network/detect',
                    type: 'GET',
                    success: function(interfaces) {
                        hideLoading();
                        if (interfaces && interfaces.length > 0) {
                            showMessage('success', '检测到 ' + interfaces.length + ' 个网络接口');
                            setTimeout(function() { location.reload(); }, 2000);
                        } else {
                            showMessage('info', '未检测到新的网络接口');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showMessage('error', '检测网卡失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                    }
                });
            });

            // 启用/禁用按钮点击事件
            $('.toggle-status').click(function() {
                var btn = $(this);
                var id = btn.data('id');
                var enabled = btn.data('enabled');
                var action = enabled ? '启用' : '禁用';

                if (confirm('确定要' + action + '此网络接口吗?')) {
                    showLoading('正在' + action + '网络接口...');

                    $.ajax({
                        url: '/network/toggle/' + id,
                        type: 'POST',
                        data: { 'enabled': enabled },
                        success: function(response) {
                            hideLoading();
                            if (response.success) {
                                showMessage('success', action + '成功', response.message);
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', action + '失败', response.message);
                            }
                        },
                        error: function(xhr) {
                            hideLoading();
                            showMessage('error', action + '失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                        }
                    });
                }
            });

            // 删除按钮点击事件
            $('.delete-interface').click(function() {
                var btn = $(this);
                var id = btn.data('id');
                var name = btn.data('name');

                if (confirm('确定要删除网络接口 "' + name + '" 吗? 此操作不可恢复!')) {
                    showLoading('正在删除网络接口...');

                    $.ajax({
                        url: '/network/delete/' + id,
                        type: 'POST',
                        success: function(response) {
                            hideLoading();
                            if (response.success) {
                                showMessage('success', '删除成功', response.message);
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', '删除失败', response.message);
                            }
                        },
                        error: function(xhr) {
                            hideLoading();
                            showMessage('error', '删除失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                        }
                    });
                }
            });

            // 虚拟网卡按钮点击事件
            $('.virtual-interfaces').click(function() {
                const parentId = $(this).data('id');
                const parentName = $(this).data('name');

                // 设置父网卡信息
                $('#parentInterfaceName').text('物理网卡: ' + parentName);
                $('#parentId').val(parentId);

                // 加载虚拟网卡列表
                loadVirtualInterfaces(parentName);

                // 显示模态框
                $('#virtualInterfaceModal').modal('show');
            });

            // 加载虚拟网卡列表
            function loadVirtualInterfaces(parentName) {
                showLoading('正在加载虚拟网卡...');

                $.ajax({
                    url: '/network/virtual/' + parentName,
                    type: 'GET',
                    success: function(virtualInterfaces) {
                        hideLoading();
                        updateVirtualInterfaceTable(virtualInterfaces, parentName);
                    },
                    error: function(xhr) {
                        hideLoading();
                        showMessage('error', '加载虚拟网卡失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                    }
                });
            }

            // 更新虚拟网卡表格
            function updateVirtualInterfaceTable(virtualInterfaces, parentName) {
                const tableBody = $('#virtualInterfaceTable tbody');
                tableBody.empty();

                // 控制新增按钮显示：eth0存在eth0:1时隐藏新增按钮
                if (parentName === 'eth0') {
                    const hasEth01 = virtualInterfaces && virtualInterfaces.some(iface => iface.interfaceName === 'eth0:1');
                    if (hasEth01) {
                        $('#addVirtualInterfaceBtn').hide();
                    } else {
                        $('#addVirtualInterfaceBtn').show();
                    }
                } else {
                    $('#addVirtualInterfaceBtn').show();
                }

                if (virtualInterfaces && virtualInterfaces.length > 0) {
                    $('#noVirtualInterfaces').hide();

                    virtualInterfaces.forEach(function(iface) {
                        const statusBadge = iface.enabled ?
                            '<span class="badge bg-success">启用</span>' :
                            '<span class="badge bg-secondary">禁用</span>';

                        // eth0:1显示网关，其他虚拟网卡不显示
                        let gatewayDisplay = '-';
                        if (iface.interfaceName === 'eth0:1') {
                            // 直接使用后端返回的网关地址
                            gatewayDisplay = iface.gateway || '-';
                        }

                        // eth0的虚拟网卡不能禁用，隐藏禁用按钮
                        const isEth0Virtual = iface.interfaceName.startsWith('eth0:');
                        const toggleButton = isEth0Virtual ? '' :
                            `<button class="btn btn-sm ${iface.enabled ? 'btn-warning' : 'btn-success'} toggle-virtual-status"
                                    data-id="${iface.id}" data-enabled="${!iface.enabled}">
                                <i class="bi bi-${iface.enabled ? 'pause-fill' : 'play-fill'}"></i> ${iface.enabled ? '禁用' : '启用'}
                            </button><button class="btn btn-sm btn-danger delete-virtual-interface"
                                                data-id="${iface.id}" data-name="${iface.interfaceName}">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>`;

                        const row = `
                            <tr>
                                <td>${iface.interfaceName}</td>
                                <td>${iface.ipAddress}</td>
                                <td>${iface.subnetMask}</td>
                                <td>${gatewayDisplay}</td>
                                <td>${statusBadge}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-info edit-virtual-interface"
                                                data-id="${iface.id}" data-parent="${parentName}"
                                                data-gateway="${iface.gateway || ''}">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </button>
                                        ${toggleButton}
                                    </div>
                                </td>
                            </tr>
                        `;
                        tableBody.append(row);
                    });

                    // 重新绑定事件
                    bindVirtualInterfaceEvents();
                } else {
                    $('#noVirtualInterfaces').show();
                }
            }

            // 绑定虚拟网卡相关事件
            function bindVirtualInterfaceEvents() {
                // 编辑虚拟网卡
                $('.edit-virtual-interface').click(function() {
                    const virtualId = $(this).data('id');
                    const parentName = $(this).data('parent');

                    // 加载虚拟网卡详情
                    $.ajax({
                        url: '/network/virtual/detail/' + virtualId,
                        type: 'GET',
                        success: function(virtualInterface) {
                            // 填充表单
                            $('#editFormTitle').text('编辑虚拟网卡');
                            $('#isNewVirtual').val('false');
                            $('#virtualId').val(virtualInterface.id);
                            $('#virtualInterfaceName').val(virtualInterface.interfaceName);
                            $('#virtualIpAddress').val(virtualInterface.ipAddress);
                            $('#virtualSubnetMask').val(virtualInterface.subnetMask);
                            $('#virtualEnabled').prop('checked', virtualInterface.enabled);

                            // 控制网关字段显示：只有eth0:1可以配置网关
                            if (virtualInterface.interfaceName === 'eth0:1') {
                                $('#virtualGatewayField').show();
                                // eth0:1的网关取eth0的网关地址
                                const eth0Gateway = getEth0Gateway();
                                $('#virtualGateway').val(virtualInterface.gateway || eth0Gateway || '');
                            } else {
                                $('#virtualGatewayField').hide();
                                $('#virtualGateway').val('');
                            }

                            // 控制状态字段：eth0的虚拟网卡不可禁用
                            if (virtualInterface.interfaceName.startsWith('eth0:')) {
                                $('#virtualEnabled').prop('checked', true).prop('disabled', true);
                                $('#virtualEnabledField .form-check-label').text('启用（eth0虚拟网卡不可禁用）');
                            } else {
                                $('#virtualEnabled').prop('checked', virtualInterface.enabled).prop('disabled', false);
                                $('#virtualEnabledField .form-check-label').text('启用');
                            }

                            // 显示编辑模态框
                            $('#virtualInterfaceEditModal').modal('show');
                        },
                        error: function(xhr) {
                            showMessage('error', '加载虚拟网卡详情失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                        }
                    });
                });

                // 删除虚拟网卡
                $('.delete-virtual-interface').click(function() {
                    const virtualId = $(this).data('id');
                    const virtualName = $(this).data('name');

                    if (confirm('确定要删除虚拟网卡 "' + virtualName + '" 吗? 此操作不可恢复!')) {
                        showLoading('正在删除虚拟网卡...');

                        $.ajax({
                            url: '/network/virtual/delete/' + virtualId,
                            type: 'POST',
                            success: function(response) {
                                hideLoading();
                                if (response.success) {
                                    showMessage('success', '删除成功', response.message);
                                    // 重新加载虚拟网卡列表
                                    const parentName = $('#parentInterfaceName').text().replace('物理网卡: ', '');
                                    loadVirtualInterfaces(parentName);
                                    // 刷新路由表格
                                    refreshRouteTable();
                                } else {
                                    showMessage('error', '删除失败', response.message);
                                }
                            },
                            error: function(xhr) {
                                hideLoading();
                                showMessage('error', '删除失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                            }
                        });
                    }
                });

                // 启用/禁用虚拟网卡
                $('.toggle-virtual-status').click(function() {
                    const virtualId = $(this).data('id');
                    const enabled = $(this).data('enabled');
                    const action = enabled ? '启用' : '禁用';

                    if (confirm('确定要' + action + '此虚拟网卡吗?')) {
                        showLoading('正在' + action + '虚拟网卡...');

                        $.ajax({
                            url: '/network/virtual/toggle/' + virtualId,
                            type: 'POST',
                            data: { 'enabled': enabled },
                            success: function(response) {
                                hideLoading();
                                if (response.success) {
                                    showMessage('success', action + '成功', response.message);

                                    // 先刷新主网络配置列表（更新网关信息）
                                    refreshNetworkConfigList();

                                    // 延迟刷新虚拟网卡列表，确保主网卡列表先更新
                                    setTimeout(function() {
                                        const parentName = $('#parentInterfaceName').text().replace('物理网卡: ', '');
                                        loadVirtualInterfaces(parentName);
                                    }, 100);

                                    // 刷新路由表格
                                    refreshRouteTable();
                                } else {
                                    showMessage('error', action + '失败', response.message);
                                }
                            },
                            error: function(xhr) {
                                hideLoading();
                                showMessage('error', action + '失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                            }
                        });
                    }
                });
            }

            // 新增虚拟网卡按钮点击事件
            $('#addVirtualInterfaceBtn').click(function() {
                const parentName = $('#parentInterfaceName').text().replace('物理网卡: ', '');

                // 重置表单
                $('#virtualInterfaceForm')[0].reset();
                $('#editFormTitle').text('新增虚拟网卡');
                $('#isNewVirtual').val('true');
                $('#virtualId').val('');

                // 生成下一个可用的虚拟网卡名
                $.ajax({
                    url: '/network/virtual/nextName/' + parentName,
                    type: 'GET',
                    success: function(nextName) {
                        $('#virtualInterfaceName').val(nextName);

                        // 控制网关字段显示：只有eth0:1可以配置网关
                        if (nextName === 'eth0:1') {
                            $('#virtualGatewayField').show();
                            // eth0:1的网关取eth0的网关地址
                            const eth0Gateway = getEth0Gateway();
                            $('#virtualGateway').val(eth0Gateway || '');
                        } else {
                            $('#virtualGatewayField').hide();
                        }

                        // 控制状态字段：eth0的虚拟网卡默认启用且不可修改
                        if (nextName.startsWith('eth0:')) {
                            $('#virtualEnabled').prop('checked', true).prop('disabled', true);
                            $('#virtualEnabledField .form-check-label').text('启用（eth0虚拟网卡不可禁用）');
                        } else {
                            $('#virtualEnabled').prop('disabled', false);
                            $('#virtualEnabledField .form-check-label').text('启用');
                        }

                        // 显示编辑模态框
                        $('#virtualInterfaceEditModal').modal('show');
                    },
                    error: function(xhr) {
                        showMessage('error', '获取下一个虚拟网卡名称失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                    }
                });
            });

            // 保存虚拟网卡
            $('#saveVirtualInterfaceBtn').click(function() {
                // 表单验证
                if (!$('#virtualInterfaceForm')[0].checkValidity()) {
                    $('#virtualInterfaceForm')[0].reportValidity();
                    return;
                }

                showLoading('正在保存虚拟网卡...');

                // 获取表单数据
                const interfaceName = $('#virtualInterfaceName').val();
                const formData = {
                    id: $('#virtualId').val() || null,
                    interfaceName: interfaceName,
                    ipAddress: $('#virtualIpAddress').val(),
                    subnetMask: $('#virtualSubnetMask').val(),
                    enabled: interfaceName.startsWith('eth0:') ? true : $('#virtualEnabled').is(':checked'), // eth0的虚拟网卡强制启用
                    parentId: $('#parentId').val(),
                    isNew: $('#isNewVirtual').val() === 'true'
                };

                // 只有eth0:1可以配置网关
                if (interfaceName === 'eth0:1') {
                    formData.gateway = $('#virtualGateway').val();
                }

                $.ajax({
                    url: '/network/virtual/save',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        hideLoading();
                        if (response.success) {
                            showMessage('success', '保存成功', response.message);
                            // 关闭编辑模态框
                            $('#virtualInterfaceEditModal').modal('hide');

                            // 先刷新主网络配置列表（更新网关信息）
                            refreshNetworkConfigList();

                            // 延迟刷新虚拟网卡列表，确保主网卡列表先更新
                            setTimeout(function() {
                                const parentName = $('#parentInterfaceName').text().replace('物理网卡: ', '');
                                loadVirtualInterfaces(parentName);
                            }, 100);

                            // 刷新路由表格
                            refreshRouteTable();
                        } else {
                            showMessage('error', '保存失败', response.message);
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        showMessage('error', '保存失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                    }
                });
            });


            // 消息和加载提示功能
            function showMessage(type, title, message) {
                var alertClass = 'alert-info';
                if (type === 'success') alertClass = 'alert-success';
                if (type === 'error') alertClass = 'alert-danger';

                var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                                '<strong>' + title + ':</strong> ' + (message || '') +
                                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                '</div>';

                // 添加到消息容器
                $('#message-container').html(alertHtml);

                // 自动消失
                setTimeout(function() {
                    $('.alert').alert('close');
                }, 5000);
            }

            function showLoading(message) {
                $('#loading-message').text(message || '正在处理...');
                $('#loading-overlay').show();
            }

            function hideLoading() {
                $('#loading-overlay').hide();
            }

            // 路由配置管理功能

            // 新增路由按钮点击事件
            $('#addRouteBtn').click(function() {
                // 重置表单
                $('#routeConfigForm')[0].reset();
                $('#routeFormTitle').text('新增路由配置');
                $('#isNewRoute').val('true');
                $('#routeId').val('');

                // 启用目标网络和子网掩码字段
                $('#targetNetwork').prop('readonly', false);
                $('#netmask').prop('readonly', false);

                // 隐藏错误信息
                $('#routeErrorMessage').addClass('d-none');

                // 加载可用的网卡设备
                loadAvailableDevices();

                // 显示模态框
                $('#routeConfigModal').modal('show');
            });

            // 编辑路由按钮点击事件
            $(document).on('click', '.edit-route', function() {
                const routeId = $(this).data('id');
                const targetNetwork = $(this).data('target');
                const netmask = $(this).data('netmask');
                const gateway = $(this).data('gateway');
                const deviceName = $(this).data('device');
                const priority = $(this).data('priority'); // 不设置默认值，保持原始值

                // 重置表单
                $('#routeConfigForm')[0].reset();
                $('#routeFormTitle').text('编辑路由配置');
                $('#isNewRoute').val('false');
                $('#routeId').val(routeId);

                // 隐藏错误信息
                $('#routeErrorMessage').addClass('d-none');

                // 填充表单数据
                $('#targetNetwork').val(targetNetwork).prop('readonly', true);
                $('#netmask').val(netmask).prop('readonly', true);
                $('#gateway').val(gateway);
                // 优先级：有值显示实际值，没有值显示空
                $('#priority').val(priority != null && priority !== 'null' && priority !== '' ? priority : '');

                // 加载可用的网卡设备并设置当前值
                loadAvailableDevices(deviceName);

                // 显示模态框
                $('#routeConfigModal').modal('show');
            });

            // 删除路由按钮点击事件
            $(document).on('click', '.delete-route', function() {
                const routeId = $(this).data('id');

                if (confirm('确定要删除此路由配置吗？删除后将无法恢复。')) {
                    showLoading('正在删除路由配置...');

                    $.ajax({
                        url: '/network/routes/delete/' + routeId,
                        type: 'POST',
                        success: function(response) {
                            hideLoading();
                            if (response.success) {
                                showMessage('success', '删除成功', response.message);
                                setTimeout(function() { location.reload(); }, 2000);
                            } else {
                                showMessage('error', '删除失败', response.message);
                            }
                        },
                        error: function(xhr) {
                            hideLoading();
                            showMessage('error', '删除失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                        }
                    });
                }
            });

            // 保存路由配置
            $('#saveRouteBtn').click(function() {
                // 隐藏之前的错误信息
                $('#routeErrorMessage').addClass('d-none');

                // 表单验证
                if (!$('#routeConfigForm')[0].checkValidity()) {
                    $('#routeConfigForm')[0].reportValidity();
                    return;
                }

                showLoading('正在保存路由配置...');

                const isNew = $('#isNewRoute').val() === 'true';
                const routeId = $('#routeId').val();

                // 获取表单数据
                const priorityValue = $('#priority').val();
                const formData = {
                    targetNetwork: $('#targetNetwork').val(),
                    netmask: $('#netmask').val(),
                    gateway: $('#gateway').val(),
                    deviceName: $('#deviceName').val(),
                    priority: priorityValue && priorityValue.trim() !== '' ? parseInt(priorityValue) : null
                };

                const url = isNew ? '/network/routes/add' : '/network/routes/update/' + routeId;

                $.ajax({
                    url: url,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        hideLoading();
                        if (response.success) {
                            showMessage('success', '保存成功', response.message);
                            // 关闭模态框
                            $('#routeConfigModal').modal('hide');
                            // 刷新页面
                            setTimeout(function() { location.reload(); }, 2000);
                        } else {
                            // 在弹窗内显示错误信息
                            showRouteError(response.message || '保存失败，请检查输入信息');
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        let errorMessage = '请求处理失败';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseText) {
                            try {
                                const errorData = JSON.parse(xhr.responseText);
                                errorMessage = errorData.message || errorMessage;
                            } catch (e) {
                                errorMessage = xhr.statusText || errorMessage;
                            }
                        }
                        // 在弹窗内显示错误信息
                        showRouteError(errorMessage);
                    }
                });
            });

            // 在路由弹窗内显示错误信息
            function showRouteError(message) {
                $('#routeErrorText').text(message);
                $('#routeErrorMessage').removeClass('d-none');
            }

            // 加载可用的网卡设备
            function loadAvailableDevices(selectedDevice) {
                $.ajax({
                    url: '/network/routes/devices',
                    type: 'GET',
                    success: function(devices) {
                        const deviceSelect = $('#deviceName');
                        deviceSelect.empty();
                        deviceSelect.append('<option value="">请选择网卡设备</option>');

                        devices.forEach(function(device) {
                            const option = $('<option></option>').attr('value', device).text(device);
                            if (selectedDevice && device === selectedDevice) {
                                option.prop('selected', true);
                            }
                            deviceSelect.append(option);
                        });
                    },
                    error: function(xhr) {
                        showMessage('error', '加载网卡设备失败', '请求处理失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                    }
                });
            }

            // 刷新路由表格
            function refreshRouteTable() {
                $.ajax({
                    url: '/network/routes',
                    type: 'GET',
                    success: function(routes) {
                        updateRouteTable(routes);
                    },
                    error: function(xhr) {
                        console.error('刷新路由表格失败:', xhr);
                    }
                });
            }


            // 更新网络配置表格
            function updateNetworkConfigTable(interfaces) {
                const tableBody = $('#networkTable tbody');
                tableBody.empty();

                interfaces.forEach(function(iface) {
                    const statusBadge = iface.enabled ?
                        '<span class="badge bg-success">启用</span>' :
                        '<span class="badge bg-secondary">禁用</span>';

                    const gatewayDisplay = iface.gateway || '';

                    // eth0不能禁用，隐藏禁用按钮
                    const isEth0 = iface.interfaceName === 'eth0';
                    const toggleButton = isEth0 ? '' :
                        `<button type="button" class="btn btn-sm ${iface.enabled ? 'btn-outline-warning' : 'btn-outline-success'} toggle-interface"
                                data-id="${iface.id}" data-enabled="${!iface.enabled}">
                            ${iface.enabled ? '禁用' : '启用'}
                        </button>`;

                    const row = `
                        <tr>
                            <td>${iface.interfaceName}</td>
                            <td>${iface.ipAddress}</td>
                            <td>${iface.subnetMask}</td>
                            <td>${gatewayDisplay}</td>
                            <td>${statusBadge}</td>
                            <td>${iface.updatedAt || ''}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary edit-interface" data-id="${iface.id}">
                                        编辑
                                    </button>
                                    ${toggleButton}
                                    ${iface.interfaceName.startsWith('eth') && !iface.interfaceName.includes(':') ?
                                        `<button type="button" class="btn btn-sm btn-outline-info virtual-interfaces" data-id="${iface.id}" data-name="${iface.interfaceName}">
                                            虚拟网卡
                                        </button>` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                    tableBody.append(row);
                });

                // 重新绑定事件处理器
                bindNetworkConfigEvents();
            }

            // 刷新主网络配置列表
            function refreshNetworkConfigList() {
                $.ajax({
                    url: '/network/interfaces',
                    type: 'GET',
                    success: function(interfaces) {
                        updateNetworkConfigTable(interfaces);
                    },
                    error: function(xhr) {
                        console.error('刷新网络配置列表失败:', xhr);
                    }
                });
            }


            // 绑定网络配置相关事件
            function bindNetworkConfigEvents() {
                // 虚拟网卡按钮点击事件
                $('.virtual-interfaces').off('click').on('click', function() {
                    const parentId = $(this).data('id');
                    const parentName = $(this).data('name');

                    // 设置父网卡信息
                    $('#parentInterfaceName').text('物理网卡: ' + parentName);
                    $('#parentId').val(parentId);

                    // 加载虚拟网卡列表
                    loadVirtualInterfaces(parentName);

                    // 显示模态框
                    $('#virtualInterfaceModal').modal('show');
                });

                // 其他事件绑定...
            }

            // 更新路由表格
            function updateRouteTable(routes) {
                const tableBody = $('#routeTable tbody');
                tableBody.empty();

                if (routes && routes.length > 0) {
                    routes.forEach(function(route) {
                        const statusBadge = route.routeType === 'via' ?
                            `<button type="button" class="btn btn-warning btn-sm edit-route"
                                data-id="${route.id}"
                                data-target="${route.targetNetwork}"
                                data-netmask="${route.netmask}"
                                data-gateway="${route.gateway}"
                                data-device="${route.deviceName}">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <button type="button" class="btn btn-danger btn-sm delete-route"
                                data-id="${route.id}">
                                <i class="bi bi-trash"></i> 删除
                            </button>` :
                            `<span class="text-muted">
                                <i class="bi bi-lock"></i> 不可编辑
                            </span>`;

                        // 格式化时间显示
                        const formattedTime = route.updatedAt ? formatDateTime(route.updatedAt) : '-';

                        // 格式化优先级显示：有值显示实际值，没有值显示空
                        const priorityDisplay = route.priority != null ? route.priority : '';

                        const row = `
                            <tr>
                                <td>${route.targetNetwork}</td>
                                <td>${route.netmask}</td>
                                <td>${route.gateway}</td>
                                <td>${route.deviceName}</td>
                                <td>${priorityDisplay}</td>
                                <td>${formattedTime}</td>
                                <td>${statusBadge}</td>
                            </tr>
                        `;
                        tableBody.append(row);
                    });
                } else {
                    tableBody.append(`
                        <tr>
                            <td colspan="6" class="text-center text-muted">暂无路由配置</td>
                        </tr>
                    `);
                }
            }

            // 格式化日期时间
            function formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '-';
                try {
                    const date = new Date(dateTimeStr);
                    return date.getFullYear() + '-' +
                           String(date.getMonth() + 1).padStart(2, '0') + '-' +
                           String(date.getDate()).padStart(2, '0') + ' ' +
                           String(date.getHours()).padStart(2, '0') + ':' +
                           String(date.getMinutes()).padStart(2, '0') + ':' +
                           String(date.getSeconds()).padStart(2, '0');
                } catch (e) {
                    return dateTimeStr;
                }
            }
        });
    </script>
</body>
</html>