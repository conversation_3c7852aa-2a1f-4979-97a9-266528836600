<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本信息 - 安全隔离单向输出模块</title>
    <meta name="_csrf" th:content="${_csrf?.token}"/>
    <meta name="_csrf_header" th:content="${_csrf?.headerName}"/>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/bootstrap-icons.css">
    <style>

    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- 导航栏 -->
    <header th:replace="fragments/navigation :: navbar('version-info')"></header>

    <div class="container">
        <div class="row">
            <!-- 主内容区 -->
            <main class="col-md-9 col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">版本信息</h1>
                </div>

                <div class="history-container">
                    <!-- 有历史记录时显示 -->
                    <div th:if="${versionInfo}">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">安全隔离单向输出模块</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th scope="col">版本号</th>
                                                <th scope="col">编号</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td th:text="${versionInfo.version}"></td>
                                                <td th:text="${versionInfo.releaseNum}"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 升级历史 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">版本升级历史</h5>
                        </div>
                        <div class="card-body">
                            <div id="upgradeHistoryContainer">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">正在加载升级历史...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </main>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            console.log('版本信息页面加载完成');

            // 加载升级历史
            loadUpgradeHistory();
        });

        // 加载升级历史
        function loadUpgradeHistory() {
            // 获取CSRF令牌
            const token = $("meta[name='_csrf']").attr("content");
            const header = $("meta[name='_csrf_header']").attr("content");

            $.ajax({
                url: '/version-info/upgrade/history',
                type: 'GET',
                beforeSend: function(xhr) {
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }
                },
                success: function(data) {
                    console.log('升级历史:', data);
                    displayUpgradeHistory(data);
                },
                error: function(xhr, status, error) {
                    console.error('获取升级历史失败:', error);
                    $('#upgradeHistoryContainer').html(`
                        <div class="alert alert-warning" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            无法获取升级历史信息
                        </div>
                    `);
                }
            });
        }

        // 显示升级历史
        function displayUpgradeHistory(historyData) {
            const container = $('#upgradeHistoryContainer');

            if (!historyData || historyData.length === 0) {
                container.html(`
                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        暂无升级历史记录
                    </div>
                `);
                return;
            }

            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th scope="col">补丁名称</th>
                                <th scope="col">升级时间</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            historyData.forEach(item => {
                tableHtml += `
                    <tr>
                        <td>${item.patch_name || '未知'}</td>
                        <td>${item.time || '未知'}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            container.html(tableHtml);
        }
    </script>
</body>
</html>