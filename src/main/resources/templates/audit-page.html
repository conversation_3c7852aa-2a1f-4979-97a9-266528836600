<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务审计日志</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <style>
        .table-container {
            overflow-x: auto;
        }

        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .status-badge {
            font-size: 0.85em;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }

        /* 时间验证错误样式 */
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
        }

        .time-error, .year-error {
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 0.25rem;
        }

        /* 时间控件样式 */
        input[type="datetime-local"] {
            cursor: pointer;
        }

        input[type="datetime-local"]:focus {
            outline: none;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* 时间控件样式优化 */
        input[type="datetime-local"] {
            min-width: 200px;
            font-family: monospace;
        }

    </style>
</head>
<body>
    <div th:replace="fragments/navigation :: navbar('audit')"></div>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-journal-text"></i> 服务审计日志</h2>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bi bi-funnel"></i> 筛选
                </button>
            </div>
        </div>

        <!-- 数据库错误提示 -->
        <div class="alert alert-danger" th:if="${databaseError}" role="alert">
            <h4 class="alert-heading"><i class="bi bi-exclamation-triangle-fill"></i> 数据库连接错误</h4>
            <p>系统无法连接到数据库，请检查数据库服务是否正常运行。</p>
            <hr>
            <p class="mb-0" th:text="${errorMessage}">错误详情</p>
            <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="window.location.reload()">
                <i class="bi bi-arrow-clockwise"></i> 重试连接
            </button>
        </div>

        <div class="collapse show filter-form" id="filterCollapse">
            <form th:action="@{/audit/search}" method="get">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="beginTime" class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="beginTime" name="beginTime" th:value="${beginTime}" step="1" min="2000-01-01T00:00:00" max="9999-12-31T23:59:59">
                    </div>
                    <div class="col-md-3">
                        <label for="endTime" class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="endTime" name="endTime" th:value="${endTime}" step="1" min="2000-01-01T00:00:00" max="9999-12-31T23:59:59">
                    </div>
                    <div class="col-md-3">
                        <label for="serviceId" class="form-label">服务名称</label>
                        <select class="form-select" id="serviceId" name="serviceId">
                            <option value="">所有服务</option>
                            <option th:each="service : ${services}"
                                    th:value="${service.id}"
                                    th:text="${service.name}"
                                    th:selected="${service.id == selectedServiceId}"></option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="clientIp" class="form-label">客户端IP</label>
                        <input type="text" class="form-control" id="clientIp" name="clientIp"
                               th:value="${clientIp}" placeholder="不填写则查询所有IP" autocomplete="off">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <a href="/audit" class="btn btn-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>客户端IP</th>
                        <th>流量大小</th>
                        <th>时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${#lists.isEmpty(auditLogs) && (param.serviceId != null || param.clientIp != null || param.beginTime != null || param.endTime != null)}">
                        <td colspan="4" class="text-center">没有审计记录</td>
                    </tr>
                    <tr th:each="log : ${auditLogs}">
                        <td th:text="${log.serviceName}"></td>
                        <td th:text="${log.clientIp}"></td>
                        <td class="flow-size" th:data-bytes="${log.dataSize}" th:text="${log.dataSize != null ? log.dataSize : '-'}"></td>
                        <td th:text="${#temporals.format(log.beginTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container" th:if="${totalPages > 0}">
            <div>
                显示 <span th:text="${currentPage * pageSize + 1}"></span> -
                <span th:text="${(currentPage * pageSize) + #lists.size(auditLogs)}"></span>
                共 <span th:text="${totalItems}"></span> 条记录
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=0, size=${pageSize})}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${currentPage - 1}, size=${pageSize})}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:each="i: ${#numbers.sequence(0, totalPages - 1)}"
                        th:if="${i >= currentPage - 2 and i <= currentPage + 2}"
                        th:classappend="${i == currentPage ? 'active' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${i}, size=${pageSize})}" th:text="${i + 1}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${currentPage + 1}, size=${pageSize})}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/audit/search(serviceId=${selectedServiceId}, clientIp=${clientIp}, beginTime=${beginTime}, endTime=${endTime}, page=${totalPages - 1}, size=${pageSize})}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        // 格式化流量大小的函数
        function updateFlowShow(bytes) {
            if (bytes < 1024) {
                return bytes + "B";
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024).toFixed(2) + "KB";
            } else if (bytes < (1024 * 1024 * 1024)) {
                return (bytes / (1024 * 1024)).toFixed(2) + "MB";
            } else {
                return (bytes / (1024 * 1024 * 1024)).toFixed(2) + "GB";
            }
        }

        $(document).ready(function() {
            // 检测浏览器对datetime-local的支持
            function supportsDatetimeLocal() {
                const input = document.createElement('input');
                input.type = 'datetime-local';
                return input.type === 'datetime-local';
            }

            // 如果不支持datetime-local，添加标记类
            if (!supportsDatetimeLocal()) {
                $('body').addClass('no-datetime-local');
                console.warn('浏览器不支持datetime-local输入类型');
            }

            // 格式化流量大小显示
            $('.flow-size').each(function() {
                const bytes = $(this).attr('data-bytes');
                if (bytes && bytes !== '-') {
                    $(this).text(updateFlowShow(parseInt(bytes)));
                }
            });

            // 初始化时间控件
            initializeDateTimeControls();

            // 初始化筛选表单的折叠状态
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('serviceId') || urlParams.has('clientIp') ||
                urlParams.has('beginTime') || urlParams.has('endTime')) {
                $('#filterCollapse').addClass('show');
            }
        });

        // 初始化时间控件函数
        function initializeDateTimeControls() {
            // 设置默认时间格式
            $("#beginTime, #endTime").each(function() {
                const $input = $(this);
                const val = $input.val();

                // 如果有值但格式不完整，补充格式
                if (val) {
                    if (val.length === 16) {
                        // 只有分钟精度，添加秒
                        $input.val(val + ':00');
                    } else if (val.length === 10) {
                        // 只有日期，添加时间
                        $input.val(val + 'T00:00:00');
                    }
                }

                // 设置placeholder
                if (!val) {
                    $input.attr('placeholder', 'YYYY-MM-DD HH:MM:SS');
                }
            });

            // 格式化日期和时间输入，支持精确到秒
            $("#beginTime, #endTime").on("change", function() {
                const $input = $(this);
                const val = $input.val();

                if (val) {
                    try {
                        const date = new Date(val);
                        if (!isNaN(date.getTime())) {
                            // 格式化为 YYYY-MM-DDTHH:MM:SS
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const hours = String(date.getHours()).padStart(2, '0');
                            const minutes = String(date.getMinutes()).padStart(2, '0');
                            const seconds = String(date.getSeconds()).padStart(2, '0');

                            const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
                            $input.val(formattedDateTime);
                        }
                    } catch (e) {
                        console.error("Invalid date format", e);
                        // 如果格式错误，清空输入
                        $input.val('');
                    }
                }
            });

            // 添加输入验证
            $("#beginTime, #endTime").on("blur", function() {
                const $input = $(this);
                const val = $input.val();

                if (val && val.length > 0) {
                    // 验证日期时间格式
                    const dateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/;
                    if (!dateTimeRegex.test(val)) {
                        alert('请输入正确的日期时间格式：YYYY-MM-DD HH:MM:SS');
                        $input.focus();
                    }
                }
            });

            // 添加时间验证
            setupTimeValidation();
        }

        // 设置时间验证
        function setupTimeValidation() {
            // 移除只读属性，改用键盘事件阻止来禁止手动输入
            $("#beginTime, #endTime").removeAttr('readonly');

            // 禁用键盘输入事件，但保留控件交互
            $("#beginTime, #endTime").on('keydown keypress keyup', function(e) {
                // 允许Tab键和方向键用于导航

                e.preventDefault();
                return false;
            });

            // 时间变化时验证
            $("#beginTime, #endTime").on("change", function() {
                validateTimeRange();
            });

            // 添加额外的输入保护
            $("#beginTime, #endTime").on('paste', function(e) {
                e.preventDefault();
                return false;
            });

            // 添加输入验证，防止无效年份
            $("#beginTime, #endTime").on('input', function() {
                const val = $(this).val();
                if (val) {
                    // 检查年份是否在合理范围内
                    const year = parseInt(val.substring(0, 4));
                    if (year < 2000 || year > 9999) {
                        $(this).addClass('is-invalid');
                        if ($('.year-error').length === 0) {
                            $(this).after('<div class="year-error text-danger small mt-1">年份必须在2000-9999之间</div>');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        $('.year-error').remove();
                    }
                }
            });

            // 初始验证
            validateTimeRange();
        }

        // 验证时间范围
        function validateTimeRange() {
            const beginTime = $("#beginTime").val();
            const endTime = $("#endTime").val();

            // 清除之前的错误状态
            $("#beginTime, #endTime").removeClass('is-invalid');
            $('.time-error').remove();

            if (beginTime && endTime) {
                const beginDate = new Date(beginTime);
                const endDate = new Date(endTime);

                if (beginDate >= endDate) {
                    // 显示错误状态
                    $("#beginTime, #endTime").addClass('is-invalid');

                    // 添加错误提示
                    if ($('.time-error').length === 0) {
                        $("#endTime").after('<div class="time-error text-danger small mt-1">开始时间不能大于或等于结束时间</div>');
                    }

                    // 禁用查询按钮
                    $('button[type="submit"]').prop('disabled', true);
                    return false;
                } else {
                    // 启用查询按钮
                    $('button[type="submit"]').prop('disabled', false);
                    return true;
                }
            } else {
                // 如果有一个时间为空，启用查询按钮
                $('button[type="submit"]').prop('disabled', false);
                return true;
            }
        }

        // 表单提交前验证
        $(document).on('submit', 'form', function(e) {
            if (!validateTimeRange()) {
                e.preventDefault();
                alert('请检查时间范围：开始时间不能大于或等于结束时间');
                return false;
            }
        });
    </script>
</body>
</html>