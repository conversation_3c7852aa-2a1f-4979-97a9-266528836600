<div th:fragment="spinner" id="loading-spinner" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:9999;">
    <div style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); text-align:center; color:white;">
        <div class="spinner-border text-light" role="status"></div>
        <p class="mt-2" id="loading-message">正在处理...</p>
    </div>
</div>

<script th:fragment="spinner-js">
function showLoading(message) {
    document.getElementById('loading-message').textContent = message || '正在处理...';
    document.getElementById('loading-spinner').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loading-spinner').style.display = 'none';
}
</script> 