<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务报警</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <style>
        .table-container {
            overflow-x: auto;
        }

        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }

        /* 时间验证错误样式 */
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
        }

        .time-error, .year-error {
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 0.25rem;
        }

        /* 时间控件样式 */
        input[type="datetime-local"] {
            cursor: pointer;
        }

        input[type="datetime-local"]:focus {
            outline: none;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* 时间控件样式优化 */
        input[type="datetime-local"] {
            min-width: 200px;
            font-family: monospace;
        }

    </style>
</head>
<body>
    <div th:replace="fragments/navigation :: navbar('alarms')"></div>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-exclamation-triangle"></i> 服务报警</h2>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bi bi-funnel"></i> 筛选
                </button>
            </div>
        </div>

        <div class="collapse show filter-form" id="filterCollapse">
            <form th:action="@{/alarms/search}" method="get">
                <div class="row g-3">
                    <div class="col-md-2">
                        <label for="startTime" class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="startTime" name="startTime" th:value="${startTime}" step="1" min="2000-01-01T00:00:00" max="9999-12-31T23:59:59">
                    </div>
                    <div class="col-md-2">
                        <label for="endTime" class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="endTime" name="endTime" th:value="${endTime}" step="1" min="2000-01-01T00:00:00" max="9999-12-31T23:59:59">
                    </div>
                    <div class="col-md-3">
                        <label for="appname" class="form-label">服务</label>
                        <select class="form-select" id="appname" name="appname">
                            <option value="">所有服务</option>
                            <option th:each="service : ${services}"
                                    th:value="${service.id}"
                                    th:text="${service.name}"
                                    th:selected="${service.id == selectedAppName}"></option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="clientIp" class="form-label">客户端IP</label>
                        <input type="text" class="form-control" id="clientIp" name="clientIp" th:value="${selectedClientIp}" autocomplete="off">
                    </div>
                    <div class="col-md-2">
                        <label for="clientPort" class="form-label">端口号</label>
                        <input type="number" class="form-control" id="clientPort" name="clientPort" th:value="${selectedClientPort}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <a href="/alarms" class="btn btn-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>服务名称</th>
                        <th>报警时间</th>
                        <th>报警信息</th>
                        <th>客户端IP</th>
                        <th>端口号</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${#lists.isEmpty(alarms) && (param.appname != null || param.clientIp != null || param.clientPort != null || param.startTime != null || param.endTime != null)}">
                        <td colspan="6" class="text-center">没有报警记录</td>
                    </tr>
                    <tr th:each="alarm : ${alarms}">
                        <td th:text="${alarm.serviceName}"></td>
                        <td th:text="${#temporals.format(alarm.alarmTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                        <td th:text="${alarm.alarmInfo}"></td>
                        <td th:text="${alarm.clientIp != null ? alarm.clientIp : '-'}"></td>
                        <td th:text="${alarm.clientPort != null ? alarm.clientPort : '-'}"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页控制 -->
        <div class="pagination-container" th:if="${totalPages > 0}">
            <div>
                显示 <span th:text="${currentPage * pageSize + 1}"></span> -
                <span th:text="${(currentPage * pageSize) + #lists.size(alarms)}"></span>
                共 <span th:text="${totalItems}"></span> 条记录
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=0, size=${pageSize})}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${currentPage - 1}, size=${pageSize})}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:each="i: ${#numbers.sequence(0, totalPages - 1)}"
                        th:if="${i >= currentPage - 2 and i <= currentPage + 2}"
                        th:classappend="${i == currentPage ? 'active' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${i}, size=${pageSize})}" th:text="${i + 1}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${currentPage + 1}, size=${pageSize})}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                        <a class="page-link" th:href="@{/alarms/search(appname=${selectedAppName}, clientIp=${selectedClientIp}, clientPort=${selectedClientPort}, startTime=${startTime}, endTime=${endTime}, page=${totalPages - 1}, size=${pageSize})}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script>
        $(document).ready(function() {
            // 初始化时间控件
            initializeDateTimeControls();

            // 初始化筛选表单的折叠状态
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('appname') || urlParams.has('clientIp') ||
                urlParams.has('clientPort') || urlParams.has('startTime') ||
                urlParams.has('endTime')) {
                $('#filterCollapse').addClass('show');
            }

        });

        // 初始化时间控件函数
        function initializeDateTimeControls() {
            // 初始化时间控件，设置默认值格式
            $("#startTime, #endTime").each(function() {
                const val = $(this).val();
                if (val && val.length === 16) {
                    // 如果只有分钟精度，添加秒
                    $(this).val(val + ':00');
                }
            });

            // 添加时间验证
            setupTimeValidation();
        }

        // 设置时间验证
        function setupTimeValidation() {
            // 移除只读属性，改用键盘事件阻止来禁止手动输入
            $("#startTime, #endTime").removeAttr('readonly');

            // 禁用键盘输入事件，但保留控件交互
            $("#startTime, #endTime").on('keydown keypress keyup', function(e) {
                // 允许Tab键和方向键用于导航

                e.preventDefault();
                return false;
            });

            // 格式化日期和时间输入，支持精确到秒
            $("#startTime, #endTime").on("change", function() {
                const val = $(this).val();
                if (val) {
                    // 确保日期时间格式符合ISO标准，精确到秒
                    try {
                        const date = new Date(val);
                        // 格式化为 YYYY-MM-DDTHH:MM:SS
                        const isoString = date.toISOString().slice(0, 19);
                        $(this).val(isoString);
                    } catch (e) {
                        console.error("Invalid date format", e);
                    }
                }
                validateTimeRange();
            });

            // 添加额外的输入保护
            $("#startTime, #endTime").on('paste', function(e) {
                e.preventDefault();
                return false;
            });

            // 添加输入验证，防止无效年份
            $("#startTime, #endTime").on('input', function() {
                const val = $(this).val();
                if (val) {
                    // 检查年份是否在合理范围内
                    const year = parseInt(val.substring(0, 4));
                    if (year < 2000 || year > 9999) {
                        $(this).addClass('is-invalid');
                        if ($('.year-error').length === 0) {
                            $(this).after('<div class="year-error text-danger small mt-1">年份必须在2000-9999之间</div>');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        $('.year-error').remove();
                    }
                }
            });

            // 初始验证
            validateTimeRange();
        }

        // 验证时间范围
        function validateTimeRange() {
            const startTime = $("#startTime").val();
            const endTime = $("#endTime").val();

            // 清除之前的错误状态
            $("#startTime, #endTime").removeClass('is-invalid');
            $('.time-error').remove();

            if (startTime && endTime) {
                const startDate = new Date(startTime);
                const endDate = new Date(endTime);

                if (startDate >= endDate) {
                    // 显示错误状态
                    $("#startTime, #endTime").addClass('is-invalid');

                    // 添加错误提示
                    if ($('.time-error').length === 0) {
                        $("#endTime").after('<div class="time-error text-danger small mt-1">开始时间不能大于或等于结束时间</div>');
                    }

                    // 禁用查询按钮
                    $('button[type="submit"]').prop('disabled', true);
                    return false;
                } else {
                    // 启用查询按钮
                    $('button[type="submit"]').prop('disabled', false);
                    return true;
                }
            } else {
                // 如果有一个时间为空，启用查询按钮
                $('button[type="submit"]').prop('disabled', false);
                return true;
            }
        }

        // 表单提交前验证
        $(document).on('submit', 'form', function(e) {
            if (!validateTimeRange()) {
                e.preventDefault();
                alert('请检查时间范围：开始时间不能大于或等于结束时间');
                return false;
            }
        });
    </script>
</body>
</html>