<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统重启 - 安全隔离单向输出模块</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    
    <style>
        body {

            min-height: 100vh;
        }
        
        .reboot-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .reboot-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            text-align: center;
        }
        
        .reboot-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .btn-reboot {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 8px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-reboot:hover {
            background: linear-gradient(45deg, #c82333, #a71e2a);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 8px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }
        
        .warning-text {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1.5rem 0;
        }
        
        .status-container {
            display: none;
            margin-top: 2rem;
        }
        
        .spinner-border-lg {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('maintenance')"></div>
    
    <div class="container-fluid">
        <div class="reboot-container">
            <div class="reboot-card">
                <div id="rebootForm">
                    <i class="bi bi-arrow-clockwise reboot-icon"></i>
                    <h2 class="mb-3">系统重启</h2>
                    <p class="text-muted mb-4">重启系统将会中断所有正在运行的服务和连接</p>
                    
                    <div class="warning-text">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>
                        <ul class="mb-0 mt-2 text-start">
                            <li>系统重启时，所有正在运行的服务将被停止</li>
                            <li>当前的网络连接将被中断</li>
                            <li>请确保已保存所有重要的配置和数据</li>
                            <li>重启过程大约需要1-2分钟</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <button type="button" class="btn btn-reboot" onclick="confirmReboot()">
                            <i class="bi bi-arrow-clockwise me-2"></i>确认重启
                        </button>
                    </div>
                </div>
                
                <!-- 重启状态显示 -->
                <div id="rebootStatus" class="status-container">
                    <div id="rebootProgress">
                        <div class="spinner-border spinner-border-lg text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h4 id="statusTitle">正在重启系统...</h4>
                        <p id="statusMessage" class="text-muted">请稍候，系统将在几秒钟后重启</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <script th:src="@{/js/jquery.min.js}"></script>
    
    <script>
        // 返回上一页
        function goBack() {
            window.history.back();
        }
        
        // 确认重启
        function confirmReboot() {
            if (confirm('您确定要重启系统吗？\n\n重启后系统将暂时不可用，请确保已保存所有工作。')) {
                executeReboot();
            }
        }
        
        // 执行重启
        function executeReboot() {
            // 隐藏表单，显示状态
            document.getElementById('rebootForm').style.display = 'none';
            document.getElementById('rebootStatus').style.display = 'block';
            
            // 获取CSRF令牌
            const token = $("meta[name='_csrf']").attr("content");
            const header = $("meta[name='_csrf_header']").attr("content");
            
            // 发送重启请求
            $.ajax({
                url: '/system/maintenance/reboot',
                type: 'POST',
                beforeSend: function(xhr) {
                    if (header && token) {
                        xhr.setRequestHeader(header, token);
                    }
                },
                success: function(data) {
                    // 重启成功
                    updateStatus('success', '重启命令已执行', '系统将在几秒钟后重启，请等待系统重新启动');

                },
                error: function(xhr, status, error) {
                    console.error('重启请求失败:', error);
                    updateStatus('error', '请求失败', '无法连接到服务器，请检查网络连接');
                    showRetryButton();
                }
            });
        }
        
        // 更新状态显示
        function updateStatus(type, title, message) {
            const progressDiv = document.getElementById('rebootProgress');
            const statusTitle = document.getElementById('statusTitle');
            const statusMessage = document.getElementById('statusMessage');
            
            if (type === 'success') {
                progressDiv.innerHTML = `
                    <div class="text-success mb-3">
                        <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                    </div>
                    <h4 class="text-success">${title}</h4>
                    <p class="text-muted">${message}</p>
                `;
            } else if (type === 'error') {
                progressDiv.innerHTML = `
                    <div class="text-danger mb-3">
                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                    </div>
                    <h4 class="text-danger">${title}</h4>
                    <p class="text-muted">${message}</p>
                `;
            }
        }
        
        // 显示重试按钮
        function showRetryButton() {
            const progressDiv = document.getElementById('rebootProgress');
            progressDiv.innerHTML += `
                <div class="mt-3">
                    <button type="button" class="btn btn-secondary me-2" onclick="goBack()">返回</button>
                    <button type="button" class="btn btn-reboot" onclick="executeReboot()">重试</button>
                </div>
            `;
        }
    </script>
</body>
</html>
