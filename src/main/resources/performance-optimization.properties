# ????????
# Performance Optimization Configuration

# ????
cache.cpu.duration=30000
cache.route.duration=10000
cache.service.duration=15000

# ??????
schedule.monitoring.interval=600000
schedule.license.interval=300000
schedule.alarm.interval=300000

# ????????
command.timeout.seconds=10
command.retry.count=2
command.cache.enabled=true

# ??????
logging.level.monitoring=DEBUG
logging.level.system=INFO
logging.level.route=DEBUG

# ?????
thread.pool.core.size=2
thread.pool.max.size=4
thread.pool.queue.capacity=100

# ??????
memory.gc.interval=300000
memory.cache.max.size=1000

# ????????
db.connection.pool.min=2
db.connection.pool.max=10
db.connection.pool.timeout=30000

# XML????
xml.parser.cache.enabled=true
xml.parser.validation.enabled=false
xml.parser.namespace.aware=false
