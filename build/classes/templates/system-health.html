<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>系统健康检查 - 安全隔离单向输出模块</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet">
    <link th:href="@{/css/bootstrap-icons.css}" rel="stylesheet">
</head>
<body>
    <!-- 使用通用导航栏片段 -->
    <div th:replace="fragments/navigation :: navbar('system')"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-activity"></i> 系统健康检查</h2>
            <form action="/system/repair" method="post">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-tools"></i> 修复系统
                </button>
            </form>
        </div>
        
        <!-- 消息容器 -->
        <div id="message-container">
            <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i> <span th:text="${success}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i> <span th:text="${error}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
        
        <!-- 系统健康状态概览 -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-heart-pulse"></i> 系统健康状态
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div th:class="${healthResult.overallHealth == '正常' ? 'card-body bg-success text-white' : 'card-body bg-warning'}">
                                <h5 class="card-title">总体状态</h5>
                                <p class="card-text display-4 text-center" th:text="${healthResult.overallHealth}">正常</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div th:class="${healthResult.configDirHealth == '正常' ? 'card-body bg-success text-white' : 'card-body bg-danger text-white'}">
                                <h5 class="card-title">配置目录</h5>
                                <p class="card-text display-4 text-center" th:text="${healthResult.configDirHealth}">正常</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div th:class="${healthResult.configFilesHealth == '正常' ? 'card-body bg-success text-white' : 'card-body bg-danger text-white'}">
                                <h5 class="card-title">配置文件</h5>
                                <p class="card-text display-4 text-center" th:text="${healthResult.configFilesHealth}">正常</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div th:class="${healthResult.diskSpaceHealth == '正常' ? 'card-body bg-success text-white' : 'card-body bg-warning'}">
                                <h5 class="card-title">磁盘空间</h5>
                                <p class="card-text display-4 text-center" th:text="${healthResult.diskSpaceHealth}">正常</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- JVM状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-cpu"></i> JVM状态
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>内存使用情况</h5>
                        <div class="progress mb-3" style="height: 30px;">
                            <div th:class="${healthResult.jvm.heapUsagePercent < 70 ? 'progress-bar bg-success' : (healthResult.jvm.heapUsagePercent < 85 ? 'progress-bar bg-warning' : 'progress-bar bg-danger')}"
                                 role="progressbar" 
                                 th:style="'width: ' + ${healthResult.jvm.heapUsagePercent} + '%'" 
                                 th:aria-valuenow="${healthResult.jvm.heapUsagePercent}"
                                 aria-valuemin="0" 
                                 aria-valuemax="100"
                                 th:text="${#numbers.formatDecimal(healthResult.jvm.heapUsagePercent, 1, 2) + '%'}">
                                25%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tr>
                                <th>最大内存</th>
                                <td th:text="${#numbers.formatDecimal(healthResult.jvm.maxMemory / 1048576, 0, 2) + ' MB'}">1024 MB</td>
                            </tr>
                            <tr>
                                <th>已分配内存</th>
                                <td th:text="${#numbers.formatDecimal(healthResult.jvm.allocatedMemory / 1048576, 0, 2) + ' MB'}">512 MB</td>
                            </tr>
                            <tr>
                                <th>已使用内存</th>
                                <td th:text="${#numbers.formatDecimal(healthResult.jvm.usedMemory / 1048576, 0, 2) + ' MB'}">256 MB</td>
                            </tr>
                            <tr>
                                <th>空闲内存</th>
                                <td th:text="${#numbers.formatDecimal(healthResult.jvm.freeMemory / 1048576, 0, 2) + ' MB'}">256 MB</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 磁盘状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-hdd"></i> 磁盘状态
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>磁盘使用情况</h5>
                        <div class="progress mb-3" style="height: 30px;">
                            <div th:class="${healthResult.diskSpace.usedPercent < 70 ? 'progress-bar bg-success' : (healthResult.diskSpace.usedPercent < 85 ? 'progress-bar bg-warning' : 'progress-bar bg-danger')}"
                                 role="progressbar" 
                                 th:style="'width: ' + ${healthResult.diskSpace.usedPercent} + '%'" 
                                 th:aria-valuenow="${healthResult.diskSpace.usedPercent}"
                                 aria-valuemin="0" 
                                 aria-valuemax="100"
                                 th:text="${#numbers.formatDecimal(healthResult.diskSpace.usedPercent, 1, 2) + '%'}">
                                25%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tr>
                                <th>总磁盘空间</th>
                                <td th:text="${#numbers.formatDecimal(healthResult.diskSpace.totalSpace / **********, 0, 2) + ' GB'}">100 GB</td>
                            </tr>
                            <tr>
                                <th>可用磁盘空间</th>
                                <td th:text="${#numbers.formatDecimal(healthResult.diskSpace.freeSpace / **********, 0, 2) + ' GB'}">75 GB</td>
                            </tr>
                            <tr>
                                <th>可用百分比</th>
                                <td th:text="${#numbers.formatDecimal(100 - healthResult.diskSpace.usedPercent, 1, 2) + '%'}">75%</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script th:src="@{/js/bootstrap.bundle.min.js}"></script>
</body>
</html>