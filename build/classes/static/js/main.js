// 主JavaScript文件

// 页面加载完成后执行
$(document).ready(function() {
    // 初始化页面功能
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 激活当前导航项
    activateCurrentNavItem();
    
    // 禁用所有表单的自动填充功能
    disableAutoComplete();
}

// 激活当前导航项
function activateCurrentNavItem() {
    const path = window.location.pathname;
    $('.navbar-nav .nav-link').each(function() {
        const href = $(this).attr('href');
        if (path.includes(href) && href !== '/') {
            $(this).addClass('active');
        } else if (path === '/' && href === '/') {
            $(this).addClass('active');
        }
    });
}

// 禁用自动填充
function disableAutoComplete() {
    // 为所有表单添加autocomplete="off"
    $('form').attr('autocomplete', 'off');
    
    // 为所有输入框添加autocomplete="off"
    $('input, textarea').attr('autocomplete', 'off');
    
    // 为页面上的所有密码字段添加特殊处理
    $('input[type="password"]').on('focus', function() {
        const $this = $(this);
        // 暂时将类型改为text再改回password，防止浏览器自动填充
        $this.attr('type', 'text');
        setTimeout(function() {
            $this.attr('type', 'password');
        }, 10);
    });
}

// 初始化提示框
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 资源管理页面初始化
function initResourcesPage() {
    console.log('初始化资源管理页面');
    
    // 加载资源数据
    loadResourceData();
    
    // 初始化表单事件
    initResourceFormEvents();
    
    // 初始化搜索功能
    initSearchForm();
    
    // 初始化全选功能
    initSelectAll();
}

// 加载资源数据
function loadResourceData() {
    // 模拟从后端获取数据
    // 实际项目中应该使用AJAX请求从后端获取数据
    const mockData = [
        {
            id: 1,
            name: '资源1',
            segmentType: 'all',
            ipRanges: [],
            createdAt: '2023-05-10 09:30',
            updatedAt: '2023-05-10 09:30'
        },
        {
            id: 2,
            name: '资源2',
            segmentType: 'specific',
            ipRanges: [
                { startIp: '***********', endIp: '*************' },
                { startIp: '********', endIp: '********00' }
            ],
            createdAt: '2023-05-11 14:20',
            updatedAt: '2023-05-12 10:15'
        },
        {
            id: 3,
            name: '资源3',
            segmentType: 'specific',
            ipRanges: [
                { startIp: '**********', endIp: '***********' }
            ],
            createdAt: '2023-05-13 16:45',
            updatedAt: '2023-05-13 16:45'
        }
    ];
    
    renderResourceTable(mockData);
    renderPagination(1, Math.ceil(mockData.length / 10));
}

// 渲染资源表格
function renderResourceTable(data) {
    const tableBody = $('#resourceTableBody');
    tableBody.empty();
    
    if (data.length === 0) {
        tableBody.append('<tr><td colspan="7" class="text-center">暂无数据</td></tr>');
        return;
    }
    
    data.forEach(resource => {
        let ipRangeText = resource.segmentType === 'all' ? '全网段' : 
            resource.ipRanges.map(range => `${range.startIp} - ${range.endIp}`).join('<br>');
        
        const row = `
            <tr data-id="${resource.id}">
                <td><input type="checkbox" class="resource-checkbox"></td>
                <td>${resource.name}</td>
                <td>${resource.segmentType === 'all' ? '全网段' : '指定网段'}</td>
                <td>${ipRangeText}</td>
                <td>${resource.createdAt}</td>
                <td>${resource.updatedAt}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary edit-resource" data-bs-toggle="modal" data-bs-target="#addResourceModal">
                        编辑
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-resource">
                        删除
                    </button>
                </td>
            </tr>
        `;
        
        tableBody.append(row);
    });
    
    // 绑定编辑和删除按钮事件
    $('.edit-resource').on('click', function() {
        const resourceId = $(this).closest('tr').data('id');
        editResource(resourceId);
    });
    
    $('.delete-resource').on('click', function() {
        const resourceId = $(this).closest('tr').data('id');
        deleteResource(resourceId);
    });
}

// 渲染分页
function renderPagination(currentPage, totalPages) {
    const pagination = $('#pagination');
    pagination.empty();
    
    if (totalPages <= 1) {
        return;
    }
    
    // 上一页
    pagination.append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
        </li>
    `);
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        pagination.append(`
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `);
    }
    
    // 下一页
    pagination.append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
        </li>
    `);
    
    // 绑定页码点击事件
    $('.page-link').on('click', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page >= 1 && page <= totalPages) {
            // 加载对应页的数据
            loadResourceData(page);
        }
    });
}

// 初始化资源表单事件
function initResourceFormEvents() {
    // 网段类型切换事件
    $('input[name="segmentType"]').on('change', function() {
        const segmentType = $(this).val();
        if (segmentType === 'specific') {
            $('#ipRangesContainer').removeClass('d-none');
        } else {
            $('#ipRangesContainer').addClass('d-none');
        }
    });
    
    // 添加IP范围按钮事件
    $('#addIpRange').on('click', function() {
        const ipRangeRow = `
            <div class="row mb-2 ip-range-row">
                <div class="col-5">
                    <input type="text" class="form-control start-ip" placeholder="起始IP">
                </div>
                <div class="col-1 text-center">
                    <span class="align-middle">至</span>
                </div>
                <div class="col-5">
                    <input type="text" class="form-control end-ip" placeholder="结束IP">
                </div>
                <div class="col-1">
                    <button type="button" class="btn btn-danger btn-sm remove-range">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        $('#ipRanges').append(ipRangeRow);
        
        // 绑定删除IP范围按钮事件
        $('.remove-range').on('click', function() {
            $(this).closest('.ip-range-row').remove();
        });
    });
    
    // 保存资源按钮事件
    $('#saveResource').on('click', function() {
        saveResource();
    });
}

// 保存资源
function saveResource() {
    // 表单验证
    if (!validateResourceForm()) {
        return;
    }
    
    // 收集表单数据
    const resourceData = {
        name: $('#name').val(),
        segmentType: $('input[name="segmentType"]:checked').val(),
        ipRanges: [],
        description: $('#description').val()
    };
    
    // 如果是指定网段，收集IP范围
    if (resourceData.segmentType === 'specific') {
        $('.ip-range-row').each(function() {
            const startIp = $(this).find('.start-ip').val();
            const endIp = $(this).find('.end-ip').val();
            
            if (startIp && endIp) {
                resourceData.ipRanges.push({
                    startIp: startIp,
                    endIp: endIp
                });
            }
        });
    }
    
    // 模拟保存到后端
    console.log('保存资源数据:', resourceData);
    
    // 显示成功提示
    alert('资源保存成功！');
    
    // 关闭模态框
    $('#addResourceModal').modal('hide');
    
    // 重新加载资源数据
    loadResourceData();
}

// 验证资源表单
function validateResourceForm() {
    const name = $('#name').val();
    if (!name) {
        alert('请输入资源名称');
        return false;
    }
    
    const segmentType = $('input[name="segmentType"]:checked').val();
    if (segmentType === 'specific') {
        const ipRangeRows = $('.ip-range-row');
        if (ipRangeRows.length === 0) {
            alert('请添加至少一个IP范围');
            return false;
        }
        
        let valid = true;
        ipRangeRows.each(function() {
            const startIp = $(this).find('.start-ip').val();
            const endIp = $(this).find('.end-ip').val();
            
            if (!startIp || !endIp) {
                alert('请填写完整的IP范围');
                valid = false;
                return false;
            }
            
            if (!isValidIp(startIp) || !isValidIp(endIp)) {
                alert('请输入有效的IP地址');
                valid = false;
                return false;
            }
        });
        
        if (!valid) {
            return false;
        }
    }
    
    return true;
}

// 验证IP地址
function isValidIp(ip) {
    const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    if (!ipRegex.test(ip)) {
        return false;
    }
    
    const parts = ip.split('.');
    for (let i = 0; i < parts.length; i++) {
        const part = parseInt(parts[i]);
        if (part < 0 || part > 255) {
            return false;
        }
    }
    
    return true;
}

// 编辑资源
function editResource(resourceId) {
    // 模拟从后端获取资源数据
    const mockResource = {
        id: resourceId,
        name: '资源' + resourceId,
        segmentType: resourceId === 1 ? 'all' : 'specific',
        ipRanges: resourceId === 1 ? [] : [
            { startIp: '***********', endIp: '*************' }
        ],
        description: '这是资源' + resourceId + '的描述'
    };
    
    // 填充表单
    $('#name').val(mockResource.name);
    $(`input[name="segmentType"][value="${mockResource.segmentType}"]`).prop('checked', true).trigger('change');
    $('#description').val(mockResource.description);
    
    // 如果是指定网段，填充IP范围
    if (mockResource.segmentType === 'specific') {
        $('#ipRanges').empty();
        
        mockResource.ipRanges.forEach(range => {
            const ipRangeRow = `
                <div class="row mb-2 ip-range-row">
                    <div class="col-5">
                        <input type="text" class="form-control start-ip" placeholder="起始IP" value="${range.startIp}">
                    </div>
                    <div class="col-1 text-center">
                        <span class="align-middle">至</span>
                    </div>
                    <div class="col-5">
                        <input type="text" class="form-control end-ip" placeholder="结束IP" value="${range.endIp}">
                    </div>
                    <div class="col-1">
                        <button type="button" class="btn btn-danger btn-sm remove-range">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            $('#ipRanges').append(ipRangeRow);
        });
        
        // 绑定删除IP范围按钮事件
        $('.remove-range').on('click', function() {
            $(this).closest('.ip-range-row').remove();
        });
    }
    
    // 修改模态框标题
    $('#addResourceModalLabel').text('编辑UDP资源');
    
    // 保存时传入资源ID
    $('#saveResource').data('id', resourceId);
}

// 删除资源
function deleteResource(resourceId) {
    if (confirm('确定要删除该资源吗？')) {
        // 模拟从后端删除资源
        console.log('删除资源:', resourceId);
        
        // 重新加载资源数据
        loadResourceData();
    }
}

// 初始化搜索表单
function initSearchForm() {
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        
        const resourceName = $('#resourceName').val();
        const ipRange = $('#ipRange').val();
        
        // 模拟搜索
        console.log('搜索条件:', { resourceName, ipRange });
        
        // 重新加载资源数据
        loadResourceData();
    });
}

// 初始化全选功能
function initSelectAll() {
    $('#selectAll').on('change', function() {
        const checked = $(this).prop('checked');
        $('.resource-checkbox').prop('checked', checked);
    });
}

// 服务配置页面初始化
function initServicesPage() {
    console.log('初始化服务配置页面');
    
    // 加载服务数据
    loadServiceData();
    
    // 初始化服务状态控制
    initServiceControls();
    
    // 配置向导按钮事件
    $('#startConfigWizard').on('click', function() {
        window.location.href = '/services/wizard';
    });
}

// 加载服务数据
function loadServiceData() {
    // 模拟从后端获取数据
    const mockData = [
        {
            id: 1,
            name: '服务1',
            resourceName: '资源1',
            transportIp: '***********00',
            portRange: '8000-8100',
            status: 'running'
        },
        {
            id: 2,
            name: '服务2',
            resourceName: '资源2',
            transportIp: '***********01',
            portRange: '9000-9100',
            status: 'stopped'
        },
        {
            id: 3,
            name: '服务3',
            resourceName: '资源3',
            transportIp: '***********02',
            portRange: '10000-10100',
            status: 'warning'
        }
    ];
    
    renderServiceTable(mockData);
}

// 渲染服务表格
function renderServiceTable(data) {
    const tableBody = $('#serviceTableBody');
    tableBody.empty();
    
    if (data.length === 0) {
        tableBody.append('<tr><td colspan="6" class="text-center">暂无数据</td></tr>');
        return;
    }
    
    data.forEach(service => {
        const statusClass = service.status === 'running' ? 'success' : 
                           (service.status === 'stopped' ? 'danger' : 'warning');
        const statusText = service.status === 'running' ? '运行中' : 
                          (service.status === 'stopped' ? '已停止' : '警告');
        
        const row = `
            <tr data-id="${service.id}">
                <td>${service.name}</td>
                <td>${service.resourceName}</td>
                <td>${service.transportIp}</td>
                <td>${service.portRange}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary edit-service">
                        编辑
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-service">
                        删除
                    </button>
                </td>
            </tr>
        `;
        
        tableBody.append(row);
    });
    
    // 绑定编辑和删除按钮事件
    $('.edit-service').on('click', function() {
        const serviceId = $(this).closest('tr').data('id');
        window.location.href = `/services/wizard?id=${serviceId}`;
    });
    
    $('.delete-service').on('click', function() {
        const serviceId = $(this).closest('tr').data('id');
        deleteService(serviceId);
    });
}

// 删除服务
function deleteService(serviceId) {
    if (confirm('确定要删除该服务吗？')) {
        // 模拟从后端删除服务
        console.log('删除服务:', serviceId);
        
        // 重新加载服务数据
        loadServiceData();
    }
}

// 初始化服务状态控制
function initServiceControls() {
    $('#startService').on('click', function() {
        changeServiceStatus('running');
    });
    
    $('#stopService').on('click', function() {
        changeServiceStatus('stopped');
    });
    
    $('#restartService').on('click', function() {
        changeServiceStatus('restarting');
        setTimeout(() => {
            changeServiceStatus('running');
        }, 2000);
    });
}

// 改变服务状态
function changeServiceStatus(status) {
    // 模拟向后端发送状态变更请求
    console.log('改变服务状态:', status);
    
    // 更新UI
    if (status === 'running') {
        $('#serviceStatus').removeClass('stopped warning').addClass('running');
        $('#statusText').text('运行中');
    } else if (status === 'stopped') {
        $('#serviceStatus').removeClass('running warning').addClass('stopped');
        $('#statusText').text('已停止');
    } else if (status === 'restarting') {
        $('#serviceStatus').removeClass('running stopped').addClass('warning');
        $('#statusText').text('重启中...');
    }
}

// 配置向导页面初始化
function initWizardPage() {
    console.log('初始化配置向导页面');
    
    // 加载资源选项
    loadResourceOptions();
    
    // 初始化向导步骤控制
    initWizardSteps();
    
    // 初始化表单事件
    initWizardFormEvents();
    
    // 取消按钮事件
    $('#cancelWizard').on('click', function() {
        if (confirm('确定要取消配置吗？已填写的内容将丢失。')) {
            window.location.href = '/services';
        }
    });
}

// 加载资源选项
function loadResourceOptions() {
    // 模拟从后端获取资源数据
    const mockResources = [
        { id: 1, name: '资源1' },
        { id: 2, name: '资源2' },
        { id: 3, name: '资源3' }
    ];
    
    const resourceSelect = $('#resourceSelect');
    resourceSelect.find('option:not(:first)').remove();
    
    mockResources.forEach(resource => {
        resourceSelect.append(`<option value="${resource.id}">${resource.name}</option>`);
    });
}

// 初始化向导步骤控制
function initWizardSteps() {
    // 步骤1到步骤2
    $('#nextToStep2').on('click', function() {
        if (validateStep1()) {
            $('.nav-pills .nav-link[href="#step2"]').tab('show');
            $('.progress-bar').css('width', '66%').attr('aria-valuenow', 66).text('步骤 2/3');
        }
    });
    
    // 步骤2到步骤1
    $('#backToStep1').on('click', function() {
        $('.nav-pills .nav-link[href="#step1"]').tab('show');
        $('.progress-bar').css('width', '33%').attr('aria-valuenow', 33).text('步骤 1/3');
    });
    
    // 步骤2到步骤3
    $('#nextToStep3').on('click', function() {
        if (validateStep2()) {
            // 更新摘要信息
            updateSummary();
            
            $('.nav-pills .nav-link[href="#step3"]').tab('show');
            $('.progress-bar').css('width', '100%').attr('aria-valuenow', 100).text('步骤 3/3');
        }
    });
    
    // 步骤3到步骤2
    $('#backToStep2').on('click', function() {
        $('.nav-pills .nav-link[href="#step2"]').tab('show');
        $('.progress-bar').css('width', '66%').attr('aria-valuenow', 66).text('步骤 2/3');
    });
    
    // 确认部署复选框
    $('#confirmDeploy').on('change', function() {
        $('#deployService').prop('disabled', !$(this).prop('checked'));
    });
    
    // 部署服务按钮
    $('#deployService').on('click', function() {
        deployService();
    });
}

// 验证步骤1
function validateStep1() {
    const serviceName = $('#serviceName').val();
    if (!serviceName) {
        alert('请输入服务名称');
        return false;
    }
    
    const resourceId = $('#resourceSelect').val();
    if (!resourceId) {
        alert('请选择UDP资源');
        return false;
    }
    
    const transportIp = $('#transportIp').val();
    if (!transportIp || !isValidIp(transportIp)) {
        alert('请输入有效的传输IP');
        return false;
    }
    
    const startPort = $('#startPort').val();
    const endPort = $('#endPort').val();
    if (!startPort || !endPort) {
        alert('请输入端口范围');
        return false;
    }
    
    const startPortNum = parseInt(startPort);
    const endPortNum = parseInt(endPort);
    if (startPortNum < 1 || startPortNum > 65535 || endPortNum < 1 || endPortNum > 65535) {
        alert('端口范围必须在1-65535之间');
        return false;
    }
    
    if (startPortNum > endPortNum) {
        alert('起始端口不能大于结束端口');
        return false;
    }
    
    return true;
}

// 验证步骤2
function validateStep2() {
    const packetRateLimit = $('#packetRateLimit').val();
    if (!packetRateLimit || parseInt(packetRateLimit) < 1) {
        alert('请输入有效的数据包速率限制');
        return false;
    }
    
    const connectionLimit = $('#connectionLimit').val();
    if (!connectionLimit || parseInt(connectionLimit) < 1) {
        alert('请输入有效的连接数限制');
        return false;
    }
    
    return true;
}

// 更新摘要信息
function updateSummary() {
    // 基础配置摘要
    $('#summaryServiceName').text($('#serviceName').val());
    $('#summaryResource').text($('#resourceSelect option:selected').text());
    $('#summaryTransportIp').text($('#transportIp').val());
    $('#summaryPortRange').text(`${$('#startPort').val()}-${$('#endPort').val()}`);
    $('#summarySyncConfig').text($('#syncConfig option:selected').text());
    
    // 安全配置摘要
    $('#summaryFloodProtection').text($('#enableFloodProtection').prop('checked') ? '已启用' : '已禁用');
    $('#summaryPacketRateLimit').text($('#packetRateLimit').val() + ' 包/秒');
    $('#summaryConnectionLimit').text($('#connectionLimit').val() + ' 连接');
    $('#summarySecurityPolicy').text($('#securityPolicy option:selected').text());
}

// 初始化向导表单事件
function initWizardFormEvents() {
    // 安全策略切换事件
    $('#securityPolicy').on('change', function() {
        const policy = $(this).val();
        if (policy === 'custom') {
            $('#customPolicySettings').removeClass('d-none');
        } else {
            $('#customPolicySettings').addClass('d-none');
        }
    });
}

// 部署服务
function deployService() {
    // 收集表单数据
    const serviceData = {
        // 基础配置
        name: $('#serviceName').val(),
        resourceId: $('#resourceSelect').val(),
        transportIp: $('#transportIp').val(),
        portRange: {
            start: $('#startPort').val(),
            end: $('#endPort').val()
        },
        syncConfig: $('#syncConfig').val(),
        
        // 安全配置
        security: {
            floodProtection: $('#enableFloodProtection').prop('checked'),
            packetRateLimit: $('#packetRateLimit').val(),
            connectionLimit: $('#connectionLimit').val(),
            securityPolicy: $('#securityPolicy').val()
        }
    };
    
    // 模拟向后端发送部署请求
    console.log('部署服务:', serviceData);
    
    // 显示部署中提示
    $('#deployService').prop('disabled', true).text('部署中...');
    
    // 模拟部署过程
    setTimeout(() => {
        alert('服务部署成功！');
        window.location.href = '/services';
    }, 2000);
} 