// 设置页面JavaScript

$(document).ready(function() {
    // 初始化设置页面功能
    initSettingsPage();
});

// 初始化设置页面
function initSettingsPage() {
    // 通用设置表单提交
    $('#generalSettingsForm').on('submit', function(e) {
        e.preventDefault();
        saveGeneralSettings();
    });
    
    // 自动备份表单提交
    $('#autoBackupForm').on('submit', function(e) {
        e.preventDefault();
        saveAutoBackupSettings();
    });
    
    // 备份表单提交
    $('#backupForm').on('submit', function(e) {
        e.preventDefault();
        createBackup();
    });
    
    // 修改用户按钮事件
    $('.edit-user').on('click', function() {
        const username = $(this).closest('tr').find('td:first').text();
        editUser(username);
    });
    
    // 删除用户按钮事件
    $('.delete-user').on('click', function() {
        const username = $(this).closest('tr').find('td:first').text();
        if (username !== 'admin' && confirm('确定要删除用户 ' + username + ' 吗？')) {
            // 模拟删除用户
            console.log('删除用户:', username);
            $(this).closest('tr').remove();
        }
    });
    
    // 保存用户按钮事件
    $('#saveUser').on('click', function() {
        saveUser();
    });
    
    // 刷新日志按钮事件
    $('#refreshLogs').on('click', function() {
        // 模拟刷新日志
        console.log('刷新日志');
    });
    
    // 导出日志按钮事件
    $('#exportLogs').on('click', function() {
        // 模拟导出日志
        console.log('导出日志');
    });
    
    // 日志筛选表单提交
    $('#logFilterForm').on('submit', function(e) {
        e.preventDefault();
        filterLogs();
    });
    
    // 恢复备份按钮事件
    $('.restore-backup').on('click', function() {
        const backupName = $(this).closest('tr').find('td:first').text();
        if (confirm('确定要恢复备份 ' + backupName + ' 吗？当前数据将被覆盖。')) {
            // 模拟恢复备份
            console.log('恢复备份:', backupName);
        }
    });
    
    // 下载备份按钮事件
    $('.download-backup').on('click', function() {
        const backupName = $(this).closest('tr').find('td:first').text();
        // 模拟下载备份
        console.log('下载备份:', backupName);
    });
    
    // 删除备份按钮事件
    $('.delete-backup').on('click', function() {
        const backupName = $(this).closest('tr').find('td:first').text();
        if (confirm('确定要删除备份 ' + backupName + ' 吗？此操作不可恢复。')) {
            // 模拟删除备份
            console.log('删除备份:', backupName);
            $(this).closest('tr').remove();
        }
    });
    
    // 更新系统信息
    updateSystemInfo();
}

// 保存通用设置
function saveGeneralSettings() {
    // 收集表单数据
    const settings = {
        systemName: $('#systemName').val(),
        dataDir: $('#dataDir').val(),
        logLevel: $('#logLevel').val(),
        logRetention: $('#logRetention').val(),
        maxConnections: $('#maxConnections').val(),
        enableMonitoring: $('#enableMonitoring').prop('checked'),
        enableNotifications: $('#enableNotifications').prop('checked')
    };
    
    // 模拟保存设置
    console.log('保存通用设置:', settings);
    
    // 显示成功提示
    alert('设置已保存');
}

// 保存自动备份设置
function saveAutoBackupSettings() {
    // 收集表单数据
    const settings = {
        enableAutoBackup: $('#enableAutoBackup').prop('checked'),
        backupFrequency: $('#backupFrequency').val(),
        backupTime: $('#backupTime').val(),
        maxBackups: $('#maxBackups').val()
    };
    
    // 模拟保存设置
    console.log('保存自动备份设置:', settings);
    
    // 显示成功提示
    alert('自动备份设置已保存');
}

// 创建备份
function createBackup() {
    // 收集表单数据
    const backupData = {
        name: $('#backupName').val(),
        includeResources: $('#includeResources').prop('checked'),
        includeServices: $('#includeServices').prop('checked'),
        includeSystemSettings: $('#includeSystemSettings').prop('checked')
    };
    
    if (!backupData.name) {
        alert('请输入备份名称');
        return;
    }
    
    // 模拟创建备份
    console.log('创建备份:', backupData);
    
    // 显示成功提示
    alert('备份创建成功');
    
    // 清空表单
    $('#backupName').val('');
}

// 编辑用户
function editUser(username) {
    // 模拟获取用户数据
    const userData = {
        username: username,
        role: username === 'admin' ? 'admin' : (username === 'operator' ? 'operator' : 'guest'),
        active: username !== 'guest'
    };
    
    // 填充表单
    $('#username').val(userData.username);
    $('#password').val('');
    $('#confirmPassword').val('');
    $('#userRole').val(userData.role);
    $('#userActive').prop('checked', userData.active);
    
    // 修改模态框标题
    $('#addUserModalLabel').text('编辑用户');
    
    // 显示模态框
    $('#addUserModal').modal('show');
}

// 保存用户
function saveUser() {
    // 表单验证
    const username = $('#username').val();
    const password = $('#password').val();
    const confirmPassword = $('#confirmPassword').val();
    const role = $('#userRole').val();
    
    if (!username) {
        alert('请输入用户名');
        return;
    }
    
    if (!password) {
        alert('请输入密码');
        return;
    }
    
    if (password !== confirmPassword) {
        alert('两次输入的密码不一致');
        return;
    }
    
    if (!role) {
        alert('请选择角色');
        return;
    }
    
    // 收集表单数据
    const userData = {
        username: username,
        password: password,
        role: role,
        active: $('#userActive').prop('checked')
    };
    
    // 模拟保存用户
    console.log('保存用户:', userData);
    
    // 显示成功提示
    alert('用户保存成功');
    
    // 关闭模态框
    $('#addUserModal').modal('hide');
}

// 筛选日志
function filterLogs() {
    // 收集筛选条件
    const filters = {
        logType: $('#logTypeFilter').val(),
        logLevel: $('#logLevelFilter').val(),
        dateRange: $('#logDateRange').val()
    };
    
    // 模拟筛选日志
    console.log('筛选日志:', filters);
}

// 更新系统信息
function updateSystemInfo() {
    // 模拟更新系统信息
    setInterval(function() {
        const cpuUsage = Math.floor(Math.random() * 40) + 20;
        const memoryUsage = Math.floor(Math.random() * 30) + 30;
        const diskUsage = Math.floor(Math.random() * 20) + 20;
        
        $('#cpuUsage').text(cpuUsage + '%');
        $('#memoryUsage').text(memoryUsage + '%');
        $('#diskUsage').text(diskUsage + '%');
        
        // 更新运行时间
        const now = new Date();
        const startTime = new Date('2023-05-15 08:00:00');
        const diff = now - startTime;
        
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        
        $('#uptime').text(hours + '小时' + minutes + '分钟');
    }, 5000);
} 