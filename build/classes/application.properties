# åºæ¬éç½®
server.port=8081
server.ssl.enabled=false
server.ssl.key-store=/etc/unimas/tomcat/conf/server_keystore
server.ssl.key-store-password=12345678
server.ssl.key-store-type=PKCS12
server.ssl.protocol=TLS
server.ssl.enabled-protocols=TLSv1.2
server.ssl.ciphers=TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA
server.ssl.client-auth=none
#server.ssl.trust-store=/etc/unimas/tomcat/conf/server_keystore
#server.ssl.trust-store-password=12345678
#server.ssl.trust-store-type=PKCS12

# æ¨¡æ¿å¼æéç½®
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# å¯ç¨éæèµæº
spring.web.resources.add-mappings=true
# éæèµæºè·¯å¾éç½®
spring.web.resources.static-locations=classpath:/static/
# éæèµæºç¼å­æ§å¶
spring.web.resources.cache.period=0
spring.mvc.static-path-pattern=/**

# æ¥å¿éç½®
logging.level.root=INFO
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.udpproxy=DEBUG
logging.file.name=/etc/unimas/tomcat/udpproxy/logs/udpproxy.log
logging.file.max-size=10MB
logging.file.max-history=10

# èº«ä»½éªè¯æ¥å¿
logging.level.org.springframework.security=DEBUG

# ç¡®ä¿æ²¡æè®¾ç½®æè®¾ç½®ä¸ºç©º
server.servlet.context-path= 

# ææç®¡çéç½®
license.hardware-serial=UDPSystem-2023-001-XXXX
license.base-modules=åºç¡æ¨¡å
license.file.path=/etc/unimas/tomcat/license/system-license.dat

# åºç¨éç½®ç®å½
config.dir=/etc/unimas/tomcat/config

# æ¨¡æè¿ç¨éç½®ç®å½ - ç¨äºæä»¶åæ­¥
remote.config.dir=/etc/unimas/tomcat/remote_config

# å¯ç¨éç½®åæ­¥åè½
sync.enabled=true

# æ°æ®åºéç½®
spring.datasource.url=**********************************
spring.datasource.username=dbsync
spring.datasource.password=testkljdyxgunimas
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql=false

# å­ä½MIMEç±»åæ å°
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**
spring.web.resources.chain.cache=true

# å­ä½ç¼å­è®¾ç½®
spring.web.resources.cache.period=31536000