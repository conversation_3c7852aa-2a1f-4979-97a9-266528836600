#! /bin/sh

#num=`ifconfig -a |awk 'BEGIN{count=-3;RS=""}{if($1  ~ /^(eth)[0-9]+$/){count++;}}END{print(count)}'`
num=`expr $(grep -c "" /proc/net/dev) - 4`
#num=1
if [ $1 -eq 0 ];then
	ip addr |grep -v eth0 |grep inet |grep -v inet6 | grep -v 172.18.18 |grep -v host |awk -v count=$num  'BEGIN{FS="/";_t=1} {
		split($2,a,"eth");
		split(a[2],b,":"); 
	   # print a[2];
	   # print count; 
	   if(b[1] <= count){
		 #print $count;
		 split($1,ip," ");
			 if(_t!=1){
				 printf ",";
			 }
			 printf("{\"ethip\":\"%s\"}", ip[2]);
		 _t++;
		# print substr($2,length($2)-4,length($2))
	   }  
	}'
elif [ $1 -eq 1 ];then
	ip addr |grep eth0 |grep inet |grep -v inet6 | grep -v host |awk -v count=$num  'BEGIN{FS="/";_t=1} {
		split($2,a,"eth");
		split(a[2],b,":"); 
	   # print a[2];
	   # print count; 
	   if(b[1] <= count){
		 #print $count;
		 split($1,ip," ");
			 if(_t!=1){
				 printf ",";
			 }
			 printf("{\"ethip\":\"%s\"}", ip[2]);
		 _t++;
		# print substr($2,length($2)-4,length($2))
	   }  
	}'
fi

