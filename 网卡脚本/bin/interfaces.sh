#! /bin/sh
export LANG=C
NET_PATH=/etc/network/interfaces
NET_PATH_BAK=/etc/network/interfaces_bak
waNGKA=$1
IP_MOD=$2
MASK_MOD=$3
UP=$4
GW_MOD=$5

isVip=$(echo $waNGKA | grep ":")

let i=0;
let j=4;
let flag=false;
cp $NET_PATH /etc/network/interfaces.bak
while read -r line
do
	if [[ "$flag" = true ]] && [[ "$i" -le "$j" ]];then
		let i++
		continue
	else
		flag=false;
	fi
	ethN=$(echo $line | grep "$waNGKA")
	if [[ -n "$ethN" ]];then
		if [[ -n "$isVip" ]];then
			vip=$(echo $line | grep ":")
			if [[ -n "$vip" ]];then
				flag=true
			else
				flag=false
			fi
		else
			vip=$(echo $line | grep ":")
			if [[ -n "$vip" ]];then
				flag=false
			else
				flag=true
			fi
		fi
		if [[ "$flag" = true ]];then 
			echo isAuto = $(echo $line | grep "auto")
			if [[ -n "$isAuto" ]];then
				j=4;
			else
				j=3;
			fi
		fi
	fi
	echo $flag	
	if [[ "$flag" = false ]];then
		echo $line >>$NET_PATH_BAK
	fi	
done < $NET_PATH
if [[ "$UP" == "1" ]];then
	echo "auto $waNGKA" >>$NET_PATH_BAK
fi
echo "iface $waNGKA inet static" >>$NET_PATH_BAK
echo "address $IP_MOD" >>$NET_PATH_BAK
echo "netmask $MASK_MOD" >>$NET_PATH_BAK
if [[ ! -n "$isVip" ]] && [[ -n "$GW_MOD" ]];then
echo "gateway $GW_MOD" >>$NET_PATH_BAK
fi
echo "" >>$NET_PATH_BAK
cp $NET_PATH_BAK $NET_PATH
rm -rf $NET_PATH_BAK
if [[ "$UP" == "1" ]];then
	ifconfig $waNGKA $IP_MOD netmask $MASK_MOD
fi
if [[ ! -n "$isVip" ]];then
	if [[ -n "$GW_MOD" ]];then
		ip route del default
		ip route add default via $GW_MOD
	fi
fi