<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>DependScanner (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.optional.depend, class: DependScanner">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.optional.depend</a></div>
<h1 title="Class DependScanner" class="title">Class DependScanner</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">org.apache.tools.ant.DirectoryScanner</a>
<div class="inheritance">org.apache.tools.ant.types.optional.depend.DependScanner</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code>, <code><a href="../../ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></code>, <code><a href="../../selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DependScanner</span>
<span class="extends-implements">extends <a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></span></div>
<div class="block">DirectoryScanner for finding class dependencies.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULT_ANALYZER_CLASS" class="member-name-link">DEFAULT_ANALYZER_CLASS</a></code></div>
<div class="col-last even-row-color">
<div class="block">The name of the analyzer to use by default.</div>
</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.DirectoryScanner">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></h3>
<code><a href="../../../DirectoryScanner.html#basedir">basedir</a>, <a href="../../../DirectoryScanner.html#DEFAULTEXCLUDES">DEFAULTEXCLUDES</a>, <a href="../../../DirectoryScanner.html#dirsDeselected">dirsDeselected</a>, <a href="../../../DirectoryScanner.html#dirsExcluded">dirsExcluded</a>, <a href="../../../DirectoryScanner.html#dirsIncluded">dirsIncluded</a>, <a href="../../../DirectoryScanner.html#dirsNotIncluded">dirsNotIncluded</a>, <a href="../../../DirectoryScanner.html#DOES_NOT_EXIST_POSTFIX">DOES_NOT_EXIST_POSTFIX</a>, <a href="../../../DirectoryScanner.html#errorOnMissingDir">errorOnMissingDir</a>, <a href="../../../DirectoryScanner.html#everythingIncluded">everythingIncluded</a>, <a href="../../../DirectoryScanner.html#excludes">excludes</a>, <a href="../../../DirectoryScanner.html#filesDeselected">filesDeselected</a>, <a href="../../../DirectoryScanner.html#filesExcluded">filesExcluded</a>, <a href="../../../DirectoryScanner.html#filesIncluded">filesIncluded</a>, <a href="../../../DirectoryScanner.html#filesNotIncluded">filesNotIncluded</a>, <a href="../../../DirectoryScanner.html#haveSlowResults">haveSlowResults</a>, <a href="../../../DirectoryScanner.html#includes">includes</a>, <a href="../../../DirectoryScanner.html#isCaseSensitive">isCaseSensitive</a>, <a href="../../../DirectoryScanner.html#MAX_LEVELS_OF_SYMLINKS">MAX_LEVELS_OF_SYMLINKS</a>, <a href="../../../DirectoryScanner.html#selectors">selectors</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.apache.tools.ant.DirectoryScanner)" class="member-name-link">DependScanner</a><wbr>(<a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a>&nbsp;parentScanner)</code></div>
<div class="col-last even-row-color">
<div class="block">Create a DependScanner, using the given scanner to provide the basic
 set of files from which class files come.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addBasedir(java.io.File)" class="member-name-link">addBasedir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDefaultExcludes()" class="member-name-link">addDefaultExcludes</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add default exclusions to the current exclusions set.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExcludedDirectories()" class="member-name-link">getExcludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExcludedFiles()" class="member-name-link">getExcludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedDirectories()" class="member-name-link">getIncludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedDirsCount()" class="member-name-link">getIncludedDirsCount</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the count of included directories.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedFiles()" class="member-name-link">getIncludedFiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the names of the class files on which baseClass depends.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludedFilesCount()" class="member-name-link">getIncludedFilesCount</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the count of included files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotIncludedDirectories()" class="member-name-link">getNotIncludedDirectories</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the directories which matched none of the include
 patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotIncludedFiles()" class="member-name-link">getNotIncludedFiles</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the names of the files which matched none of the include
 patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scan()" class="member-name-link">scan</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Scans the base directory for files on which baseClass depends.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCaseSensitive(boolean)" class="member-name-link">setCaseSensitive</a><wbr>(boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether or not include and exclude patterns are matched
 in a case sensitive way.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludes(java.lang.String%5B%5D)" class="member-name-link">setExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the list of exclude patterns to use.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludes(java.lang.String%5B%5D)" class="member-name-link">setIncludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the list of include patterns to use.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRootClasses(java.util.Vector)" class="member-name-link">setRootClasses</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;rootClasses)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the root classes to be used to drive the scan.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.DirectoryScanner">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></h3>
<code><a href="../../../DirectoryScanner.html#addDefaultExclude(java.lang.String)">addDefaultExclude</a>, <a href="../../../DirectoryScanner.html#addExcludes(java.lang.String%5B%5D)">addExcludes</a>, <a href="../../../DirectoryScanner.html#clearResults()">clearResults</a>, <a href="../../../DirectoryScanner.html#couldHoldIncluded(java.lang.String)">couldHoldIncluded</a>, <a href="../../../DirectoryScanner.html#getBasedir()">getBasedir</a>, <a href="../../../DirectoryScanner.html#getDefaultExcludes()">getDefaultExcludes</a>, <a href="../../../DirectoryScanner.html#getDeselectedDirectories()">getDeselectedDirectories</a>, <a href="../../../DirectoryScanner.html#getDeselectedFiles()">getDeselectedFiles</a>, <a href="../../../DirectoryScanner.html#getNotFollowedSymlinks()">getNotFollowedSymlinks</a>, <a href="../../../DirectoryScanner.html#getResource(java.lang.String)">getResource</a>, <a href="../../../DirectoryScanner.html#isCaseSensitive()">isCaseSensitive</a>, <a href="../../../DirectoryScanner.html#isEverythingIncluded()">isEverythingIncluded</a>, <a href="../../../DirectoryScanner.html#isExcluded(java.lang.String)">isExcluded</a>, <a href="../../../DirectoryScanner.html#isFollowSymlinks()">isFollowSymlinks</a>, <a href="../../../DirectoryScanner.html#isIncluded(java.lang.String)">isIncluded</a>, <a href="../../../DirectoryScanner.html#isSelected(java.lang.String,java.io.File)">isSelected</a>, <a href="../../../DirectoryScanner.html#match(java.lang.String,java.lang.String)">match</a>, <a href="../../../DirectoryScanner.html#match(java.lang.String,java.lang.String,boolean)">match</a>, <a href="../../../DirectoryScanner.html#matchPath(java.lang.String,java.lang.String)">matchPath</a>, <a href="../../../DirectoryScanner.html#matchPath(java.lang.String,java.lang.String,boolean)">matchPath</a>, <a href="../../../DirectoryScanner.html#matchPatternStart(java.lang.String,java.lang.String)">matchPatternStart</a>, <a href="../../../DirectoryScanner.html#matchPatternStart(java.lang.String,java.lang.String,boolean)">matchPatternStart</a>, <a href="../../../DirectoryScanner.html#removeDefaultExclude(java.lang.String)">removeDefaultExclude</a>, <a href="../../../DirectoryScanner.html#resetDefaultExcludes()">resetDefaultExcludes</a>, <a href="../../../DirectoryScanner.html#scandir(java.io.File,java.lang.String,boolean)">scandir</a>, <a href="../../../DirectoryScanner.html#setBasedir(java.io.File)">setBasedir</a>, <a href="../../../DirectoryScanner.html#setBasedir(java.lang.String)">setBasedir</a>, <a href="../../../DirectoryScanner.html#setErrorOnMissingDir(boolean)">setErrorOnMissingDir</a>, <a href="../../../DirectoryScanner.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="../../../DirectoryScanner.html#setMaxLevelsOfSymlinks(int)">setMaxLevelsOfSymlinks</a>, <a href="../../../DirectoryScanner.html#setSelectors(org.apache.tools.ant.types.selectors.FileSelector%5B%5D)">setSelectors</a>, <a href="../../../DirectoryScanner.html#slowScan()">slowScan</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULT_ANALYZER_CLASS">
<h3>DEFAULT_ANALYZER_CLASS</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULT_ANALYZER_CLASS</span></div>
<div class="block">The name of the analyzer to use by default.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.types.optional.depend.DependScanner.DEFAULT_ANALYZER_CLASS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(org.apache.tools.ant.DirectoryScanner)">
<h3>DependScanner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DependScanner</span><wbr><span class="parameters">(<a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a>&nbsp;parentScanner)</span></div>
<div class="block">Create a DependScanner, using the given scanner to provide the basic
 set of files from which class files come.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parentScanner</code> - the DirectoryScanner which returns the files from
        which class files must come.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setRootClasses(java.util.Vector)">
<h3>setRootClasses</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRootClasses</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;rootClasses)</span></div>
<div class="block">Sets the root classes to be used to drive the scan.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rootClasses</code> - the rootClasses to be used for this scan.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludedFiles()">
<h3>getIncludedFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getIncludedFiles</span>()</div>
<div class="block">Get the names of the class files on which baseClass depends.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#getIncludedFiles()">getIncludedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getIncludedFiles()">getIncludedFiles</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludedFilesCount()">
<h3>getIncludedFilesCount</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getIncludedFilesCount</span>()</div>
<div class="block">Return the count of included files..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getIncludedFilesCount()">getIncludedFilesCount</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd><code>int</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="scan()">
<h3>scan</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">scan</span>()
          throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></span></div>
<div class="block">Scans the base directory for files on which baseClass depends.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#scan()">scan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#scan()">scan</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/IllegalStateException.html" title="class or interface in java.lang" class="external-link">IllegalStateException</a></code> - when basedir was set incorrectly.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDefaultExcludes()">
<h3>addDefaultExcludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDefaultExcludes</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#addDefaultExcludes()">DirectoryScanner</a></code></span></div>
<div class="block">Add default exclusions to the current exclusions set.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#addDefaultExcludes()">addDefaultExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#addDefaultExcludes()">addDefaultExcludes</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../../../DirectoryScanner.html#addDefaultExcludes()"><code>DirectoryScanner.addDefaultExcludes()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExcludedDirectories()">
<h3>getExcludedDirectories</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getExcludedDirectories</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#getExcludedDirectories()">DirectoryScanner</a></code></span></div>
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#getExcludedDirectories()">getExcludedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getExcludedDirectories()">getExcludedDirectories</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#getExcludedDirectories()">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExcludedFiles()">
<h3>getExcludedFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getExcludedFiles</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#getExcludedFiles()">DirectoryScanner</a></code></span></div>
<div class="block">Return the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#getExcludedFiles()">getExcludedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getExcludedFiles()">getExcludedFiles</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files which matched at least one of the
         include patterns and at least one of the exclude patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#getExcludedFiles()">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludedDirectories()">
<h3>getIncludedDirectories</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getIncludedDirectories</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#getIncludedDirectories()">DirectoryScanner</a></code></span></div>
<div class="block">Return the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#getIncludedDirectories()">getIncludedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getIncludedDirectories()">getIncludedDirectories</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#getIncludedDirectories()">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludedDirsCount()">
<h3>getIncludedDirsCount</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getIncludedDirsCount</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#getIncludedDirsCount()">DirectoryScanner</a></code></span></div>
<div class="block">Return the count of included directories.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getIncludedDirsCount()">getIncludedDirsCount</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd><code>int</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#getIncludedDirsCount()">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNotIncludedDirectories()">
<h3>getNotIncludedDirectories</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotIncludedDirectories</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#getNotIncludedDirectories()">DirectoryScanner</a></code></span></div>
<div class="block">Return the names of the directories which matched none of the include
 patterns. The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the directories which matched none of the include
 patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#getNotIncludedDirectories()">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNotIncludedFiles()">
<h3>getNotIncludedFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getNotIncludedFiles</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#getNotIncludedFiles()">DirectoryScanner</a></code></span></div>
<div class="block">Return the names of the files which matched none of the include
 patterns. The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#getNotIncludedFiles()">getNotIncludedFiles</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#getNotIncludedFiles()">getNotIncludedFiles</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Returns:</dt>
<dd>the names of the files which matched none of the include
         patterns.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#getNotIncludedFiles()">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExcludes(java.lang.String[])">
<h3>setExcludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;excludes)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#setExcludes(java.lang.String%5B%5D)">DirectoryScanner</a></code></span></div>
<div class="block">Set the list of exclude patterns to use. All '/' and '\' characters
 are replaced by <code>File.separatorChar</code>, so the separator used
 need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#setExcludes(java.lang.String%5B%5D)">setExcludes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#setExcludes(java.lang.String%5B%5D)">setExcludes</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>excludes</code> - A list of exclude patterns.
                 May be <code>null</code>, indicating that no files
                 should be excluded. If a non-<code>null</code> list is
                 given, all elements must be non-<code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#setExcludes(java.lang.String%5B%5D)">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludes(java.lang.String[])">
<h3>setIncludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;includes)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#setIncludes(java.lang.String%5B%5D)">DirectoryScanner</a></code></span></div>
<div class="block">Set the list of include patterns to use. All '/' and '\' characters
 are replaced by <code>File.separatorChar</code>, so the separator used
 need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#setIncludes(java.lang.String%5B%5D)">setIncludes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#setIncludes(java.lang.String%5B%5D)">setIncludes</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>includes</code> - A list of include patterns.
                 May be <code>null</code>, indicating that all files
                 should be included. If a non-<code>null</code>
                 list is given, all elements must be
                 non-<code>null</code>.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#setIncludes(java.lang.String%5B%5D)">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCaseSensitive(boolean)">
<h3>setCaseSensitive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCaseSensitive</span><wbr><span class="parameters">(boolean&nbsp;isCaseSensitive)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../DirectoryScanner.html#setCaseSensitive(boolean)">DirectoryScanner</a></code></span></div>
<div class="block">Set whether or not include and exclude patterns are matched
 in a case sensitive way.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../../../FileScanner.html#setCaseSensitive(boolean)">setCaseSensitive</a></code>&nbsp;in interface&nbsp;<code><a href="../../../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="../../../DirectoryScanner.html#setCaseSensitive(boolean)">setCaseSensitive</a></code>&nbsp;in class&nbsp;<code><a href="../../../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>isCaseSensitive</code> - whether or not the file system should be
                        regarded as a case sensitive one.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../DirectoryScanner.html#setCaseSensitive(boolean)">.</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addBasedir(java.io.File)">
<h3>addBasedir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addBasedir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</span></div>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
