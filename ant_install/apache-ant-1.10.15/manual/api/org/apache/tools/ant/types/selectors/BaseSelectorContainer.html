<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>BaseSelectorContainer (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors, class: BaseSelectorContainer">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<h1 title="Class BaseSelectorContainer" class="title">Class BaseSelectorContainer</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseSelector</a>
<div class="inheritance">org.apache.tools.ant.types.selectors.BaseSelectorContainer</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code>, <code><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code>, <code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></code>, <code><a href="MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></code>, <code><a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a></code>, <code><a href="OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></code>, <code><a href="SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">BaseSelectorContainer</span>
<span class="extends-implements">extends <a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a>
implements <a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></span></div>
<div class="block">This is the base class for selectors that can contain other selectors.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">BaseSelectorContainer</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.types.selectors.FileSelector)" class="member-name-link">add</a><wbr>(<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an arbitrary selector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addAnd(org.apache.tools.ant.types.selectors.AndSelector)" class="member-name-link">addAnd</a><wbr>(<a href="AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an "And" selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)" class="member-name-link">addContains</a><wbr>(<a href="ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a contains selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)" class="member-name-link">addContainsRegexp</a><wbr>(<a href="ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a regular expression selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)" class="member-name-link">addCustom</a><wbr>(<a href="ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an extended selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDate(org.apache.tools.ant.types.selectors.DateSelector)" class="member-name-link">addDate</a><wbr>(<a href="DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a selector date entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDepend(org.apache.tools.ant.types.selectors.DependSelector)" class="member-name-link">addDepend</a><wbr>(<a href="DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a depends selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)" class="member-name-link">addDepth</a><wbr>(<a href="DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a depth selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)" class="member-name-link">addDifferent</a><wbr>(<a href="DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">adds a different selector to the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)" class="member-name-link">addExecutable</a><wbr>(<a href="ExecutableSelector.html" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)" class="member-name-link">addFilename</a><wbr>(<a href="FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a selector filename entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)" class="member-name-link">addMajority</a><wbr>(<a href="MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a majority selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)" class="member-name-link">addModified</a><wbr>(<a href="modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add the modified selector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNone(org.apache.tools.ant.types.selectors.NoneSelector)" class="member-name-link">addNone</a><wbr>(<a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a "None" selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNot(org.apache.tools.ant.types.selectors.NotSelector)" class="member-name-link">addNot</a><wbr>(<a href="NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a "Not" selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOr(org.apache.tools.ant.types.selectors.OrSelector)" class="member-name-link">addOr</a><wbr>(<a href="OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an "Or" selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)" class="member-name-link">addOwnedBy</a><wbr>(<a href="OwnedBySelector.html" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a>&nbsp;o)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)" class="member-name-link">addPosixGroup</a><wbr>(<a href="PosixGroupSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a>&nbsp;o)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)" class="member-name-link">addPosixPermissions</a><wbr>(<a href="PosixPermissionsSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a>&nbsp;o)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)" class="member-name-link">addPresent</a><wbr>(<a href="PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a present selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)" class="member-name-link">addReadable</a><wbr>(<a href="ReadableSelector.html" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)" class="member-name-link">addSelector</a><wbr>(<a href="SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a "Select" selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSize(org.apache.tools.ant.types.selectors.SizeSelector)" class="member-name-link">addSize</a><wbr>(<a href="SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a selector size entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)" class="member-name-link">addSymlink</a><wbr>(<a href="SymlinkSelector.html" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addType(org.apache.tools.ant.types.selectors.TypeSelector)" class="member-name-link">addType</a><wbr>(<a href="TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">adds a type selector to the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addWritable(org.apache.tools.ant.types.selectors.WritableSelector)" class="member-name-link">addWritable</a><wbr>(<a href="WritableSelector.html" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a>&nbsp;w)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)" class="member-name-link">appendSelector</a><wbr>(<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new selector into this container.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSelectors(org.apache.tools.ant.Project)" class="member-name-link">getSelectors</a><wbr>(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the set of selectors as an array.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasSelectors()" class="member-name-link">hasSelectors</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether there are any selectors here.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>abstract boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#isSelected(java.io.File,java.lang.String,java.io.File)" class="member-name-link">isSelected</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Method that each selector will implement to create their selection
 behaviour.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectorCount()" class="member-name-link">selectorCount</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gives the count of the number of selectors in this container</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a><wbr>&lt;<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectorElements()" class="member-name-link">selectorElements</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an enumerator for accessing the set of selectors.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert the Selectors within this container to a string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validate()" class="member-name-link">validate</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This implementation validates the container by calling
 verifySettings() and then validates each contained selector
 provided that the selector implements the validate interface.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></h3>
<code><a href="BaseSelector.html#getError()">getError</a>, <a href="BaseSelector.html#setError(java.lang.String)">setError</a>, <a href="BaseSelector.html#setError(java.lang.String,java.lang.Throwable)">setError</a>, <a href="BaseSelector.html#verifySettings()">verifySettings</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#clone()">clone</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.FileSelector">Methods inherited from interface&nbsp;org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></h3>
<code><a href="FileSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>BaseSelectorContainer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BaseSelectorContainer</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="hasSelectors()">
<h3>hasSelectors</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasSelectors</span>()</div>
<div class="block">Indicates whether there are any selectors here.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#hasSelectors()">hasSelectors</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>true if there are selectors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="selectorCount()">
<h3>selectorCount</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">selectorCount</span>()</div>
<div class="block">Gives the count of the number of selectors in this container</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#selectorCount()">selectorCount</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>the number of selectors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSelectors(org.apache.tools.ant.Project)">
<h3>getSelectors</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]</span>&nbsp;<span class="element-name">getSelectors</span><wbr><span class="parameters">(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Returns the set of selectors as an array.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>p</code> - the current project</dd>
<dt>Returns:</dt>
<dd>an array of selectors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="selectorElements()">
<h3>selectorElements</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&gt;</span>&nbsp;<span class="element-name">selectorElements</span>()</div>
<div class="block">Returns an enumerator for accessing the set of selectors.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#selectorElements()">selectorElements</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>an enumerator for the selectors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Convert the Selectors within this container to a string. This will
 just be a helper class for the subclasses that put their own name
 around the contents listed here.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DataType.html#toString()">toString</a></code>&nbsp;in class&nbsp;<code><a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Returns:</dt>
<dd>comma separated list of Selectors contained in this one</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">
<h3>appendSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">appendSelector</span><wbr><span class="parameters">(<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</span></div>
<div class="block">Add a new selector into this container.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the new selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="validate()">
<h3>validate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validate</span>()</div>
<div class="block"><p>This implementation validates the container by calling
 verifySettings() and then validates each contained selector
 provided that the selector implements the validate interface.
 </p>
 <p>Ordinarily, this will validate all the elements of a selector
 container even if the isSelected() method of some elements is
 never called. This has two effects:</p>
 <ul>
 <li>Validation will often occur twice.
 <li>Since it is not required that selectors derive from
 BaseSelector, there could be selectors in the container whose
 error conditions are not detected if their isSelected() call
 is never made.
 </ul></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="BaseSelector.html#validate()">validate</a></code>&nbsp;in class&nbsp;<code><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected(java.io.File,java.lang.String,java.io.File)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public abstract</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Method that each selector will implement to create their selection
 behaviour. This is what makes SelectorContainer abstract.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="BaseSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in class&nbsp;<code><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>basedir</code> - the base directory the scan is being done from</dd>
<dd><code>filename</code> - the name of the file to check</dd>
<dd><code>file</code> - a java.io.File object for the filename that the selector
 can use</dd>
<dt>Returns:</dt>
<dd>whether the file should be selected or not</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">
<h3>addSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSelector</span><wbr><span class="parameters">(<a href="SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a>&nbsp;selector)</span></div>
<div class="block">add a "Select" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addAnd(org.apache.tools.ant.types.selectors.AndSelector)">
<h3>addAnd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addAnd</span><wbr><span class="parameters">(<a href="AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a>&nbsp;selector)</span></div>
<div class="block">add an "And" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOr(org.apache.tools.ant.types.selectors.OrSelector)">
<h3>addOr</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOr</span><wbr><span class="parameters">(<a href="OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a>&nbsp;selector)</span></div>
<div class="block">add an "Or" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addNot(org.apache.tools.ant.types.selectors.NotSelector)">
<h3>addNot</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNot</span><wbr><span class="parameters">(<a href="NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a>&nbsp;selector)</span></div>
<div class="block">add a "Not" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addNone(org.apache.tools.ant.types.selectors.NoneSelector)">
<h3>addNone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNone</span><wbr><span class="parameters">(<a href="NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a>&nbsp;selector)</span></div>
<div class="block">add a "None" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">
<h3>addMajority</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addMajority</span><wbr><span class="parameters">(<a href="MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a>&nbsp;selector)</span></div>
<div class="block">add a majority selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDate(org.apache.tools.ant.types.selectors.DateSelector)">
<h3>addDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDate</span><wbr><span class="parameters">(<a href="DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a>&nbsp;selector)</span></div>
<div class="block">add a selector date entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSize(org.apache.tools.ant.types.selectors.SizeSelector)">
<h3>addSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSize</span><wbr><span class="parameters">(<a href="SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a>&nbsp;selector)</span></div>
<div class="block">add a selector size entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">
<h3>addFilename</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFilename</span><wbr><span class="parameters">(<a href="FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a>&nbsp;selector)</span></div>
<div class="block">add a selector filename entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">
<h3>addCustom</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addCustom</span><wbr><span class="parameters">(<a href="ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a>&nbsp;selector)</span></div>
<div class="block">add an extended selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">
<h3>addContains</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContains</span><wbr><span class="parameters">(<a href="ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a>&nbsp;selector)</span></div>
<div class="block">add a contains selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">
<h3>addPresent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPresent</span><wbr><span class="parameters">(<a href="PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a>&nbsp;selector)</span></div>
<div class="block">add a present selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">
<h3>addDepth</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDepth</span><wbr><span class="parameters">(<a href="DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a>&nbsp;selector)</span></div>
<div class="block">add a depth selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDepend(org.apache.tools.ant.types.selectors.DependSelector)">
<h3>addDepend</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDepend</span><wbr><span class="parameters">(<a href="DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a>&nbsp;selector)</span></div>
<div class="block">add a depends selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">
<h3>addDifferent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDifferent</span><wbr><span class="parameters">(<a href="DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a>&nbsp;selector)</span></div>
<div class="block">adds a different selector to the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addType(org.apache.tools.ant.types.selectors.TypeSelector)">
<h3>addType</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addType</span><wbr><span class="parameters">(<a href="TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a>&nbsp;selector)</span></div>
<div class="block">adds a type selector to the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">
<h3>addContainsRegexp</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContainsRegexp</span><wbr><span class="parameters">(<a href="ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a>&nbsp;selector)</span></div>
<div class="block">add a regular expression selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">
<h3>addModified</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addModified</span><wbr><span class="parameters">(<a href="modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a>&nbsp;selector)</span></div>
<div class="block">add the modified selector</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addReadable(org.apache.tools.ant.types.selectors.ReadableSelector)">
<h3>addReadable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addReadable</span><wbr><span class="parameters">(<a href="ReadableSelector.html" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a>&nbsp;r)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="addWritable(org.apache.tools.ant.types.selectors.WritableSelector)">
<h3>addWritable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addWritable</span><wbr><span class="parameters">(<a href="WritableSelector.html" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a>&nbsp;w)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="addExecutable(org.apache.tools.ant.types.selectors.ExecutableSelector)">
<h3>addExecutable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExecutable</span><wbr><span class="parameters">(<a href="ExecutableSelector.html" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a>&nbsp;e)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>e</code> - ExecutableSelector</dd>
<dt>Since:</dt>
<dd>1.10.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSymlink(org.apache.tools.ant.types.selectors.SymlinkSelector)">
<h3>addSymlink</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSymlink</span><wbr><span class="parameters">(<a href="SymlinkSelector.html" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a>&nbsp;e)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>e</code> - SymlinkSelector</dd>
<dt>Since:</dt>
<dd>1.10.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOwnedBy(org.apache.tools.ant.types.selectors.OwnedBySelector)">
<h3>addOwnedBy</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOwnedBy</span><wbr><span class="parameters">(<a href="OwnedBySelector.html" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a>&nbsp;o)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - OwnedBySelector</dd>
<dt>Since:</dt>
<dd>1.10.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addPosixGroup(org.apache.tools.ant.types.selectors.PosixGroupSelector)">
<h3>addPosixGroup</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPosixGroup</span><wbr><span class="parameters">(<a href="PosixGroupSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a>&nbsp;o)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - PosixGroupSelector</dd>
<dt>Since:</dt>
<dd>1.10.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addPosixPermissions(org.apache.tools.ant.types.selectors.PosixPermissionsSelector)">
<h3>addPosixPermissions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPosixPermissions</span><wbr><span class="parameters">(<a href="PosixPermissionsSelector.html" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a>&nbsp;o)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - PosixPermissionsSelector</dd>
<dt>Since:</dt>
<dd>1.10.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.types.selectors.FileSelector)">
<h3>add</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</span></div>
<div class="block">add an arbitrary selector</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="SelectorContainer.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a></code>&nbsp;in interface&nbsp;<code><a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span>
                               throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">DataType</a></code></span></div>
<div class="block">Check to see whether any DataType we hold references to is
 included in the Stack (which holds all DataType instances that
 directly or indirectly reference this instance, including this
 instance itself).

 <p>If one is included, throw a BuildException created by <a href="../DataType.html#circularReference()"><code>circularReference</code></a>.</p>

 <p>This implementation is appropriate only for a DataType that
 cannot hold other DataTypes as children.</p>

 <p>The general contract of this method is that it shouldn't do
 anything if <a href="../DataType.html#checked"><code>DataType.checked</code></a> is true and
 set it to true on exit.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a></code>&nbsp;in class&nbsp;<code><a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stk</code> - the stack of references to check.</dd>
<dd><code>p</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
