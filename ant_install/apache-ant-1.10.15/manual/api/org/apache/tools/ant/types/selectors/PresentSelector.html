<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>PresentSelector (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors, class: PresentSelector">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<h1 title="Class PresentSelector" class="title">Class PresentSelector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseSelector</a>
<div class="inheritance">org.apache.tools.ant.types.selectors.PresentSelector</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code>, <code><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">PresentSelector</span>
<span class="extends-implements">extends <a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></span></div>
<div class="block">Selector that filters files based on whether they appear in another
 directory tree. It can contain a mapper element, so isn't available
 as an ExtendSelector (since those parameters can't hold other
 elements).</div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="PresentSelector.FilePresence.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a></code></div>
<div class="col-last even-row-color">
<div class="block">Enumerated attribute with the values for indicating where a file's
 presence is allowed and required.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">PresentSelector</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfigured(org.apache.tools.ant.util.FileNameMapper)" class="member-name-link">addConfigured</a><wbr>(<a href="../../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;fileNameMapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a configured FileNameMapper instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createMapper()" class="member-name-link">createMapper</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Defines the FileNameMapper to use (nested mapper element).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelected(java.io.File,java.lang.String,java.io.File)" class="member-name-link">isSelected</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The heart of the matter.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPresent(org.apache.tools.ant.types.selectors.PresentSelector.FilePresence)" class="member-name-link">setPresent</a><wbr>(<a href="PresentSelector.FilePresence.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a>&nbsp;fp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This sets whether to select a file if its dest file is present.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTargetdir(java.io.File)" class="member-name-link">setTargetdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetdir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The name of the file or directory which is checked for matching
 files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Basic DataType toString().</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#verifySettings()" class="member-name-link">verifySettings</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks to make sure all settings are kosher.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></h3>
<code><a href="BaseSelector.html#getError()">getError</a>, <a href="BaseSelector.html#setError(java.lang.String)">setError</a>, <a href="BaseSelector.html#setError(java.lang.String,java.lang.Throwable)">setError</a>, <a href="BaseSelector.html#validate()">validate</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#clone()">clone</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.FileSelector">Methods inherited from interface&nbsp;org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></h3>
<code><a href="FileSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>PresentSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">PresentSelector</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../DataType.html#toString()">DataType</a></code></span></div>
<div class="block">Basic DataType toString().</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../DataType.html#toString()">toString</a></code>&nbsp;in class&nbsp;<code><a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Returns:</dt>
<dd>a string describing this object</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTargetdir(java.io.File)">
<h3>setTargetdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTargetdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetdir)</span></div>
<div class="block">The name of the file or directory which is checked for matching
 files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetdir</code> - the directory to scan looking for matching files.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createMapper()">
<h3>createMapper</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></span>&nbsp;<span class="element-name">createMapper</span>()
                    throws <span class="exceptions"><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Defines the FileNameMapper to use (nested mapper element).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a mapper to be configured</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if more than one mapper defined</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addConfigured(org.apache.tools.ant.util.FileNameMapper)">
<h3>addConfigured</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfigured</span><wbr><span class="parameters">(<a href="../../util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>&nbsp;fileNameMapper)</span></div>
<div class="block">Add a configured FileNameMapper instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileNameMapper</code> - the FileNameMapper to add</dd>
<dt>Throws:</dt>
<dd><code><a href="../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if more than one mapper defined</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setPresent(org.apache.tools.ant.types.selectors.PresentSelector.FilePresence)">
<h3>setPresent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPresent</span><wbr><span class="parameters">(<a href="PresentSelector.FilePresence.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a>&nbsp;fp)</span></div>
<div class="block">This sets whether to select a file if its dest file is present.
 It could be a <code>negate</code> boolean, but by doing things
 this way, we get some documentation on how the system works.
 A user looking at the documentation should clearly understand
 that the ONLY files whose presence is being tested are those
 that already exist in the source directory, hence the lack of
 a <code>destonly</code> option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fp</code> - An attribute set to either <code>srconly</code> or
           <code>both</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="verifySettings()">
<h3>verifySettings</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">verifySettings</span>()</div>
<div class="block">Checks to make sure all settings are kosher. In this case, it
 means that the targetdir attribute has been set and we have a mapper.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="BaseSelector.html#verifySettings()">verifySettings</a></code>&nbsp;in class&nbsp;<code><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected(java.io.File,java.lang.String,java.io.File)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">The heart of the matter. This is where the selector gets to decide
 on the inclusion of a file in a particular fileset.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="FileSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="BaseSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in class&nbsp;<code><a href="BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>basedir</code> - the base directory the scan is being done from</dd>
<dd><code>filename</code> - is the name of the file to check</dd>
<dd><code>file</code> - is a java.io.File object the selector can use</dd>
<dt>Returns:</dt>
<dd>whether the file should be selected or not</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
