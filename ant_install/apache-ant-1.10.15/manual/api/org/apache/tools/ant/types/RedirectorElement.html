<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>RedirectorElement (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, class: RedirectorElement">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Class RedirectorElement" class="title">Class RedirectorElement</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance">org.apache.tools.ant.types.RedirectorElement</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">RedirectorElement</span>
<span class="extends-implements">extends <a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></span></div>
<div class="block">Element representation of a <code>Redirector</code>.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.6.2</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="DataType.html#checked">checked</a>, <a href="DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">RedirectorElement</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredErrorMapper(org.apache.tools.ant.types.Mapper)" class="member-name-link">addConfiguredErrorMapper</a><wbr>(<a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;errorMapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the error file mapper.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredInputMapper(org.apache.tools.ant.types.Mapper)" class="member-name-link">addConfiguredInputMapper</a><wbr>(<a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;inputMapper)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the input file mapper.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addConfiguredOutputMapper(org.apache.tools.ant.types.Mapper)" class="member-name-link">addConfiguredOutputMapper</a><wbr>(<a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;outputMapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add the output file mapper.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure(org.apache.tools.ant.taskdefs.Redirector)" class="member-name-link">configure</a><wbr>(<a href="../taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a>&nbsp;redirector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configure the specified <code>Redirector</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configure(org.apache.tools.ant.taskdefs.Redirector,java.lang.String)" class="member-name-link">configure</a><wbr>(<a href="../taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a>&nbsp;redirector,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourcefile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Configure the specified <code>Redirector</code>
 for the specified sourcefile.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createErrorFilterChain()" class="member-name-link">createErrorFilterChain</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a nested error <code>FilterChain</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createInputFilterChain()" class="member-name-link">createInputFilterChain</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a nested input <code>FilterChain</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createMergeMapper(java.io.File)" class="member-name-link">createMergeMapper</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a merge mapper pointing to the specified destination file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createOutputFilterChain()" class="member-name-link">createOutputFilterChain</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a nested output <code>FilterChain</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)" class="member-name-link">dieOnCircularReference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Overrides the version of DataType to recurse on all DataType
 child elements that may have been added.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAlwaysLog(boolean)" class="member-name-link">setAlwaysLog</a><wbr>(boolean&nbsp;alwaysLog)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, (error and non-error) output will be "teed", redirected
 as specified while being sent to Ant's logging mechanism as if no
 redirection had taken place.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAppend(boolean)" class="member-name-link">setAppend</a><wbr>(boolean&nbsp;append)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether output should be appended to or overwrite an existing file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBinaryOutput(boolean)" class="member-name-link">setBinaryOutput</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether to consider the output created by the process binary.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCreateEmptyFiles(boolean)" class="member-name-link">setCreateEmptyFiles</a><wbr>(boolean&nbsp;createEmptyFiles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether output and error files should be created even when empty.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setError(java.io.File)" class="member-name-link">setError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;error)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the file to which standard error is to be redirected.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorEncoding(java.lang.String)" class="member-name-link">setErrorEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorEncoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the error encoding.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorProperty(java.lang.String)" class="member-name-link">setErrorProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Property name whose value should be set to the error of
 the process.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInput(java.io.File)" class="member-name-link">setInput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;input)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the input to use for the task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputEncoding(java.lang.String)" class="member-name-link">setInputEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputEncoding)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the input encoding.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputString(java.lang.String)" class="member-name-link">setInputString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the string to use as input</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogError(boolean)" class="member-name-link">setLogError</a><wbr>(boolean&nbsp;logError)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Controls whether error output of exec is logged.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLogInputString(boolean)" class="member-name-link">setLogInputString</a><wbr>(boolean&nbsp;logInputString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set whether to include the value of the input string in log messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutput(java.io.File)" class="member-name-link">setOutput</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;out)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">File the output of the process is redirected to.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputEncoding(java.lang.String)" class="member-name-link">setOutputEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputEncoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the output encoding.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOutputProperty(java.lang.String)" class="member-name-link">setOutputProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Property name whose value should be set to the output of
 the process.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRefid(org.apache.tools.ant.types.Reference)" class="member-name-link">setRefid</a><wbr>(<a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Make this instance in effect a reference to another instance.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toFileArray(java.lang.String%5B%5D)" class="member-name-link">toFileArray</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a <code>File[]</code> from the specified set of filenames.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="DataType.html#circularReference()">circularReference</a>, <a href="DataType.html#clone()">clone</a>, <a href="DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="DataType.html#getRefid()">getRefid</a>, <a href="DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="DataType.html#isChecked()">isChecked</a>, <a href="DataType.html#isReference()">isReference</a>, <a href="DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="DataType.html#setChecked(boolean)">setChecked</a>, <a href="DataType.html#tooManyAttributes()">tooManyAttributes</a>, <a href="DataType.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a>, <a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>RedirectorElement</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">RedirectorElement</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addConfiguredInputMapper(org.apache.tools.ant.types.Mapper)">
<h3>addConfiguredInputMapper</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredInputMapper</span><wbr><span class="parameters">(<a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;inputMapper)</span></div>
<div class="block">Add the input file mapper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputMapper</code> - <code>Mapper</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addConfiguredOutputMapper(org.apache.tools.ant.types.Mapper)">
<h3>addConfiguredOutputMapper</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredOutputMapper</span><wbr><span class="parameters">(<a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;outputMapper)</span></div>
<div class="block">Add the output file mapper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputMapper</code> - <code>Mapper</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addConfiguredErrorMapper(org.apache.tools.ant.types.Mapper)">
<h3>addConfiguredErrorMapper</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addConfiguredErrorMapper</span><wbr><span class="parameters">(<a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a>&nbsp;errorMapper)</span></div>
<div class="block">Add the error file mapper.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorMapper</code> - <code>Mapper</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRefid(org.apache.tools.ant.types.Reference)">
<h3>setRefid</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRefid</span><wbr><span class="parameters">(<a href="Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span>
              throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Make this instance in effect a reference to another instance.

 <p>You must not set another attribute or nest elements inside
 this element if you make it a reference.</p></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a></code>&nbsp;in class&nbsp;<code><a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Parameters:</dt>
<dd><code>r</code> - the reference to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setInput(java.io.File)">
<h3>setInput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;input)</span></div>
<div class="block">Set the input to use for the task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input</code> - the file from which input is read.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setInputString(java.lang.String)">
<h3>setInputString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputString)</span></div>
<div class="block">Set the string to use as input</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputString</code> - the string which is used as the input source</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLogInputString(boolean)">
<h3>setLogInputString</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogInputString</span><wbr><span class="parameters">(boolean&nbsp;logInputString)</span></div>
<div class="block">Set whether to include the value of the input string in log messages.
 Defaults to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>logInputString</code> - true or false.</dd>
<dt>Since:</dt>
<dd>Ant 1.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOutput(java.io.File)">
<h3>setOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutput</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;out)</span></div>
<div class="block">File the output of the process is redirected to. If error is not
 redirected, it too will appear in the output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>out</code> - the file to which output stream is written.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOutputEncoding(java.lang.String)">
<h3>setOutputEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputEncoding)</span></div>
<div class="block">Set the output encoding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputEncoding</code> - <code>String</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setErrorEncoding(java.lang.String)">
<h3>setErrorEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorEncoding)</span></div>
<div class="block">Set the error encoding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorEncoding</code> - <code>String</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setInputEncoding(java.lang.String)">
<h3>setInputEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inputEncoding)</span></div>
<div class="block">Set the input encoding.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputEncoding</code> - <code>String</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLogError(boolean)">
<h3>setLogError</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLogError</span><wbr><span class="parameters">(boolean&nbsp;logError)</span></div>
<div class="block">Controls whether error output of exec is logged. This is only useful
 when output is being redirected and error output is desired in the
 Ant log.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>logError</code> - if true the standard error is sent to the Ant log system
        and not sent to output.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setError(java.io.File)">
<h3>setError</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;error)</span></div>
<div class="block">Set the file to which standard error is to be redirected.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>error</code> - the file to which error is to be written.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOutputProperty(java.lang.String)">
<h3>setOutputProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOutputProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputProperty)</span></div>
<div class="block">Property name whose value should be set to the output of
 the process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>outputProperty</code> - the name of the property to be set with the
        task's output.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setAppend(boolean)">
<h3>setAppend</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAppend</span><wbr><span class="parameters">(boolean&nbsp;append)</span></div>
<div class="block">Whether output should be appended to or overwrite an existing file.
 Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>append</code> - if true output and error streams are appended to their
        respective files, if specified.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setAlwaysLog(boolean)">
<h3>setAlwaysLog</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAlwaysLog</span><wbr><span class="parameters">(boolean&nbsp;alwaysLog)</span></div>
<div class="block">If true, (error and non-error) output will be "teed", redirected
 as specified while being sent to Ant's logging mechanism as if no
 redirection had taken place.  Defaults to false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>alwaysLog</code> - <code>boolean</code></dd>
<dt>Since:</dt>
<dd>Ant 1.6.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCreateEmptyFiles(boolean)">
<h3>setCreateEmptyFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCreateEmptyFiles</span><wbr><span class="parameters">(boolean&nbsp;createEmptyFiles)</span></div>
<div class="block">Whether output and error files should be created even when empty.
 Defaults to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>createEmptyFiles</code> - <code>boolean</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setErrorProperty(java.lang.String)">
<h3>setErrorProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</span></div>
<div class="block">Property name whose value should be set to the error of
 the process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorProperty</code> - the name of the property to be set
        with the error output.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createInputFilterChain()">
<h3>createInputFilterChain</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></span>&nbsp;<span class="element-name">createInputFilterChain</span>()</div>
<div class="block">Create a nested input <code>FilterChain</code>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>FilterChain</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createOutputFilterChain()">
<h3>createOutputFilterChain</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></span>&nbsp;<span class="element-name">createOutputFilterChain</span>()</div>
<div class="block">Create a nested output <code>FilterChain</code>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>FilterChain</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createErrorFilterChain()">
<h3>createErrorFilterChain</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="FilterChain.html" title="class in org.apache.tools.ant.types">FilterChain</a></span>&nbsp;<span class="element-name">createErrorFilterChain</span>()</div>
<div class="block">Create a nested error <code>FilterChain</code>.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd><code>FilterChain</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBinaryOutput(boolean)">
<h3>setBinaryOutput</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBinaryOutput</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether to consider the output created by the process binary.

 <p>Binary output will not be split into lines which may make
 error and normal output look mixed up when they get written to
 the same stream.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>1.9.4</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="configure(org.apache.tools.ant.taskdefs.Redirector)">
<h3>configure</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(<a href="../taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a>&nbsp;redirector)</span></div>
<div class="block">Configure the specified <code>Redirector</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>redirector</code> - <code>Redirector</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="configure(org.apache.tools.ant.taskdefs.Redirector,java.lang.String)">
<h3>configure</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configure</span><wbr><span class="parameters">(<a href="../taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</a>&nbsp;redirector,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourcefile)</span></div>
<div class="block">Configure the specified <code>Redirector</code>
 for the specified sourcefile.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>redirector</code> - <code>Redirector</code>.</dd>
<dd><code>sourcefile</code> - <code>String</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createMergeMapper(java.io.File)">
<h3>createMergeMapper</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="Mapper.html" title="class in org.apache.tools.ant.types">Mapper</a></span>&nbsp;<span class="element-name">createMergeMapper</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destfile)</span></div>
<div class="block">Create a merge mapper pointing to the specified destination file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destfile</code> - <code>File</code></dd>
<dt>Returns:</dt>
<dd><code>Mapper</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="toFileArray(java.lang.String[])">
<h3>toFileArray</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</span>&nbsp;<span class="element-name">toFileArray</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;name)</span></div>
<div class="block">Return a <code>File[]</code> from the specified set of filenames.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - <code>String[]</code></dd>
<dt>Returns:</dt>
<dd><code>File[]</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">
<h3>dieOnCircularReference</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">dieOnCircularReference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;stk,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span>
                               throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Overrides the version of DataType to recurse on all DataType
 child elements that may have been added.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a></code>&nbsp;in class&nbsp;<code><a href="DataType.html" title="class in org.apache.tools.ant.types">DataType</a></code></dd>
<dt>Parameters:</dt>
<dd><code>stk</code> - the stack of data types to use (recursively).</dd>
<dd><code>p</code> - the project to use to dereference the references.</dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - on error.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
