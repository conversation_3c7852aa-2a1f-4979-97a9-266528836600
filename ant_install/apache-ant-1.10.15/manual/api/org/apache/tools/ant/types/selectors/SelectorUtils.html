<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>SelectorUtils (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.selectors, class: SelectorUtils">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.selectors</a></div>
<h1 title="Class SelectorUtils" class="title">Class SelectorUtils</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.ant.types.selectors.SelectorUtils</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">SelectorUtils</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block"><p>This is a utility class used by selectors and DirectoryScanner. The
 functionality more properly belongs just to selectors, but unfortunately
 DirectoryScanner exposed these as protected methods. Thus we have to
 support any subclasses of DirectoryScanner that may access these methods.
 </p>
 <p>This is a Singleton.</p></div>
<dl class="notes">
<dt>Since:</dt>
<dd>1.5</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEEP_TREE_MATCH" class="member-name-link">DEEP_TREE_MATCH</a></code></div>
<div class="col-last even-row-color">
<div class="block">The pattern that matches an arbitrary number of directories.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SelectorUtils.html" title="class in org.apache.tools.ant.types.selectors">SelectorUtils</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInstance()" class="member-name-link">getInstance</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Retrieves the instance of the Singleton.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#hasWildcards(java.lang.String)" class="member-name-link">hasWildcards</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests if a string contains stars or question marks</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isOutOfDate(java.io.File,java.io.File,int)" class="member-name-link">isOutOfDate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;target,
 int&nbsp;granularity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns dependency information on these two files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isOutOfDate(org.apache.tools.ant.types.Resource,org.apache.tools.ant.types.Resource,int)" class="member-name-link">isOutOfDate</a><wbr>(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src,
 <a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;target,
 int&nbsp;granularity)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns dependency information on these two resources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isOutOfDate(org.apache.tools.ant.types.Resource,org.apache.tools.ant.types.Resource,long)" class="member-name-link">isOutOfDate</a><wbr>(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src,
 <a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;target,
 long&nbsp;granularity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns dependency information on these two resources.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#match(java.lang.String,java.lang.String)" class="member-name-link">match</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests whether or not a string matches against a pattern.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#match(java.lang.String,java.lang.String,boolean)" class="member-name-link">match</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;caseSensitive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests whether or not a string matches against a pattern.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPath(java.lang.String,java.lang.String)" class="member-name-link">matchPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests whether or not a given path matches a given pattern.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPath(java.lang.String,java.lang.String,boolean)" class="member-name-link">matchPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests whether or not a given path matches a given pattern.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPatternStart(java.lang.String,java.lang.String)" class="member-name-link">matchPatternStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests whether or not a given path matches the start of a given
 pattern up to the first "**".</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#matchPatternStart(java.lang.String,java.lang.String,boolean)" class="member-name-link">matchPatternStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Tests whether or not a given path matches the start of a given
 pattern up to the first "**".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#removeWhitespace(java.lang.String)" class="member-name-link">removeWhitespace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">"Flattens" a string by removing all whitespace (space, tab, linefeed,
 carriage return, and formfeed).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#rtrimWildcardTokens(java.lang.String)" class="member-name-link">rtrimWildcardTokens</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">removes from a pattern all tokens to the right containing wildcards</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#tokenizePath(java.lang.String)" class="member-name-link">tokenizePath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Breaks a path up into a Vector of path elements, tokenizing on
 <code>File.separator</code>.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#tokenizePath(java.lang.String,java.lang.String)" class="member-name-link">tokenizePath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;separator)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Breaks a path up into a Vector of path elements, tokenizing on</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEEP_TREE_MATCH">
<h3>DEEP_TREE_MATCH</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">DEEP_TREE_MATCH</span></div>
<div class="block">The pattern that matches an arbitrary number of directories.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../constant-values.html#org.apache.tools.ant.types.selectors.SelectorUtils.DEEP_TREE_MATCH">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getInstance()">
<h3>getInstance</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SelectorUtils.html" title="class in org.apache.tools.ant.types.selectors">SelectorUtils</a></span>&nbsp;<span class="element-name">getInstance</span>()</div>
<div class="block">Retrieves the instance of the Singleton.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>singleton instance</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="matchPatternStart(java.lang.String,java.lang.String)">
<h3>matchPatternStart</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPatternStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</span></div>
<div class="block">Tests whether or not a given path matches the start of a given
 pattern up to the first "**".
 <p>
 This is not a general purpose test and should only be used if you
 can live with false positives. For example, <code>pattern=**\a</code>
 and <code>str=b</code> will yield <code>true</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>whether or not a given path matches the start of a given
 pattern up to the first "**".</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="matchPatternStart(java.lang.String,java.lang.String,boolean)">
<h3>matchPatternStart</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPatternStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Tests whether or not a given path matches the start of a given
 pattern up to the first "**".
 <p>
 This is not a general purpose test and should only be used if you
 can live with false positives. For example, <code>pattern=**\a</code>
 and <code>str=b</code> will yield <code>true</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dd><code>isCaseSensitive</code> - Whether or not matching should be performed
                        case sensitively.</dd>
<dt>Returns:</dt>
<dd>whether or not a given path matches the start of a given
 pattern up to the first "**".</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="matchPath(java.lang.String,java.lang.String)">
<h3>matchPath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</span></div>
<div class="block">Tests whether or not a given path matches a given pattern.

 If you need to call this method multiple times with the same
 pattern you should rather use TokenizedPath</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the pattern matches against the string,
         or <code>false</code> otherwise.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="TokenizedPath.html" title="class in org.apache.tools.ant.types.selectors"><code>TokenizedPath</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="matchPath(java.lang.String,java.lang.String,boolean)">
<h3>matchPath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">matchPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Tests whether or not a given path matches a given pattern.

 If you need to call this method multiple times with the same
 pattern you should rather use TokenizedPattern</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against. Must not be
                <code>null</code>.</dd>
<dd><code>str</code> - The path to match, as a String. Must not be
                <code>null</code>.</dd>
<dd><code>isCaseSensitive</code> - Whether or not matching should be performed
                        case sensitively.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the pattern matches against the string,
         or <code>false</code> otherwise.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="TokenizedPattern.html" title="class in org.apache.tools.ant.types.selectors"><code>TokenizedPattern</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="match(java.lang.String,java.lang.String)">
<h3>match</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str)</span></div>
<div class="block">Tests whether or not a string matches against a pattern.
 The pattern may contain two special characters:<br>
 '*' means zero or more characters<br>
 '?' means one and only one character</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against.
                Must not be <code>null</code>.</dd>
<dd><code>str</code> - The string which must be matched against the pattern.
                Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the string matches against the pattern,
         or <code>false</code> otherwise.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="match(java.lang.String,java.lang.String,boolean)">
<h3>match</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">match</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;pattern,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;str,
 boolean&nbsp;caseSensitive)</span></div>
<div class="block">Tests whether or not a string matches against a pattern.
 The pattern may contain two special characters:<br>
 '*' means zero or more characters<br>
 '?' means one and only one character</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pattern</code> - The pattern to match against.
                Must not be <code>null</code>.</dd>
<dd><code>str</code> - The string which must be matched against the pattern.
                Must not be <code>null</code>.</dd>
<dd><code>caseSensitive</code> - Whether or not matching should be performed
                        case sensitively.</dd>
<dt>Returns:</dt>
<dd><code>true</code> if the string matches against the pattern,
         or <code>false</code> otherwise.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="tokenizePath(java.lang.String)">
<h3>tokenizePath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">tokenizePath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Breaks a path up into a Vector of path elements, tokenizing on
 <code>File.separator</code>.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - Path to tokenize. Must not be <code>null</code>.</dd>
<dt>Returns:</dt>
<dd>a Vector of path elements from the tokenized path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="tokenizePath(java.lang.String,java.lang.String)">
<h3>tokenizePath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">tokenizePath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;separator)</span></div>
<div class="block">Breaks a path up into a Vector of path elements, tokenizing on</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - Path to tokenize. Must not be <code>null</code>.</dd>
<dd><code>separator</code> - the separator against which to tokenize.</dd>
<dt>Returns:</dt>
<dd>a Vector of path elements from the tokenized path</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isOutOfDate(java.io.File,java.io.File,int)">
<h3>isOutOfDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isOutOfDate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;target,
 int&nbsp;granularity)</span></div>
<div class="block">Returns dependency information on these two files. If src has been
 modified later than target, it returns true. If target doesn't exist,
 it likewise returns true. Otherwise, target is newer than src and
 is not out of date, thus the method returns false. It also returns
 false if the src file doesn't even exist, since how could the
 target then be out of date.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - the original file</dd>
<dd><code>target</code> - the file being compared against</dd>
<dd><code>granularity</code> - the amount in milliseconds of slack we will give in
        determining out of dateness</dd>
<dt>Returns:</dt>
<dd>whether the target is out of date</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isOutOfDate(org.apache.tools.ant.types.Resource,org.apache.tools.ant.types.Resource,int)">
<h3>isOutOfDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isOutOfDate</span><wbr><span class="parameters">(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src,
 <a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;target,
 int&nbsp;granularity)</span></div>
<div class="block">Returns dependency information on these two resources. If src has been
 modified later than target, it returns true. If target doesn't exist,
 it likewise returns true. Otherwise, target is newer than src and
 is not out of date, thus the method returns false. It also returns
 false if the src file doesn't even exist, since how could the
 target then be out of date.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - the original resource</dd>
<dd><code>target</code> - the resource being compared against</dd>
<dd><code>granularity</code> - the int amount in milliseconds of slack we will give in
        determining out of dateness</dd>
<dt>Returns:</dt>
<dd>whether the target is out of date</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isOutOfDate(org.apache.tools.ant.types.Resource,org.apache.tools.ant.types.Resource,long)">
<h3>isOutOfDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isOutOfDate</span><wbr><span class="parameters">(<a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src,
 <a href="../Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;target,
 long&nbsp;granularity)</span></div>
<div class="block">Returns dependency information on these two resources. If src has been
 modified later than target, it returns true. If target doesn't exist,
 it likewise returns true. Otherwise, target is newer than src and
 is not out of date, thus the method returns false. It also returns
 false if the src file doesn't even exist, since how could the
 target then be out of date.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - the original resource</dd>
<dd><code>target</code> - the resource being compared against</dd>
<dd><code>granularity</code> - the long amount in milliseconds of slack we will give in
        determining out of dateness</dd>
<dt>Returns:</dt>
<dd>whether the target is out of date</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="removeWhitespace(java.lang.String)">
<h3>removeWhitespace</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">removeWhitespace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</span></div>
<div class="block">"Flattens" a string by removing all whitespace (space, tab, linefeed,
 carriage return, and formfeed). This uses StringTokenizer and the
 default set of tokens as documented in the single argument constructor.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input</code> - a String to remove all whitespace.</dd>
<dt>Returns:</dt>
<dd>a String that has had all whitespace removed.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasWildcards(java.lang.String)">
<h3>hasWildcards</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasWildcards</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</span></div>
<div class="block">Tests if a string contains stars or question marks</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input</code> - a String which one wants to test for containing wildcard</dd>
<dt>Returns:</dt>
<dd>true if the string contains at least a star or a question mark</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="rtrimWildcardTokens(java.lang.String)">
<h3>rtrimWildcardTokens</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">rtrimWildcardTokens</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;input)</span></div>
<div class="block">removes from a pattern all tokens to the right containing wildcards</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input</code> - the input string</dd>
<dt>Returns:</dt>
<dd>the leftmost part of the pattern without wildcards</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
