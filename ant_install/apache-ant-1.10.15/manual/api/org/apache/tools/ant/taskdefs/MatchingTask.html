<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>MatchingTask (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: MatchingTask">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class MatchingTask" class="title">Class MatchingTask</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.MatchingTask</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="optional/Cab.html" title="class in org.apache.tools.ant.taskdefs.optional">Cab</a></code>, <code><a href="Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</a></code>, <code><a href="Copydir.html" title="class in org.apache.tools.ant.taskdefs">Copydir</a></code>, <code><a href="Delete.html" title="class in org.apache.tools.ant.taskdefs">Delete</a></code>, <code><a href="optional/depend/Depend.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">Depend</a></code>, <code><a href="DependSet.html" title="class in org.apache.tools.ant.taskdefs">DependSet</a></code>, <code><a href="optional/ejb/EjbJar.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar</a></code>, <code><a href="FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</a></code>, <code><a href="optional/image/Image.html" title="class in org.apache.tools.ant.taskdefs.optional.image">Image</a></code>, <code><a href="optional/image/ImageIOTask.html" title="class in org.apache.tools.ant.taskdefs.optional.image">ImageIOTask</a></code>, <code><a href="Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</a></code>, <code><a href="optional/jlink/JlinkTask.html" title="class in org.apache.tools.ant.taskdefs.optional.jlink">JlinkTask</a></code>, <code><a href="optional/jsp/JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</a></code>, <code><a href="optional/Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</a></code>, <code><a href="optional/NetRexxC.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC</a></code>, <code><a href="optional/RenameExtensions.html" title="class in org.apache.tools.ant.taskdefs.optional">RenameExtensions</a></code>, <code><a href="Replace.html" title="class in org.apache.tools.ant.taskdefs">Replace</a></code>, <code><a href="Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</a></code>, <code><a href="Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</a></code>, <code><a href="optional/i18n/Translate.html" title="class in org.apache.tools.ant.taskdefs.optional.i18n">Translate</a></code>, <code><a href="optional/jsp/WLJspc.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">WLJspc</a></code>, <code><a href="XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</a></code>, <code><a href="Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">MatchingTask</span>
<span class="extends-implements">extends <a href="../Task.html" title="class in org.apache.tools.ant">Task</a>
implements <a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></span></div>
<div class="block">This is an abstract task that should be used by all those tasks that
 require to include or exclude files based on pattern matching.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></div>
<div class="col-second even-row-color"><code><a href="#fileset" class="member-name-link">fileset</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">MatchingTask</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.types.selectors.FileSelector)" class="member-name-link">add</a><wbr>(<a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an arbitrary selector</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addAnd(org.apache.tools.ant.types.selectors.AndSelector)" class="member-name-link">addAnd</a><wbr>(<a href="../types/selectors/AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an "And" selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)" class="member-name-link">addContains</a><wbr>(<a href="../types/selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a contains selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)" class="member-name-link">addContainsRegexp</a><wbr>(<a href="../types/selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a regular expression selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)" class="member-name-link">addCustom</a><wbr>(<a href="../types/selectors/ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an extended selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDate(org.apache.tools.ant.types.selectors.DateSelector)" class="member-name-link">addDate</a><wbr>(<a href="../types/selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a selector date entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDepend(org.apache.tools.ant.types.selectors.DependSelector)" class="member-name-link">addDepend</a><wbr>(<a href="../types/selectors/DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a depends selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)" class="member-name-link">addDepth</a><wbr>(<a href="../types/selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a depth selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)" class="member-name-link">addDifferent</a><wbr>(<a href="../types/selectors/DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a type selector entry on the type list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)" class="member-name-link">addFilename</a><wbr>(<a href="../types/selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a selector filename entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)" class="member-name-link">addMajority</a><wbr>(<a href="../types/selectors/MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a majority selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)" class="member-name-link">addModified</a><wbr>(<a href="../types/selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add the modified selector</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNone(org.apache.tools.ant.types.selectors.NoneSelector)" class="member-name-link">addNone</a><wbr>(<a href="../types/selectors/NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a "None" selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNot(org.apache.tools.ant.types.selectors.NotSelector)" class="member-name-link">addNot</a><wbr>(<a href="../types/selectors/NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a "Not" selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOr(org.apache.tools.ant.types.selectors.OrSelector)" class="member-name-link">addOr</a><wbr>(<a href="../types/selectors/OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add an "Or" selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)" class="member-name-link">addPresent</a><wbr>(<a href="../types/selectors/PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a present selector entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)" class="member-name-link">addSelector</a><wbr>(<a href="../types/selectors/SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a "Select" selector entry on the selector list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addSize(org.apache.tools.ant.types.selectors.SizeSelector)" class="member-name-link">addSize</a><wbr>(<a href="../types/selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a selector size entry on the selector list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addType(org.apache.tools.ant.types.selectors.TypeSelector)" class="member-name-link">addType</a><wbr>(<a href="../types/selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a>&nbsp;selector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a type selector entry on the type list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)" class="member-name-link">appendSelector</a><wbr>(<a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new selector into this container.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExclude()" class="member-name-link">createExclude</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a name entry on the exclude list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExcludesFile()" class="member-name-link">createExcludesFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a name entry on the include files list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createInclude()" class="member-name-link">createInclude</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a name entry on the include list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createIncludesFile()" class="member-name-link">createIncludesFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a name entry on the include files list</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createPatternSet()" class="member-name-link">createPatternSet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">add a set of patterns</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDirectoryScanner(java.io.File)" class="member-name-link">getDirectoryScanner</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the directory scanner needed to access the files to process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected final <a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImplicitFileSet()" class="member-name-link">getImplicitFileSet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Accessor for the implicit fileset.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSelectors(org.apache.tools.ant.Project)" class="member-name-link">getSelectors</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the set of selectors as an array.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasSelectors()" class="member-name-link">hasSelectors</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether there are any selectors here.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectorCount()" class="member-name-link">selectorCount</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gives the count of the number of selectors in this container</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a><wbr>&lt;<a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectorElements()" class="member-name-link">selectorElements</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an enumerator for accessing the set of selectors.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCaseSensitive(boolean)" class="member-name-link">setCaseSensitive</a><wbr>(boolean&nbsp;isCaseSensitive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets case sensitivity of the file system</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultexcludes(boolean)" class="member-name-link">setDefaultexcludes</a><wbr>(boolean&nbsp;useDefaultExcludes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether default exclusions should be used or not.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludes(java.lang.String)" class="member-name-link">setExcludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;excludes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the set of exclude patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExcludesfile(java.io.File)" class="member-name-link">setExcludesfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;excludesfile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of the file containing the includes patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFollowSymlinks(boolean)" class="member-name-link">setFollowSymlinks</a><wbr>(boolean&nbsp;followSymlinks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets whether or not symbolic links should be followed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludes(java.lang.String)" class="member-name-link">setIncludes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;includes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the set of include patterns.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludesfile(java.io.File)" class="member-name-link">setIncludesfile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;includesfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of the file containing the includes patterns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProject(org.apache.tools.ant.Project)" class="member-name-link">setProject</a><wbr>(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the project object of this component.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#XsetIgnore(java.lang.String)" class="member-name-link">XsetIgnore</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ignoreString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">List of filenames and directory names to not include.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#XsetItems(java.lang.String)" class="member-name-link">XsetItems</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;itemString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set this to be the items in the base directory that you want to be
 included.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#execute()">execute</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="fileset">
<h3>fileset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></span>&nbsp;<span class="element-name">fileset</span></div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>MatchingTask</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MatchingTask</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setProject(org.apache.tools.ant.Project)">
<h3>setProject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProject</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Sets the project object of this component. This method is used by
 Project when a component is added to it so that the component has
 access to the functions of the project. It should not be used
 for any other purpose..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code>&nbsp;in class&nbsp;<code><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - Project in whose scope this component belongs.
                Must not be <code>null</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createInclude()">
<h3>createInclude</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createInclude</span>()</div>
<div class="block">add a name entry on the include list</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a NameEntry object to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createIncludesFile()">
<h3>createIncludesFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createIncludesFile</span>()</div>
<div class="block">add a name entry on the include files list</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an PatternFileNameEntry object to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createExclude()">
<h3>createExclude</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createExclude</span>()</div>
<div class="block">add a name entry on the exclude list</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an NameEntry object to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createExcludesFile()">
<h3>createExcludesFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</a></span>&nbsp;<span class="element-name">createExcludesFile</span>()</div>
<div class="block">add a name entry on the include files list</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an PatternFileNameEntry object to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createPatternSet()">
<h3>createPatternSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</a></span>&nbsp;<span class="element-name">createPatternSet</span>()</div>
<div class="block">add a set of patterns</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>PatternSet object to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludes(java.lang.String)">
<h3>setIncludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;includes)</span></div>
<div class="block">Sets the set of include patterns. Patterns may be separated by a comma
 or a space.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includes</code> - the string containing the include patterns</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="XsetItems(java.lang.String)">
<h3>XsetItems</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">XsetItems</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;itemString)</span></div>
<div class="block">Set this to be the items in the base directory that you want to be
 included. You can also specify "*" for the items (ie: items="*")
 and it will include all the items in the base directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>itemString</code> - the string containing the files to include.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExcludes(java.lang.String)">
<h3>setExcludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;excludes)</span></div>
<div class="block">Sets the set of exclude patterns. Patterns may be separated by a comma
 or a space.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludes</code> - the string containing the exclude patterns</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="XsetIgnore(java.lang.String)">
<h3>XsetIgnore</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">XsetIgnore</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ignoreString)</span></div>
<div class="block">List of filenames and directory names to not include. They should be
 either comma or space separated. The ignored files will be logged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ignoreString</code> - the string containing the files to ignore.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDefaultexcludes(boolean)">
<h3>setDefaultexcludes</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultexcludes</span><wbr><span class="parameters">(boolean&nbsp;useDefaultExcludes)</span></div>
<div class="block">Sets whether default exclusions should be used or not.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>useDefaultExcludes</code> - "true"|"on"|"yes" when default exclusions
                           should be used, "false"|"off"|"no" when they
                           shouldn't be used.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDirectoryScanner(java.io.File)">
<h3>getDirectoryScanner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></span>&nbsp;<span class="element-name">getDirectoryScanner</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;baseDir)</span></div>
<div class="block">Returns the directory scanner needed to access the files to process.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseDir</code> - the base directory to use with the fileset</dd>
<dt>Returns:</dt>
<dd>a directory scanner</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludesfile(java.io.File)">
<h3>setIncludesfile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludesfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;includesfile)</span></div>
<div class="block">Sets the name of the file containing the includes patterns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includesfile</code> - A string containing the filename to fetch
 the include patterns from.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExcludesfile(java.io.File)">
<h3>setExcludesfile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExcludesfile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;excludesfile)</span></div>
<div class="block">Sets the name of the file containing the includes patterns.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>excludesfile</code> - A string containing the filename to fetch
 the include patterns from.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCaseSensitive(boolean)">
<h3>setCaseSensitive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCaseSensitive</span><wbr><span class="parameters">(boolean&nbsp;isCaseSensitive)</span></div>
<div class="block">Sets case sensitivity of the file system</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>isCaseSensitive</code> - "true"|"on"|"yes" if file system is case
                           sensitive, "false"|"off"|"no" when not.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFollowSymlinks(boolean)">
<h3>setFollowSymlinks</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFollowSymlinks</span><wbr><span class="parameters">(boolean&nbsp;followSymlinks)</span></div>
<div class="block">Sets whether or not symbolic links should be followed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>followSymlinks</code> - whether or not symbolic links should be followed</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="hasSelectors()">
<h3>hasSelectors</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasSelectors</span>()</div>
<div class="block">Indicates whether there are any selectors here.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#hasSelectors()">hasSelectors</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>whether any selectors are in this container</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="selectorCount()">
<h3>selectorCount</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">selectorCount</span>()</div>
<div class="block">Gives the count of the number of selectors in this container</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#selectorCount()">selectorCount</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>the number of selectors in this container</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSelectors(org.apache.tools.ant.Project)">
<h3>getSelectors</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>[]</span>&nbsp;<span class="element-name">getSelectors</span><wbr><span class="parameters">(<a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;p)</span></div>
<div class="block">Returns the set of selectors as an array.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>p</code> - the current project</dd>
<dt>Returns:</dt>
<dd>an array of selectors in this container</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="selectorElements()">
<h3>selectorElements</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;<a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&gt;</span>&nbsp;<span class="element-name">selectorElements</span>()</div>
<div class="block">Returns an enumerator for accessing the set of selectors.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#selectorElements()">selectorElements</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Returns:</dt>
<dd>an enumerator that goes through each of the selectors</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">
<h3>appendSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">appendSelector</span><wbr><span class="parameters">(<a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</span></div>
<div class="block">Add a new selector into this container.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the new selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">
<h3>addSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSelector</span><wbr><span class="parameters">(<a href="../types/selectors/SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a>&nbsp;selector)</span></div>
<div class="block">add a "Select" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addAnd(org.apache.tools.ant.types.selectors.AndSelector)">
<h3>addAnd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addAnd</span><wbr><span class="parameters">(<a href="../types/selectors/AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</a>&nbsp;selector)</span></div>
<div class="block">add an "And" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addOr(org.apache.tools.ant.types.selectors.OrSelector)">
<h3>addOr</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addOr</span><wbr><span class="parameters">(<a href="../types/selectors/OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</a>&nbsp;selector)</span></div>
<div class="block">add an "Or" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addNot(org.apache.tools.ant.types.selectors.NotSelector)">
<h3>addNot</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNot</span><wbr><span class="parameters">(<a href="../types/selectors/NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</a>&nbsp;selector)</span></div>
<div class="block">add a "Not" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addNone(org.apache.tools.ant.types.selectors.NoneSelector)">
<h3>addNone</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addNone</span><wbr><span class="parameters">(<a href="../types/selectors/NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a>&nbsp;selector)</span></div>
<div class="block">add a "None" selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">
<h3>addMajority</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addMajority</span><wbr><span class="parameters">(<a href="../types/selectors/MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a>&nbsp;selector)</span></div>
<div class="block">add a majority selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDate(org.apache.tools.ant.types.selectors.DateSelector)">
<h3>addDate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDate</span><wbr><span class="parameters">(<a href="../types/selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</a>&nbsp;selector)</span></div>
<div class="block">add a selector date entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addSize(org.apache.tools.ant.types.selectors.SizeSelector)">
<h3>addSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addSize</span><wbr><span class="parameters">(<a href="../types/selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a>&nbsp;selector)</span></div>
<div class="block">add a selector size entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">
<h3>addFilename</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addFilename</span><wbr><span class="parameters">(<a href="../types/selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a>&nbsp;selector)</span></div>
<div class="block">add a selector filename entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">
<h3>addCustom</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addCustom</span><wbr><span class="parameters">(<a href="../types/selectors/ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a>&nbsp;selector)</span></div>
<div class="block">add an extended selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">
<h3>addContains</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContains</span><wbr><span class="parameters">(<a href="../types/selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a>&nbsp;selector)</span></div>
<div class="block">add a contains selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">
<h3>addPresent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addPresent</span><wbr><span class="parameters">(<a href="../types/selectors/PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a>&nbsp;selector)</span></div>
<div class="block">add a present selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">
<h3>addDepth</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDepth</span><wbr><span class="parameters">(<a href="../types/selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a>&nbsp;selector)</span></div>
<div class="block">add a depth selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDepend(org.apache.tools.ant.types.selectors.DependSelector)">
<h3>addDepend</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDepend</span><wbr><span class="parameters">(<a href="../types/selectors/DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</a>&nbsp;selector)</span></div>
<div class="block">add a depends selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">
<h3>addContainsRegexp</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addContainsRegexp</span><wbr><span class="parameters">(<a href="../types/selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a>&nbsp;selector)</span></div>
<div class="block">add a regular expression selector entry on the selector list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">
<h3>addDifferent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDifferent</span><wbr><span class="parameters">(<a href="../types/selectors/DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a>&nbsp;selector)</span></div>
<div class="block">add a type selector entry on the type list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addType(org.apache.tools.ant.types.selectors.TypeSelector)">
<h3>addType</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addType</span><wbr><span class="parameters">(<a href="../types/selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a>&nbsp;selector)</span></div>
<div class="block">add a type selector entry on the type list</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">
<h3>addModified</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addModified</span><wbr><span class="parameters">(<a href="../types/selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</a>&nbsp;selector)</span></div>
<div class="block">add the modified selector</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.types.selectors.FileSelector)">
<h3>add</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="../types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>&nbsp;selector)</span></div>
<div class="block">add an arbitrary selector</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../types/selectors/SelectorContainer.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a></code>&nbsp;in interface&nbsp;<code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
<dt>Parameters:</dt>
<dd><code>selector</code> - the selector to add</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getImplicitFileSet()">
<h3>getImplicitFileSet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></span>&nbsp;<span class="element-name">getImplicitFileSet</span>()</div>
<div class="block">Accessor for the implicit fileset.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the implicit fileset</dd>
<dt>Since:</dt>
<dd>Ant 1.5.2</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
