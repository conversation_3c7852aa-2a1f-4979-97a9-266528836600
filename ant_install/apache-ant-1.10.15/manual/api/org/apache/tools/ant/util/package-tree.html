<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.util Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="tree: package: org.apache.tools.ant.util">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.util</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractCollection.html" class="type-name-link external-link" title="class or interface in java.util">AbstractCollection</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;E&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractList.html" class="type-name-link external-link" title="class or interface in java.util">AbstractList</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" class="type-name-link external-link" title="class or interface in java.util">Vector</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/RandomAccess.html" title="class or interface in java.util" class="external-link">RandomAccess</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" class="type-name-link external-link" title="class or interface in java.util">Stack</a>&lt;E&gt;
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="IdentityStack.html" class="type-name-link" title="class in org.apache.tools.ant.util">IdentityStack</a>&lt;E&gt;</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="VectorSet.html" class="type-name-link" title="class in org.apache.tools.ant.util">VectorSet</a>&lt;E&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="Base64Converter.html" class="type-name-link" title="class in org.apache.tools.ant.util">Base64Converter</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" class="type-name-link external-link" title="class or interface in java.lang">ClassLoader</a>
<ul>
<li class="circle">org.apache.tools.ant.<a href="../AntClassLoader.html" class="type-name-link" title="class in org.apache.tools.ant">AntClassLoader</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, org.apache.tools.ant.<a href="../SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="SplitClassLoader.html" class="type-name-link" title="class in org.apache.tools.ant.util">SplitClassLoader</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="ClasspathUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">ClasspathUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ClasspathUtils.Delegate.html" class="type-name-link" title="class in org.apache.tools.ant.util">ClasspathUtils.Delegate</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="CollectionUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">CollectionUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="CollectionUtils.EmptyEnumeration.html" class="type-name-link" title="class in org.apache.tools.ant.util">CollectionUtils.EmptyEnumeration</a>&lt;E&gt; (implements java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Enumeration.html" title="class or interface in java.util" class="external-link">Enumeration</a>&lt;E&gt;)</li>
<li class="circle">org.apache.tools.ant.util.<a href="ContainerMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ContainerMapper</a> (implements org.apache.tools.ant.util.<a href="FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="ChainedMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ChainedMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="CompositeMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">CompositeMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="FirstMatchMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">FirstMatchMapper</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="DateUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">DateUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="DeweyDecimal.html" class="type-name-link" title="class in org.apache.tools.ant.util">DeweyDecimal</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;)</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Dictionary.html" class="type-name-link external-link" title="class or interface in java.util">Dictionary</a>&lt;K,<wbr>V&gt;
<ul>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" class="type-name-link external-link" title="class or interface in java.util">Hashtable</a>&lt;K,<wbr>V&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;K,<wbr>V&gt;, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="LazyHashtable.html" class="type-name-link" title="class in org.apache.tools.ant.util">LazyHashtable</a>&lt;K,<wbr>V&gt;</li>
<li class="circle">org.apache.tools.ant.util.<a href="LinkedHashtable.html" class="type-name-link" title="class in org.apache.tools.ant.util">LinkedHashtable</a>&lt;K,<wbr>V&gt;</li>
<li class="circle">java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Properties.html" class="type-name-link external-link" title="class or interface in java.util">Properties</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="LayoutPreservingProperties.html" class="type-name-link" title="class in org.apache.tools.ant.util">LayoutPreservingProperties</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="DOMElementWriter.html" class="type-name-link" title="class in org.apache.tools.ant.util">DOMElementWriter</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="DOMElementWriter.XmlNamespacePolicy.html" class="type-name-link" title="class in org.apache.tools.ant.util">DOMElementWriter.XmlNamespacePolicy</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="DOMUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">DOMUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="FileUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">FileUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="FlatFileNameMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">FlatFileNameMapper</a> (implements org.apache.tools.ant.util.<a href="FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="GlobPatternMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">GlobPatternMapper</a> (implements org.apache.tools.ant.util.<a href="FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="PackageNameMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">PackageNameMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="UnPackageNameMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">UnPackageNameMapper</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="IdentityMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">IdentityMapper</a> (implements org.apache.tools.ant.util.<a href="FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/InputStream.html" class="type-name-link external-link" title="class or interface in java.io">InputStream</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="ConcatFileInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">ConcatFileInputStream</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ConcatResourceInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">ConcatResourceInputStream</a></li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterInputStream.html" class="type-name-link external-link" title="class or interface in java.io">FilterInputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="KeepAliveInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">KeepAliveInputStream</a></li>
</ul>
</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/PipedInputStream.html" class="type-name-link external-link" title="class or interface in java.io">PipedInputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="LeadPipeInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LeadPipeInputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="ReaderInputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">ReaderInputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="JavaEnvUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">JavaEnvUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="JAXPUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">JAXPUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="LoaderUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">LoaderUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="MergingMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">MergingMapper</a> (implements org.apache.tools.ant.util.<a href="FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="Native2AsciiUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">Native2AsciiUtils</a></li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/OutputStream.html" class="type-name-link external-link" title="class or interface in java.io">OutputStream</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Flushable.html" title="class or interface in java.io" class="external-link">Flushable</a>)
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/ByteArrayOutputStream.html" class="type-name-link external-link" title="class or interface in java.io">ByteArrayOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="PropertyOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">PropertyOutputStream</a></li>
</ul>
</li>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/FilterOutputStream.html" class="type-name-link external-link" title="class or interface in java.io">FilterOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="KeepAliveOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">KeepAliveOutputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="LazyFileOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LazyFileOutputStream</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="LineOrientedOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineOrientedOutputStream</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="LineOrientedOutputStreamRedirector.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineOrientedOutputStreamRedirector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="NullOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">NullOutputStream</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="TeeOutputStream.html" class="type-name-link" title="class in org.apache.tools.ant.util">TeeOutputStream</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="OutputStreamFunneler.html" class="type-name-link" title="class in org.apache.tools.ant.util">OutputStreamFunneler</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="PermissionUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">PermissionUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ProcessUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">ProcessUtil</a></li>
<li class="circle">org.apache.tools.ant.<a href="../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="FileTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.util">FileTokenizer</a> (implements org.apache.tools.ant.util.<a href="Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="LineTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.util">LineTokenizer</a> (implements org.apache.tools.ant.util.<a href="Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="StringTokenizer.html" class="type-name-link" title="class in org.apache.tools.ant.util">StringTokenizer</a> (implements org.apache.tools.ant.util.<a href="Tokenizer.html" title="interface in org.apache.tools.ant.util">Tokenizer</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="XMLFragment.html" class="type-name-link" title="class in org.apache.tools.ant.util">XMLFragment</a> (implements org.apache.tools.ant.<a href="../DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="ProxySetup.html" class="type-name-link" title="class in org.apache.tools.ant.util">ProxySetup</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ReflectUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">ReflectUtil</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ReflectWrapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ReflectWrapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="RegexpPatternMapper.html" class="type-name-link" title="class in org.apache.tools.ant.util">RegexpPatternMapper</a> (implements org.apache.tools.ant.util.<a href="FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="ResourceUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">ResourceUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="RetryHandler.html" class="type-name-link" title="class in org.apache.tools.ant.util">RetryHandler</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ScriptFixBSFPath.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptFixBSFPath</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ScriptRunnerBase.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunnerBase</a>
<ul>
<li class="circle">org.apache.tools.ant.util.optional.<a href="optional/ScriptRunner.html" class="type-name-link" title="class in org.apache.tools.ant.util.optional">ScriptRunner</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="ScriptRunner.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunner</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="ScriptRunnerCreator.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunnerCreator</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ScriptRunnerHelper.html" class="type-name-link" title="class in org.apache.tools.ant.util">ScriptRunnerHelper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="SecurityManagerUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">SecurityManagerUtil</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="SourceFileScanner.html" class="type-name-link" title="class in org.apache.tools.ant.util">SourceFileScanner</a> (implements org.apache.tools.ant.types.<a href="../types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="StreamUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">StreamUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="StringUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">StringUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="SymbolicLinkUtils.html" class="type-name-link" title="class in org.apache.tools.ant.util">SymbolicLinkUtils</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="TaskLogger.html" class="type-name-link" title="class in org.apache.tools.ant.util">TaskLogger</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Thread.html" class="type-name-link external-link" title="class or interface in java.lang">Thread</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="WorkerAnt.html" class="type-name-link" title="class in org.apache.tools.ant.util">WorkerAnt</a></li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" class="type-name-link external-link" title="class or interface in java.io">IOException</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="ResourceUtils.ReadOnlyTargetFileException.html" class="type-name-link" title="class in org.apache.tools.ant.util">ResourceUtils.ReadOnlyTargetFileException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="UnicodeUtil.html" class="type-name-link" title="class in org.apache.tools.ant.util">UnicodeUtil</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="UUEncoder.html" class="type-name-link" title="class in org.apache.tools.ant.util">UUEncoder</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="Watchdog.html" class="type-name-link" title="class in org.apache.tools.ant.util">Watchdog</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Runnable.html" title="class or interface in java.lang" class="external-link">Runnable</a>)</li>
<li class="circle">org.apache.tools.ant.util.<a href="WeakishReference.html" class="type-name-link" title="class in org.apache.tools.ant.util">WeakishReference</a>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="WeakishReference.HardReference.html" class="type-name-link" title="class in org.apache.tools.ant.util">WeakishReference.HardReference</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.util.<a href="XmlConstants.html" class="type-name-link" title="class in org.apache.tools.ant.util">XmlConstants</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="XMLFragment.Child.html" class="type-name-link" title="class in org.apache.tools.ant.util">XMLFragment.Child</a> (implements org.apache.tools.ant.<a href="../DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="FileNameMapper.html" class="type-name-link" title="interface in org.apache.tools.ant.util">FileNameMapper</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ResourceUtils.ResourceSelectorProvider.html" class="type-name-link" title="interface in org.apache.tools.ant.util">ResourceUtils.ResourceSelectorProvider</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="Retryable.html" class="type-name-link" title="interface in org.apache.tools.ant.util">Retryable</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="TimeoutObserver.html" class="type-name-link" title="interface in org.apache.tools.ant.util">TimeoutObserver</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="Tokenizer.html" class="type-name-link" title="interface in org.apache.tools.ant.util">Tokenizer</a></li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">org.apache.tools.ant.util.<a href="PermissionUtils.FileType.html" class="type-name-link" title="enum class in org.apache.tools.ant.util">PermissionUtils.FileType</a></li>
<li class="circle">org.apache.tools.ant.util.<a href="ScriptManager.html" class="type-name-link" title="enum class in org.apache.tools.ant.util">ScriptManager</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</body>
</html>
