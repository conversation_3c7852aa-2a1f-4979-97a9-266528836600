<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.types.selectors Class Hierarchy (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="tree: package: org.apache.tools.ant.types.selectors">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package org.apache.tools.ant.types.selectors</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../EnumeratedAttribute.html" class="type-name-link" title="class in org.apache.tools.ant.types">EnumeratedAttribute</a>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../Comparison.html" class="type-name-link" title="class in org.apache.tools.ant.types">Comparison</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SizeSelector.SizeComparisons.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SizeSelector.SizeComparisons</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="PresentSelector.FilePresence.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PresentSelector.FilePresence</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SizeSelector.ByteUnits.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SizeSelector.ByteUnits</a></li>
<li class="circle">org.apache.tools.ant.types.<a href="../TimeComparison.html" class="type-name-link" title="class in org.apache.tools.ant.types">TimeComparison</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="DateSelector.TimeComparisons.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DateSelector.TimeComparisons</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="TypeSelector.FileType.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TypeSelector.FileType</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ExecutableSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ExecutableSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="OwnedBySelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">OwnedBySelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="PosixGroupSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PosixGroupSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="PosixPermissionsSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PosixPermissionsSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.<a href="../../ProjectComponent.html" class="type-name-link" title="class in org.apache.tools.ant">ProjectComponent</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../DataType.html" class="type-name-link" title="class in org.apache.tools.ant.types">DataType</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="AbstractSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">AbstractSelectorContainer</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a>, org.apache.tools.ant.types.selectors.<a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="BaseSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="BaseExtendSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">BaseExtendSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="ExtendFileSelector.html" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ContainsRegexpSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ContainsSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</a> (implements org.apache.tools.ant.types.resources.selectors.<a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="DateSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DateSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="DepthSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DepthSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="FilenameSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SizeSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SizeSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="TypeSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TypeSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="BaseSelectorContainer.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">BaseSelectorContainer</a> (implements org.apache.tools.ant.types.selectors.<a href="SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a>)
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="AndSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">AndSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="MajoritySelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="NoneSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">NoneSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="NotSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">NotSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="OrSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">OrSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SelectSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SelectSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ExtendSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="MappingSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">MappingSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="DependSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DependSelector</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="DifferentSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="PresentSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">PresentSelector</a></li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SignedSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SignedSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ReadableSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">ReadableSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SelectorUtils.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SelectorUtils</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SymlinkSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">SymlinkSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="TokenizedPath.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TokenizedPath</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="TokenizedPattern.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">TokenizedPattern</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="WritableSelector.html" class="type-name-link" title="class in org.apache.tools.ant.types.selectors">WritableSelector</a> (implements org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.apache.tools.ant.types.<a href="../Parameterizable.html" class="type-name-link" title="interface in org.apache.tools.ant.types">Parameterizable</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ExtendFileSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a> (also extends org.apache.tools.ant.types.selectors.<a href="FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>)</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.resources.selectors.<a href="../resources/selectors/ResourceSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="FileSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a>
<ul>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="ExtendFileSelector.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">ExtendFileSelector</a> (also extends org.apache.tools.ant.types.<a href="../Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</a>)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SelectorContainer.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></li>
<li class="circle">org.apache.tools.ant.types.selectors.<a href="SelectorScanner.html" class="type-name-link" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></li>
</ul>
</section>
</main>
</body>
</html>
