<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.ant.property (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.property">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li><a href="#package-description">Description</a></li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li><a href="#package-description">Description</a>&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.ant.property" class="title">Package org.apache.tools.ant.property</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">org.apache.tools.ant.property</span></div>
<section class="package-description" id="package-description">
<div class="block">Contains helper classes for Ant properties.</div>
</section>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">org.apache.tools.ant</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="GetProperty.html" title="interface in org.apache.tools.ant.property">GetProperty</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Interface to a class (normally PropertyHelper) to get a property.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LocalProperties.html" title="class in org.apache.tools.ant.property">LocalProperties</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Thread local class containing local properties.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LocalPropertyStack.html" title="class in org.apache.tools.ant.property">LocalPropertyStack</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A stack of local property maps.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="NullReturn.html" title="class in org.apache.tools.ant.property">NullReturn</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class to represent a null and to stop the chain of lookups.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="ParseNextProperty.html" title="interface in org.apache.tools.ant.property">ParseNextProperty</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Helper for <a href="PropertyExpander.html" title="interface in org.apache.tools.ant.property"><code>PropertyExpander</code></a> that can be
 used to expand property references to values.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ParseProperties.html" title="class in org.apache.tools.ant.property">ParseProperties</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Parse properties using a collection of expanders.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="PropertyExpander.html" title="interface in org.apache.tools.ant.property">PropertyExpander</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Responsible for locating a property reference inside a String.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ResolvePropertyMap.html" title="class in org.apache.tools.ant.property">ResolvePropertyMap</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class to resolve properties in a map.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</body>
</html>
