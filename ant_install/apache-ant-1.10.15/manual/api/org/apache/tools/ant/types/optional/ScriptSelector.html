<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>ScriptSelector (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types.optional, class: ScriptSelector">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types.optional</a></div>
<h1 title="Class ScriptSelector" class="title">Class ScriptSelector</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</a>
<div class="inheritance"><a href="../selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">org.apache.tools.ant.types.selectors.BaseSelector</a>
<div class="inheritance">org.apache.tools.ant.types.optional.ScriptSelector</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../resources/selectors/ResourceSelector.html" title="interface in org.apache.tools.ant.types.resources.selectors">ResourceSelector</a></code>, <code><a href="../selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">ScriptSelector</span>
<span class="extends-implements">extends <a href="../selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></span></div>
<div class="block">Selector that lets you run a script with selection logic inline</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant1.7</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.DataType">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checked">checked</a>, <a href="../DataType.html#ref">ref</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#description">description</a>, <a href="../../ProjectComponent.html#location">location</a>, <a href="../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">ScriptSelector</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addText(java.lang.String)" class="member-name-link">addText</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The script text.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Classpath to be used when searching for classes and resources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBasedir()" class="member-name-link">getBasedir</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the base directory</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFile()" class="member-name-link">getFile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the file that is currently to be tested</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFilename()" class="member-name-link">getFilename</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get the filename of the file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelected()" class="member-name-link">isSelected</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">get state of selected flag</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelected(java.io.File,java.lang.String,java.io.File)" class="member-name-link">isSelected</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method that each selector will implement to create their selection
 behaviour.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath to be used when searching for classes and resources.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setClasspathRef</a><wbr>(<a href="../Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath by reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the encoding of the script from an external file; optional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLanguage(java.lang.String)" class="member-name-link">setLanguage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;language)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Defines the language (required).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setManager(java.lang.String)" class="member-name-link">setManager</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;manager)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setManager(org.apache.tools.ant.util.ScriptManager)" class="member-name-link">setManager</a><wbr>(<a href="../../util/ScriptManager.html" title="enum class in org.apache.tools.ant.util">ScriptManager</a>&nbsp;manager)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the script manager.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProject(org.apache.tools.ant.Project)" class="member-name-link">setProject</a><wbr>(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the project.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSelected(boolean)" class="member-name-link">setSelected</a><wbr>(boolean&nbsp;selected)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">set the selected state
 Intended for script use, not as an Ant attribute</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSetBeans(boolean)" class="member-name-link">setSetBeans</a><wbr>(boolean&nbsp;setBeans)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the setbeans attribute.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrc(java.io.File)" class="member-name-link">setSrc</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Load the script from an external file; optional.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.BaseSelector">Methods inherited from class&nbsp;org.apache.tools.ant.types.selectors.<a href="../selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></h3>
<code><a href="../selectors/BaseSelector.html#getError()">getError</a>, <a href="../selectors/BaseSelector.html#setError(java.lang.String)">setError</a>, <a href="../selectors/BaseSelector.html#setError(java.lang.String,java.lang.Throwable)">setError</a>, <a href="../selectors/BaseSelector.html#validate()">validate</a>, <a href="../selectors/BaseSelector.html#verifySettings()">verifySettings</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.DataType">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="../DataType.html" title="class in org.apache.tools.ant.types">DataType</a></h3>
<code><a href="../DataType.html#checkAttributesAllowed()">checkAttributesAllowed</a>, <a href="../DataType.html#checkChildrenAllowed()">checkChildrenAllowed</a>, <a href="../DataType.html#circularReference()">circularReference</a>, <a href="../DataType.html#clone()">clone</a>, <a href="../DataType.html#dieOnCircularReference()">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(java.util.Stack,org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</a>, <a href="../DataType.html#getCheckedRef()">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(java.lang.Class,java.lang.String,org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</a>, <a href="../DataType.html#getDataTypeName()">getDataTypeName</a>, <a href="../DataType.html#getRefid()">getRefid</a>, <a href="../DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">invokeCircularReferenceCheck</a>, <a href="../DataType.html#isChecked()">isChecked</a>, <a href="../DataType.html#isReference()">isReference</a>, <a href="../DataType.html#noChildrenAllowed()">noChildrenAllowed</a>, <a href="../DataType.html#pushAndInvokeCircularReferenceCheck(org.apache.tools.ant.types.DataType,java.util.Stack,org.apache.tools.ant.Project)">pushAndInvokeCircularReferenceCheck</a>, <a href="../DataType.html#setChecked(boolean)">setChecked</a>, <a href="../DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</a>, <a href="../DataType.html#tooManyAttributes()">tooManyAttributes</a>, <a href="../DataType.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../ProjectComponent.html#log(java.lang.String)">log</a>, <a href="../../ProjectComponent.html#log(java.lang.String,int)">log</a>, <a href="../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.selectors.FileSelector">Methods inherited from interface&nbsp;org.apache.tools.ant.types.selectors.<a href="../selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></h3>
<code><a href="../selectors/FileSelector.html#isSelected(org.apache.tools.ant.types.Resource)">isSelected</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>ScriptSelector</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">ScriptSelector</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setProject(org.apache.tools.ant.Project)">
<h3>setProject</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProject</span><wbr><span class="parameters">(<a href="../../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project)</span></div>
<div class="block">Set the project.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</a></code>&nbsp;in class&nbsp;<code><a href="../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></code></dd>
<dt>Parameters:</dt>
<dd><code>project</code> - the owner of this component.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setManager(java.lang.String)">
<h3>setManager</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManager</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;manager)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Defines the manager.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manager</code> - the scripting manager.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setManager(org.apache.tools.ant.util.ScriptManager)">
<h3>setManager</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManager</span><wbr><span class="parameters">(<a href="../../util/ScriptManager.html" title="enum class in org.apache.tools.ant.util">ScriptManager</a>&nbsp;manager)</span></div>
<div class="block">Set the script manager.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manager</code> - </dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLanguage(java.lang.String)">
<h3>setLanguage</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLanguage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;language)</span></div>
<div class="block">Defines the language (required).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>language</code> - the scripting language name for the script.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSrc(java.io.File)">
<h3>setSrc</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrc</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Load the script from an external file; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file containing the script source.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addText(java.lang.String)">
<h3>addText</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addText</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span></div>
<div class="block">The script text.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>text</code> - a component of the script text to be added.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath to be used when searching for classes and resources.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - an Ant Path object containing the search path.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Classpath to be used when searching for classes and resources.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an empty Path instance to be configured by Ant.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setClasspathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspathRef</span><wbr><span class="parameters">(<a href="../Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Set the classpath by reference.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a Reference to a Path instance to be used as the classpath
          value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSetBeans(boolean)">
<h3>setSetBeans</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSetBeans</span><wbr><span class="parameters">(boolean&nbsp;setBeans)</span></div>
<div class="block">Set the setbeans attribute.
 If this is true, &lt;script&gt; will create variables in the
 script instance for all
 properties, targets and references of the current project.
 It this is false, only the project and self variables will
 be set.
 The default is true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>setBeans</code> - the value to set.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected(java.io.File,java.lang.String,java.io.File)">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;basedir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Method that each selector will implement to create their selection
 behaviour. If there is a problem with the setup of a selector, it can
 throw a BuildException to indicate the problem.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../selectors/FileSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in interface&nbsp;<code><a href="../selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</a></code></dd>
<dt>Specified by:</dt>
<dd><code><a href="../selectors/BaseSelector.html#isSelected(java.io.File,java.lang.String,java.io.File)">isSelected</a></code>&nbsp;in class&nbsp;<code><a href="../selectors/BaseSelector.html" title="class in org.apache.tools.ant.types.selectors">BaseSelector</a></code></dd>
<dt>Parameters:</dt>
<dd><code>basedir</code> - A java.io.File object for the base directory</dd>
<dd><code>filename</code> - The name of the file to check</dd>
<dd><code>file</code> - A File object for this filename</dd>
<dt>Returns:</dt>
<dd>whether the file should be selected or not</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getBasedir()">
<h3>getBasedir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getBasedir</span>()</div>
<div class="block">get the base directory</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the base directory</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFilename()">
<h3>getFilename</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFilename</span>()</div>
<div class="block">get the filename of the file</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the filename of the file that is currently been tested</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFile()">
<h3>getFile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getFile</span>()</div>
<div class="block">get the file that is currently to be tested</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the file that is currently been tested</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isSelected()">
<h3>isSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelected</span>()</div>
<div class="block">get state of selected flag</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the selected flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSelected(boolean)">
<h3>setSelected</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSelected</span><wbr><span class="parameters">(boolean&nbsp;selected)</span></div>
<div class="block">set the selected state
 Intended for script use, not as an Ant attribute</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>selected</code> - the selected state</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Set the encoding of the script from an external file; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - the encoding of the file containing the script source.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
