<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>WebsphereDeploymentTool (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: WebsphereDeploymentTool">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class WebsphereDeploymentTool" class="title">Class WebsphereDeploymentTool</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">WebsphereDeploymentTool</span>
<span class="extends-implements">extends <a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></span></div>
<div class="block">WebSphere deployment tool that augments the ejbjar task.
 Searches for the WebSphere specific deployment descriptors and
 adds them to the final ejb jar file. WebSphere has two specific descriptors for session
 beans:
 <ul>
    <li>ibm-ejb-jar-bnd.xmi</li>
    <li>ibm-ejb-jar-ext.xmi</li>
 </ul>
 and another two for container managed entity beans:
 <ul>
    <li>Map.mapxmi</li>
    <li>Schema.dbxmi</li>
 </ul>
 In terms of WebSphere, the generation of container code and stubs is
 called <code>deployment</code>. This step can be performed by the websphere
 element as part of the jar generation process. If the switch
 <code>ejbdeploy</code> is on, the ejbdeploy tool from the WebSphere toolset
 is called for every ejb-jar. Unfortunately, this step only works, if you
 use the ibm jdk. Otherwise, the rmic (called by ejbdeploy) throws a
 ClassFormatError. Be sure to switch ejbdeploy off, if run ant with
 sun jdk.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#PUBLICID_EJB11" class="member-name-link">PUBLICID_EJB11</a></code></div>
<div class="col-last even-row-color">
<div class="block">ID for ejb 1.1</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PUBLICID_EJB20" class="member-name-link">PUBLICID_EJB20</a></code></div>
<div class="col-last odd-row-color">
<div class="block">ID for ejb 2.0</div>
</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#SCHEMA_DIR" class="member-name-link">SCHEMA_DIR</a></code></div>
<div class="col-last even-row-color">
<div class="block">Schema directory</div>
</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#WAS_BND" class="member-name-link">WAS_BND</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#WAS_CMP_MAP" class="member-name-link">WAS_CMP_MAP</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#WAS_CMP_SCHEMA" class="member-name-link">WAS_CMP_SCHEMA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#WAS_EXT" class="member-name-link">WAS_EXT</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</a>, <a href="GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</a>, <a href="GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</a>, <a href="GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</a>, <a href="GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</a>, <a href="GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</a>, <a href="GenericDeploymentTool.html#EJB_DD">EJB_DD</a>, <a href="GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</a>, <a href="GenericDeploymentTool.html#MANIFEST">MANIFEST</a>, <a href="GenericDeploymentTool.html#META_DIR">META_DIR</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">WebsphereDeploymentTool</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addVendorFiles(java.util.Hashtable,java.lang.String)" class="member-name-link">addVendorFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add any vendor specific files which should be included in the EJB Jar.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWASClasspath()" class="member-name-link">createWASClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the classpath to the WebSphere classpaths.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassLoaderFromJar(java.io.File)" class="member-name-link">getClassLoaderFromJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;classjar)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Helper method invoked by isRebuildRequired to get a ClassLoader for a
 Jar File passed to it.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDescriptorHandler(java.io.File)" class="member-name-link">getDescriptorHandler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a descriptionHandler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOptions()" class="member-name-link">getOptions</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the options for the EJB Deploy operation</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWebsphereDescriptorHandler(java.io.File)" class="member-name-link">getWebsphereDescriptorHandler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a description handler.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isRebuildRequired(java.io.File,java.io.File)" class="member-name-link">isRebuildRequired</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;genericJarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;websphereJarFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Helper method to check to see if a WebSphere EJB 1.1 jar needs to be
 rebuilt using ejbdeploy.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCodegen(boolean)" class="member-name-link">setCodegen</a><wbr>(boolean&nbsp;codegen)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag, default false, to only generate the deployment
 code, do not run RMIC or Javac</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDbname(java.lang.String)" class="member-name-link">setDbname</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dbName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of the Database to create; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDbschema(java.lang.String)" class="member-name-link">setDbschema</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dbSchema)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of the schema to create; optional.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDbvendor(java.lang.String)" class="member-name-link">setDbvendor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dbvendor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the DB Vendor for the Entity Bean mapping; optional.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEjbdeploy(boolean)" class="member-name-link">setEjbdeploy</a><wbr>(boolean&nbsp;ejbdeploy)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decide, whether ejbdeploy should be called or not;
 optional, default true.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEJBdtd(java.lang.String)" class="member-name-link">setEJBdtd</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Setter used to store the location of the Sun's Generic EJB DTD.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setKeepgeneric(boolean)" class="member-name-link">setKeepgeneric</a><wbr>(boolean&nbsp;inValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This controls whether the generic file used as input to
 ejbdeploy is retained; optional, default false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNewCMP(boolean)" class="member-name-link">setNewCMP</a><wbr>(boolean&nbsp;newCMP)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the value of the newCMP scheme.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNoinform(boolean)" class="member-name-link">setNoinform</a><wbr>(boolean&nbsp;noinform)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to disable informational messages; optional, default false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNovalidate(boolean)" class="member-name-link">setNovalidate</a><wbr>(boolean&nbsp;novalidate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to disable the validation steps; optional, default false.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNowarn(boolean)" class="member-name-link">setNowarn</a><wbr>(boolean&nbsp;nowarn)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to disable warning and informational messages; optional, default false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOldCMP(boolean)" class="member-name-link">setOldCMP</a><wbr>(boolean&nbsp;oldCMP)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the value of the oldCMP scheme.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setQuiet(boolean)" class="member-name-link">setQuiet</a><wbr>(boolean&nbsp;quiet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag, default true, to only output error messages.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRebuild(boolean)" class="member-name-link">setRebuild</a><wbr>(boolean&nbsp;rebuild)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the rebuild flag to false to only update changes in the jar rather
 than rerunning ejbdeploy; optional, default true.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRmicoptions(java.lang.String)" class="member-name-link">setRmicoptions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;options)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the rmic options.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSuffix(java.lang.String)" class="member-name-link">setSuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">String value appended to the basename of the deployment
 descriptor to create the filename of the WebLogic EJB
 jar file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTempdir(java.lang.String)" class="member-name-link">setTempdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tempdir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The directory, where ejbdeploy will write temporary files;
 optional, defaults to '_ejbdeploy_temp'.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTrace(boolean)" class="member-name-link">setTrace</a><wbr>(boolean&nbsp;trace)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to enable internal tracing when set, optional, default false.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUse35(boolean)" class="member-name-link">setUse35</a><wbr>(boolean&nbsp;attr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Flag to use the WebSphere 3.5 compatible mapping rules; optional, default false.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setWASClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setWASClasspath</a><wbr>(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;wasClasspath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the WebSphere classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validateConfigured()" class="member-name-link">validateConfigured</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Called to validate that the tool parameters have been configured.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)" class="member-name-link">writeJar</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method used to encapsulate the writing of the JAR file.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.optional.ejb.<a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></h3>
<code><a href="GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream,java.io.File,java.lang.String)">addFileToJar</a>, <a href="GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</a>, <a href="GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</a>, <a href="GenericDeploymentTool.html#checkConfiguration(java.lang.String,javax.xml.parsers.SAXParser)">checkConfiguration</a>, <a href="GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</a>, <a href="GenericDeploymentTool.html#createClasspath()">createClasspath</a>, <a href="GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</a>, <a href="GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</a>, <a href="GenericDeploymentTool.html#getConfig()">getConfig</a>, <a href="GenericDeploymentTool.html#getDestDir()">getDestDir</a>, <a href="GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</a>, <a href="GenericDeploymentTool.html#getLocation()">getLocation</a>, <a href="GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</a>, <a href="GenericDeploymentTool.html#getPublicId()">getPublicId</a>, <a href="GenericDeploymentTool.html#getTask()">getTask</a>, <a href="GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String,java.lang.String)">getVendorDDPrefix</a>, <a href="GenericDeploymentTool.html#log(java.lang.String,int)">log</a>, <a href="GenericDeploymentTool.html#needToRebuild(java.util.Hashtable,java.io.File)">needToRebuild</a>, <a href="GenericDeploymentTool.html#parseEjbFiles(java.lang.String,javax.xml.parsers.SAXParser)">parseEjbFiles</a>, <a href="GenericDeploymentTool.html#processDescriptor(java.lang.String,javax.xml.parsers.SAXParser)">processDescriptor</a>, <a href="GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</a>, <a href="GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</a>, <a href="GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</a>, <a href="GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</a>, <a href="GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</a>, <a href="GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="PUBLICID_EJB11">
<h3>PUBLICID_EJB11</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_EJB11</span></div>
<div class="block">ID for ejb 1.1</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.PUBLICID_EJB11">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PUBLICID_EJB20">
<h3>PUBLICID_EJB20</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PUBLICID_EJB20</span></div>
<div class="block">ID for ejb 2.0</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.PUBLICID_EJB20">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="SCHEMA_DIR">
<h3>SCHEMA_DIR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">SCHEMA_DIR</span></div>
<div class="block">Schema directory</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.SCHEMA_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WAS_EXT">
<h3>WAS_EXT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WAS_EXT</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_EXT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WAS_BND">
<h3>WAS_BND</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WAS_BND</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_BND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WAS_CMP_MAP">
<h3>WAS_CMP_MAP</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WAS_CMP_MAP</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_CMP_MAP">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="WAS_CMP_SCHEMA">
<h3>WAS_CMP_SCHEMA</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">WAS_CMP_SCHEMA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_CMP_SCHEMA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>WebsphereDeploymentTool</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">WebsphereDeploymentTool</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createWASClasspath()">
<h3>createWASClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createWASClasspath</span>()</div>
<div class="block">Get the classpath to the WebSphere classpaths.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the WebSphere classpath.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setWASClasspath(org.apache.tools.ant.types.Path)">
<h3>setWASClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setWASClasspath</span><wbr><span class="parameters">(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;wasClasspath)</span></div>
<div class="block">Set the WebSphere classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>wasClasspath</code> - the WebSphere classpath.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDbvendor(java.lang.String)">
<h3>setDbvendor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDbvendor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dbvendor)</span></div>
<div class="block">Sets the DB Vendor for the Entity Bean mapping; optional.
 <p>
 Valid options can be obtained by running the following command:
 <code>
 &lt;WAS_HOME&gt;/bin/EJBDeploy.[sh/bat] -help
 </code>
 </p>
 <p>
 This is also used to determine the name of the Map.mapxmi and
 Schema.dbxmi files, for example Account-DB2UDB_V81-Map.mapxmi
 and Account-DB2UDB_V81-Schema.dbxmi.
 </p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dbvendor</code> - database vendor type</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDbname(java.lang.String)">
<h3>setDbname</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDbname</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dbName)</span></div>
<div class="block">Sets the name of the Database to create; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dbName</code> - name of the database</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDbschema(java.lang.String)">
<h3>setDbschema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDbschema</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dbSchema)</span></div>
<div class="block">Sets the name of the schema to create; optional.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dbSchema</code> - name of the schema</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCodegen(boolean)">
<h3>setCodegen</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCodegen</span><wbr><span class="parameters">(boolean&nbsp;codegen)</span></div>
<div class="block">Flag, default false, to only generate the deployment
 code, do not run RMIC or Javac</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>codegen</code> - option</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setQuiet(boolean)">
<h3>setQuiet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setQuiet</span><wbr><span class="parameters">(boolean&nbsp;quiet)</span></div>
<div class="block">Flag, default true, to only output error messages.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>quiet</code> - option</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNovalidate(boolean)">
<h3>setNovalidate</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNovalidate</span><wbr><span class="parameters">(boolean&nbsp;novalidate)</span></div>
<div class="block">Flag to disable the validation steps; optional, default false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>novalidate</code> - option</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNowarn(boolean)">
<h3>setNowarn</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNowarn</span><wbr><span class="parameters">(boolean&nbsp;nowarn)</span></div>
<div class="block">Flag to disable warning and informational messages; optional, default false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nowarn</code> - option</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNoinform(boolean)">
<h3>setNoinform</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNoinform</span><wbr><span class="parameters">(boolean&nbsp;noinform)</span></div>
<div class="block">Flag to disable informational messages; optional, default false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>noinform</code> - if true disables informational messages</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTrace(boolean)">
<h3>setTrace</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTrace</span><wbr><span class="parameters">(boolean&nbsp;trace)</span></div>
<div class="block">Flag to enable internal tracing when set, optional, default false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>trace</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRmicoptions(java.lang.String)">
<h3>setRmicoptions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRmicoptions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;options)</span></div>
<div class="block">Set the rmic options.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>options</code> - the options to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUse35(boolean)">
<h3>setUse35</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUse35</span><wbr><span class="parameters">(boolean&nbsp;attr)</span></div>
<div class="block">Flag to use the WebSphere 3.5 compatible mapping rules; optional, default false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attr</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRebuild(boolean)">
<h3>setRebuild</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRebuild</span><wbr><span class="parameters">(boolean&nbsp;rebuild)</span></div>
<div class="block">Set the rebuild flag to false to only update changes in the jar rather
 than rerunning ejbdeploy; optional, default true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rebuild</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSuffix(java.lang.String)">
<h3>setSuffix</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">String value appended to the basename of the deployment
 descriptor to create the filename of the WebLogic EJB
 jar file. Optional, default '.jar'.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the suffix.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setKeepgeneric(boolean)">
<h3>setKeepgeneric</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setKeepgeneric</span><wbr><span class="parameters">(boolean&nbsp;inValue)</span></div>
<div class="block">This controls whether the generic file used as input to
 ejbdeploy is retained; optional, default false.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - either 'true' or 'false'.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEjbdeploy(boolean)">
<h3>setEjbdeploy</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEjbdeploy</span><wbr><span class="parameters">(boolean&nbsp;ejbdeploy)</span></div>
<div class="block">Decide, whether ejbdeploy should be called or not;
 optional, default true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ejbdeploy</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEJBdtd(java.lang.String)">
<h3>setEJBdtd</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEJBdtd</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Setter used to store the location of the Sun's Generic EJB DTD. This
 can be a file on the system or a resource on the classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the DTD location.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOldCMP(boolean)">
<h3>setOldCMP</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOldCMP</span><wbr><span class="parameters">(boolean&nbsp;oldCMP)</span></div>
<div class="block">Set the value of the oldCMP scheme. This is an antonym for newCMP</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>oldCMP</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNewCMP(boolean)">
<h3>setNewCMP</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNewCMP</span><wbr><span class="parameters">(boolean&nbsp;newCMP)</span></div>
<div class="block">Set the value of the newCMP scheme. The old CMP scheme locates the
 WebSphere CMP descriptor based on the naming convention where the
 WebSphere CMP file is expected to be named with the bean name as the
 prefix. Under this scheme the name of the CMP descriptor does not match
 the name actually used in the main WebSphere EJB descriptor. Also,
 descriptors which contain multiple CMP references could not be used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newCMP</code> - a <code>boolean</code> value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTempdir(java.lang.String)">
<h3>setTempdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTempdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tempdir)</span></div>
<div class="block">The directory, where ejbdeploy will write temporary files;
 optional, defaults to '_ejbdeploy_temp'.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tempdir</code> - the directory name to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDescriptorHandler(java.io.File)">
<h3>getDescriptorHandler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></span>&nbsp;<span class="element-name">getDescriptorHandler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Get a descriptionHandler..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directory.</dd>
<dt>Returns:</dt>
<dd>a handler.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getWebsphereDescriptorHandler(java.io.File)">
<h3>getWebsphereDescriptorHandler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</a></span>&nbsp;<span class="element-name">getWebsphereDescriptorHandler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir)</span></div>
<div class="block">Get a description handler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directory.</dd>
<dt>Returns:</dt>
<dd>the handler.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="addVendorFiles(java.util.Hashtable,java.lang.String)">
<h3>addVendorFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addVendorFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;ejbFiles,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName)</span></div>
<div class="block">Add any vendor specific files which should be included in the EJB Jar.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable,java.lang.String)">addVendorFiles</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>ejbFiles</code> - a hashtable entryname -&gt; file.</dd>
<dd><code>baseName</code> - a prefix to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getOptions()">
<h3>getOptions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOptions</span>()</div>
<div class="block">Gets the options for the EJB Deploy operation</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>String</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">
<h3>writeJar</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">writeJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;jarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Hashtable.html" title="class or interface in java.util" class="external-link">Hashtable</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&gt;&nbsp;files,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;publicId)</span>
                 throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Method used to encapsulate the writing of the JAR file. Iterates over the
 filenames/java.io.Files in the Hashtable stored on the instance variable
 ejbFiles..</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#writeJar(java.lang.String,java.io.File,java.util.Hashtable,java.lang.String)">writeJar</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Parameters:</dt>
<dd><code>baseName</code> - the base name to use.</dd>
<dd><code>jarFile</code> - the jar file to write to.</dd>
<dd><code>files</code> - the files to write to the jar.</dd>
<dd><code>publicId</code> - the id to use.</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is a problem.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="validateConfigured()">
<h3>validateConfigured</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">validateConfigured</span>()
                        throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Called to validate that the tool parameters have been configured.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="EJBDeploymentTool.html#validateConfigured()">validateConfigured</a></code>&nbsp;in interface&nbsp;<code><a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="GenericDeploymentTool.html#validateConfigured()">validateConfigured</a></code>&nbsp;in class&nbsp;<code><a href="GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if there is an error.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isRebuildRequired(java.io.File,java.io.File)">
<h3>isRebuildRequired</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isRebuildRequired</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;genericJarFile,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;websphereJarFile)</span></div>
<div class="block">Helper method to check to see if a WebSphere EJB 1.1 jar needs to be
 rebuilt using ejbdeploy. Called from writeJar it sees if the "Bean"
 classes are the only thing that needs to be updated and either updates
 the Jar with the Bean classfile or returns true, saying that the whole
 WebSphere jar needs to be regenerated with ejbdeploy. This allows faster
 build times for working developers. <p>

 The way WebSphere ejbdeploy works is it creates wrappers for the
 publicly defined methods as they are exposed in the remote interface.
 If the actual bean changes without changing the the method signatures
 then only the bean classfile needs to be updated and the rest of the
 WebSphere jar file can remain the same. If the Interfaces, ie. the
 method signatures change or if the xml deployment descriptors changed,
 the whole jar needs to be rebuilt with ejbdeploy. This is not strictly
 true for the xml files. If the JNDI name changes then the jar doesn't
 have to be rebuild, but if the resources references change then it
 does. At this point the WebSphere jar gets rebuilt if the xml files
 change at all.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>genericJarFile</code> - java.io.File The generic jar file.</dd>
<dd><code>websphereJarFile</code> - java.io.File The WebSphere jar file to check to
      see if it needs to be rebuilt.</dd>
<dt>Returns:</dt>
<dd>true if a rebuild is required.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getClassLoaderFromJar(java.io.File)">
<h3>getClassLoaderFromJar</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></span>&nbsp;<span class="element-name">getClassLoaderFromJar</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;classjar)</span>
                                     throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Helper method invoked by isRebuildRequired to get a ClassLoader for a
 Jar File passed to it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classjar</code> - java.io.File representing jar file to get classes from.</dd>
<dt>Returns:</dt>
<dd>a classloader for the jar file.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code> - if there is an error.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
