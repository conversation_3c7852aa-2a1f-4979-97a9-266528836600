<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>IdentityStack (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.util, class: IdentityStack">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.util</a></div>
<h1 title="Class IdentityStack" class="title">Class IdentityStack&lt;E&gt;</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractCollection.html" title="class or interface in java.util" class="external-link">java.util.AbstractCollection</a>&lt;E&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractList.html" title="class or interface in java.util" class="external-link">java.util.AbstractList</a>&lt;E&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">java.util.Vector</a>&lt;E&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">java.util.Stack</a>&lt;E&gt;
<div class="inheritance">org.apache.tools.ant.util.IdentityStack&lt;E&gt;</div>
</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;E&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;E&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;E&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/RandomAccess.html" title="class or interface in java.util" class="external-link">RandomAccess</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/SequencedCollection.html" title="class or interface in java.util" class="external-link">SequencedCollection</a>&lt;E&gt;</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">IdentityStack&lt;E&gt;</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;E&gt;</span></div>
<div class="block">Identity Stack.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.7</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../serialized-form.html#org.apache.tools.ant.util.IdentityStack">Serialized Form</a></li>
</ul>
</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.Vector">Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#capacityIncrement" title="class or interface in java.util" class="external-link">capacityIncrement</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#elementCount" title="class or interface in java.util" class="external-link">elementCount</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#elementData" title="class or interface in java.util" class="external-link">elementData</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.AbstractList">Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractList.html" title="class or interface in java.util" class="external-link">AbstractList</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/AbstractList.html#modCount" title="class or interface in java.util" class="external-link">modCount</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">IdentityStack</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Default constructor.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(E)" class="member-name-link">IdentityStack</a><wbr>(<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&nbsp;o)</code></div>
<div class="col-last odd-row-color">
<div class="block">Construct a new IdentityStack with the specified Object
 as the bottom element.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#contains(java.lang.Object)" class="member-name-link">contains</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override methods that use <code>.equals()</code> comparisons on elements.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#containsAll(java.util.Collection)" class="member-name-link">containsAll</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;?&gt;&nbsp;c)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;E&gt;&nbsp;<a href="IdentityStack.html" title="class in org.apache.tools.ant.util">IdentityStack</a><wbr>&lt;E&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInstance(java.util.Stack)" class="member-name-link">getInstance</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;E&gt;&nbsp;s)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get an IdentityStack containing the contents of the specified Stack.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#indexOf(java.lang.Object,int)" class="member-name-link">indexOf</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o,
 int&nbsp;pos)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override methods that use <code>.equals()</code> comparisons on elements.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#lastIndexOf(java.lang.Object,int)" class="member-name-link">lastIndexOf</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o,
 int&nbsp;pos)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Override methods that use <code>.equals()</code> comparisons on elements.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeAll(java.util.Collection)" class="member-name-link">removeAll</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;?&gt;&nbsp;c)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#retainAll(java.util.Collection)" class="member-name-link">retainAll</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;?&gt;&nbsp;c)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Stack">Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html#empty()" title="class or interface in java.util" class="external-link">empty</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html#peek()" title="class or interface in java.util" class="external-link">peek</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html#pop()" title="class or interface in java.util" class="external-link">pop</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html#push(E)" title="class or interface in java.util" class="external-link">push</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html#search(java.lang.Object)" title="class or interface in java.util" class="external-link">search</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Vector">Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#add(int,E)" title="class or interface in java.util" class="external-link">add</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#add(E)" title="class or interface in java.util" class="external-link">add</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#addAll(int,java.util.Collection)" title="class or interface in java.util" class="external-link">addAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#addAll(java.util.Collection)" title="class or interface in java.util" class="external-link">addAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#addElement(E)" title="class or interface in java.util" class="external-link">addElement</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#capacity()" title="class or interface in java.util" class="external-link">capacity</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#clear()" title="class or interface in java.util" class="external-link">clear</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#clone()" title="class or interface in java.util" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#copyInto(java.lang.Object%5B%5D)" title="class or interface in java.util" class="external-link">copyInto</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#elementAt(int)" title="class or interface in java.util" class="external-link">elementAt</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#elements()" title="class or interface in java.util" class="external-link">elements</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#ensureCapacity(int)" title="class or interface in java.util" class="external-link">ensureCapacity</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#equals(java.lang.Object)" title="class or interface in java.util" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#firstElement()" title="class or interface in java.util" class="external-link">firstElement</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#forEach(java.util.function.Consumer)" title="class or interface in java.util" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#get(int)" title="class or interface in java.util" class="external-link">get</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#hashCode()" title="class or interface in java.util" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#indexOf(java.lang.Object)" title="class or interface in java.util" class="external-link">indexOf</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#insertElementAt(E,int)" title="class or interface in java.util" class="external-link">insertElementAt</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#isEmpty()" title="class or interface in java.util" class="external-link">isEmpty</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#iterator()" title="class or interface in java.util" class="external-link">iterator</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#lastElement()" title="class or interface in java.util" class="external-link">lastElement</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#lastIndexOf(java.lang.Object)" title="class or interface in java.util" class="external-link">lastIndexOf</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#listIterator()" title="class or interface in java.util" class="external-link">listIterator</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#listIterator(int)" title="class or interface in java.util" class="external-link">listIterator</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#remove(int)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#remove(java.lang.Object)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#removeAllElements()" title="class or interface in java.util" class="external-link">removeAllElements</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#removeElement(java.lang.Object)" title="class or interface in java.util" class="external-link">removeElement</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#removeElementAt(int)" title="class or interface in java.util" class="external-link">removeElementAt</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#removeIf(java.util.function.Predicate)" title="class or interface in java.util" class="external-link">removeIf</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#removeRange(int,int)" title="class or interface in java.util" class="external-link">removeRange</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#replaceAll(java.util.function.UnaryOperator)" title="class or interface in java.util" class="external-link">replaceAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#set(int,E)" title="class or interface in java.util" class="external-link">set</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#setElementAt(E,int)" title="class or interface in java.util" class="external-link">setElementAt</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#setSize(int)" title="class or interface in java.util" class="external-link">setSize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#size()" title="class or interface in java.util" class="external-link">size</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#sort(java.util.Comparator)" title="class or interface in java.util" class="external-link">sort</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#spliterator()" title="class or interface in java.util" class="external-link">spliterator</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#subList(int,int)" title="class or interface in java.util" class="external-link">subList</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#toArray()" title="class or interface in java.util" class="external-link">toArray</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#toArray(T%5B%5D)" title="class or interface in java.util" class="external-link">toArray</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#toString()" title="class or interface in java.util" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#trimToSize()" title="class or interface in java.util" class="external-link">trimToSize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Collection">Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#parallelStream()" title="class or interface in java.util" class="external-link">parallelStream</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#stream()" title="class or interface in java.util" class="external-link">stream</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#toArray(java.util.function.IntFunction)" title="class or interface in java.util" class="external-link">toArray</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.List">Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#addFirst(E)" title="class or interface in java.util" class="external-link">addFirst</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#addLast(E)" title="class or interface in java.util" class="external-link">addLast</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#getFirst()" title="class or interface in java.util" class="external-link">getFirst</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#getLast()" title="class or interface in java.util" class="external-link">getLast</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#removeFirst()" title="class or interface in java.util" class="external-link">removeFirst</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#removeLast()" title="class or interface in java.util" class="external-link">removeLast</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#reversed()" title="class or interface in java.util" class="external-link">reversed</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>IdentityStack</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">IdentityStack</span>()</div>
<div class="block">Default constructor.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(E)">
<h3 id="&lt;init&gt;(java.lang.Object)">IdentityStack</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">IdentityStack</span><wbr><span class="parameters">(<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&nbsp;o)</span></div>
<div class="block">Construct a new IdentityStack with the specified Object
 as the bottom element.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>o</code> - the bottom element.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getInstance(java.util.Stack)">
<h3>getInstance</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;E&gt;</span>&nbsp;<span class="return-type"><a href="IdentityStack.html" title="class in org.apache.tools.ant.util">IdentityStack</a>&lt;E&gt;</span>&nbsp;<span class="element-name">getInstance</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Stack.html" title="class or interface in java.util" class="external-link">Stack</a>&lt;E&gt;&nbsp;s)</span></div>
<div class="block">Get an IdentityStack containing the contents of the specified Stack.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>E</code> - desired type</dd>
<dt>Parameters:</dt>
<dd><code>s</code> - the Stack to copy; ignored if null.</dd>
<dt>Returns:</dt>
<dd>an IdentityStack instance.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="contains(java.lang.Object)">
<h3>contains</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">contains</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o)</span></div>
<div class="block">Override methods that use <code>.equals()</code> comparisons on elements.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#contains(java.lang.Object)" title="class or interface in java.util" class="external-link">contains</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#contains(java.lang.Object)" title="class or interface in java.util" class="external-link">contains</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#contains(java.lang.Object)" title="class or interface in java.util" class="external-link">contains</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>o</code> - the Object to search for.</dd>
<dt>Returns:</dt>
<dd>true if the stack contains the object.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#contains(java.lang.Object)" title="class or interface in java.util" class="external-link"><code>Vector.contains(Object)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="indexOf(java.lang.Object,int)">
<h3>indexOf</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">indexOf</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o,
 int&nbsp;pos)</span></div>
<div class="block">Override methods that use <code>.equals()</code> comparisons on elements.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#indexOf(java.lang.Object,int)" title="class or interface in java.util" class="external-link">indexOf</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>o</code> - the Object to search for.</dd>
<dd><code>pos</code> - the position from which to search.</dd>
<dt>Returns:</dt>
<dd>the position of the object, -1 if not found.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#indexOf(java.lang.Object,int)" title="class or interface in java.util" class="external-link"><code>Vector.indexOf(Object, int)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="lastIndexOf(java.lang.Object,int)">
<h3>lastIndexOf</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">lastIndexOf</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;o,
 int&nbsp;pos)</span></div>
<div class="block">Override methods that use <code>.equals()</code> comparisons on elements.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#lastIndexOf(java.lang.Object,int)" title="class or interface in java.util" class="external-link">lastIndexOf</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Parameters:</dt>
<dd><code>o</code> - the Object to search for.</dd>
<dd><code>pos</code> - the position from which to search (backward).</dd>
<dt>Returns:</dt>
<dd>the position of the object, -1 if not found.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#indexOf(java.lang.Object,int)" title="class or interface in java.util" class="external-link"><code>Vector.indexOf(Object, int)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="removeAll(java.util.Collection)">
<h3>removeAll</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">removeAll</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;?&gt;&nbsp;c)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#removeAll(java.util.Collection)" title="class or interface in java.util" class="external-link">removeAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#removeAll(java.util.Collection)" title="class or interface in java.util" class="external-link">removeAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#removeAll(java.util.Collection)" title="class or interface in java.util" class="external-link">removeAll</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="retainAll(java.util.Collection)">
<h3>retainAll</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">retainAll</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;?&gt;&nbsp;c)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#retainAll(java.util.Collection)" title="class or interface in java.util" class="external-link">retainAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#retainAll(java.util.Collection)" title="class or interface in java.util" class="external-link">retainAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#retainAll(java.util.Collection)" title="class or interface in java.util" class="external-link">retainAll</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="containsAll(java.util.Collection)">
<h3>containsAll</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">containsAll</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;?&gt;&nbsp;c)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html#containsAll(java.util.Collection)" title="class or interface in java.util" class="external-link">containsAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html#containsAll(java.util.Collection)" title="class or interface in java.util" class="external-link">containsAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html#containsAll(java.util.Collection)" title="class or interface in java.util" class="external-link">containsAll</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Vector.html" title="class or interface in java.util" class="external-link">Vector</a>&lt;<a href="IdentityStack.html" title="type parameter in IdentityStack">E</a>&gt;</code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
