<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>SOSCmd (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.sos, interface: SOSCmd">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li>Constr</li>
<li>Method</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.sos</a></div>
<h1 title="Interface SOSCmd" class="title">Interface SOSCmd</h1>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</a></code>, <code><a href="SOSCheckin.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSCheckin</a></code>, <code><a href="SOSCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSCheckout</a></code>, <code><a href="SOSGet.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSGet</a></code>, <code><a href="SOSLabel.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOSLabel</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">SOSCmd</span></div>
<div class="block">Interface to hold constants used by the SOS tasks</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMMAND_CHECKIN_FILE" class="member-name-link">COMMAND_CHECKIN_FILE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The checkin file command</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COMMAND_CHECKIN_PROJECT" class="member-name-link">COMMAND_CHECKIN_PROJECT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The checkin project command</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMMAND_CHECKOUT_FILE" class="member-name-link">COMMAND_CHECKOUT_FILE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The checkout file command</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COMMAND_CHECKOUT_PROJECT" class="member-name-link">COMMAND_CHECKOUT_PROJECT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The checkout project command</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMMAND_GET_FILE" class="member-name-link">COMMAND_GET_FILE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The get file command</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COMMAND_GET_PROJECT" class="member-name-link">COMMAND_GET_PROJECT</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The get project command</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMMAND_HISTORY" class="member-name-link">COMMAND_HISTORY</a></code></div>
<div class="col-last even-row-color">
<div class="block">The get history command</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#COMMAND_LABEL" class="member-name-link">COMMAND_LABEL</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The add label command</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COMMAND_SOS_EXE" class="member-name-link">COMMAND_SOS_EXE</a></code></div>
<div class="col-last even-row-color">
<div class="block">The sos executable</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_COMMAND" class="member-name-link">FLAG_COMMAND</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The command option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_COMMENT" class="member-name-link">FLAG_COMMENT</a></code></div>
<div class="col-last even-row-color">
<div class="block">The log option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_FILE" class="member-name-link">FLAG_FILE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The file option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_LABEL" class="member-name-link">FLAG_LABEL</a></code></div>
<div class="col-last even-row-color">
<div class="block">The label option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_NO_CACHE" class="member-name-link">FLAG_NO_CACHE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The no cache option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_NO_COMPRESSION" class="member-name-link">FLAG_NO_COMPRESSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">The no compression option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_PASSWORD" class="member-name-link">FLAG_PASSWORD</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The password option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_PROJECT" class="member-name-link">FLAG_PROJECT</a></code></div>
<div class="col-last even-row-color">
<div class="block">The project option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_RECURSION" class="member-name-link">FLAG_RECURSION</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The recursive option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_SOS_HOME" class="member-name-link">FLAG_SOS_HOME</a></code></div>
<div class="col-last even-row-color">
<div class="block">The sos home option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_SOS_SERVER" class="member-name-link">FLAG_SOS_SERVER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The server option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_USERNAME" class="member-name-link">FLAG_USERNAME</a></code></div>
<div class="col-last even-row-color">
<div class="block">The username option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_VERBOSE" class="member-name-link">FLAG_VERBOSE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The verbose option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_VERSION" class="member-name-link">FLAG_VERSION</a></code></div>
<div class="col-last even-row-color">
<div class="block">The revision option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#FLAG_VSS_SERVER" class="member-name-link">FLAG_VSS_SERVER</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The database (vss server) option</div>
</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#FLAG_WORKING_DIR" class="member-name-link">FLAG_WORKING_DIR</a></code></div>
<div class="col-last even-row-color">
<div class="block">The workdir option</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#PROJECT_PREFIX" class="member-name-link">PROJECT_PREFIX</a></code></div>
<div class="col-last odd-row-color">
<div class="block">The project prefix</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="COMMAND_SOS_EXE">
<h3>COMMAND_SOS_EXE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_SOS_EXE</span></div>
<div class="block">The sos executable</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_SOS_EXE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_GET_FILE">
<h3>COMMAND_GET_FILE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_GET_FILE</span></div>
<div class="block">The get file command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_GET_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_GET_PROJECT">
<h3>COMMAND_GET_PROJECT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_GET_PROJECT</span></div>
<div class="block">The get project command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_GET_PROJECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_CHECKOUT_FILE">
<h3>COMMAND_CHECKOUT_FILE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_CHECKOUT_FILE</span></div>
<div class="block">The checkout file command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKOUT_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_CHECKOUT_PROJECT">
<h3>COMMAND_CHECKOUT_PROJECT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_CHECKOUT_PROJECT</span></div>
<div class="block">The checkout project command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKOUT_PROJECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_CHECKIN_FILE">
<h3>COMMAND_CHECKIN_FILE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_CHECKIN_FILE</span></div>
<div class="block">The checkin file command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKIN_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_CHECKIN_PROJECT">
<h3>COMMAND_CHECKIN_PROJECT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_CHECKIN_PROJECT</span></div>
<div class="block">The checkin project command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKIN_PROJECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_HISTORY">
<h3>COMMAND_HISTORY</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_HISTORY</span></div>
<div class="block">The get history command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_HISTORY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="COMMAND_LABEL">
<h3>COMMAND_LABEL</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COMMAND_LABEL</span></div>
<div class="block">The add label command</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_LABEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="PROJECT_PREFIX">
<h3>PROJECT_PREFIX</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">PROJECT_PREFIX</span></div>
<div class="block">The project prefix</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.PROJECT_PREFIX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_COMMAND">
<h3>FLAG_COMMAND</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_COMMAND</span></div>
<div class="block">The command option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_COMMAND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_VSS_SERVER">
<h3>FLAG_VSS_SERVER</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_VSS_SERVER</span></div>
<div class="block">The database (vss server) option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_VSS_SERVER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_USERNAME">
<h3>FLAG_USERNAME</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_USERNAME</span></div>
<div class="block">The username option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_USERNAME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_PASSWORD">
<h3>FLAG_PASSWORD</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_PASSWORD</span></div>
<div class="block">The password option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_PASSWORD">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_COMMENT">
<h3>FLAG_COMMENT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_COMMENT</span></div>
<div class="block">The log option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_COMMENT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_WORKING_DIR">
<h3>FLAG_WORKING_DIR</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_WORKING_DIR</span></div>
<div class="block">The workdir option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_WORKING_DIR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_RECURSION">
<h3>FLAG_RECURSION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_RECURSION</span></div>
<div class="block">The recursive option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_RECURSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_VERSION">
<h3>FLAG_VERSION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_VERSION</span></div>
<div class="block">The revision option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_VERSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_LABEL">
<h3>FLAG_LABEL</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_LABEL</span></div>
<div class="block">The label option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_LABEL">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_NO_COMPRESSION">
<h3>FLAG_NO_COMPRESSION</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_NO_COMPRESSION</span></div>
<div class="block">The no compression option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_NO_COMPRESSION">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_NO_CACHE">
<h3>FLAG_NO_CACHE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_NO_CACHE</span></div>
<div class="block">The no cache option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_NO_CACHE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_SOS_SERVER">
<h3>FLAG_SOS_SERVER</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_SOS_SERVER</span></div>
<div class="block">The server option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_SOS_SERVER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_SOS_HOME">
<h3>FLAG_SOS_HOME</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_SOS_HOME</span></div>
<div class="block">The sos home option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_SOS_HOME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_PROJECT">
<h3>FLAG_PROJECT</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_PROJECT</span></div>
<div class="block">The project option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_PROJECT">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_FILE">
<h3>FLAG_FILE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_FILE</span></div>
<div class="block">The file option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_FILE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="FLAG_VERBOSE">
<h3>FLAG_VERBOSE</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">FLAG_VERBOSE</span></div>
<div class="block">The verbose option</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_VERBOSE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
