<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title><PERSON><PERSON> (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs, class: Javac">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs</a></div>
<h1 title="Class Javac" class="title">Class Javac</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.Javac</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Javac</span>
<span class="extends-implements">extends <a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></span></div>
<div class="block">Compiles Java source files. This task can take the following
 arguments:
 <ul>
 <li>sourcedir
 <li>destdir
 <li>deprecation
 <li>classpath
 <li>bootclasspath
 <li>extdirs
 <li>optimize
 <li>debug
 <li>encoding
 <li>target
 <li>depend
 <li>verbose
 <li>failonerror
 <li>includeantruntime
 <li>includejavaruntime
 <li>source
 <li>compiler
 <li>release
 </ul>
 Of these arguments, the <b>sourcedir</b> and <b>destdir</b> are required.
 <p>
 When this task executes, it will recursively scan the sourcedir and
 destdir looking for Java source files to compile. This task makes its
 compile decision based on timestamp.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.1</dd>
</dl>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Javac.ImplementationSpecificArgument.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs">Javac.ImplementationSpecificArgument</a></code></div>
<div class="col-last even-row-color">
<div class="block">Adds an "compiler" attribute to Commandline$Attribute used to
 filter command line attributes based on the current
 implementation.</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</code></div>
<div class="col-second even-row-color"><code><a href="#compileList" class="member-name-link">compileList</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#failOnError" class="member-name-link">failOnError</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#listFiles" class="member-name-link">listFiles</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#target">target</a>, <a href="../Task.html#taskName">taskName</a>, <a href="../Task.html#taskType">taskType</a>, <a href="../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#description">description</a>, <a href="../ProjectComponent.html#location">location</a>, <a href="../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Javac</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Javac task for compilation of Java files.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(org.apache.tools.ant.taskdefs.compilers.CompilerAdapter)" class="member-name-link">add</a><wbr>(<a href="compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a>&nbsp;adapter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the compiler adapter explicitly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkParameters()" class="member-name-link">checkParameters</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check that all required attributes have been set and nothing
 silly has been entered.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#compile()" class="member-name-link">compile</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform the compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createBootclasspath()" class="member-name-link">createBootclasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to the bootclasspath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to the classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Javac.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Javac.ImplementationSpecificArgument</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCompilerArg()" class="member-name-link">createCompilerArg</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an implementation specific command-line argument.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createCompilerClasspath()" class="member-name-link">createCompilerClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The classpath to use when loading the compiler implementation
 if it is not a built-in one.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createExtdirs()" class="member-name-link">createExtdirs</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to extdirs.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModulepath()" class="member-name-link">createModulepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to the modulepath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createModulesourcepath()" class="member-name-link">createModulesourcepath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to modulesourcepath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createSourcepath()" class="member-name-link">createSourcepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to sourcepath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createSrc()" class="member-name-link">createSrc</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path for source compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createUpgrademodulepath()" class="member-name-link">createUpgrademodulepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a path to the upgrademodulepath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Executes the task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBootclasspath()" class="member-name-link">getBootclasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the bootclasspath that will be used to compile the classes
 against.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClasspath()" class="member-name-link">getClasspath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the classpath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompiler()" class="member-name-link">getCompiler</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The implementation for this particular task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCompilerVersion()" class="member-name-link">getCompilerVersion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The implementation for this particular task.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentCompilerArgs()" class="member-name-link">getCurrentCompilerArgs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the additional implementation specific command line arguments.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDebug()" class="member-name-link">getDebug</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the debug flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDebugLevel()" class="member-name-link">getDebugLevel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the value of debugLevel.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDepend()" class="member-name-link">getDepend</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the depend flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeprecation()" class="member-name-link">getDeprecation</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the deprecation flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestdir()" class="member-name-link">getDestdir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the destination directory into which the java source files
 should be compiled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEncoding()" class="member-name-link">getEncoding</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the java source file encoding name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExecutable()" class="member-name-link">getExecutable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The value of the executable attribute, if any.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExtdirs()" class="member-name-link">getExtdirs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the extension directories that will be used during the
 compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFailonerror()" class="member-name-link">getFailonerror</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the failonerror flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileList()" class="member-name-link">getFileList</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the list of files to be compiled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludeantruntime()" class="member-name-link">getIncludeantruntime</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets whether or not the ant classpath is to be included in the classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIncludejavaruntime()" class="member-name-link">getIncludejavaruntime</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets whether or not the java runtime should be included in this
 task's classpath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJavacExecutable()" class="member-name-link">getJavacExecutable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The name of the javac executable to use in fork-mode.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getListfiles()" class="member-name-link">getListfiles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the listfiles flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMemoryInitialSize()" class="member-name-link">getMemoryInitialSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the memoryInitialSize flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMemoryMaximumSize()" class="member-name-link">getMemoryMaximumSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the memoryMaximumSize flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModulepath()" class="member-name-link">getModulepath</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the modulepath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModulesourcepath()" class="member-name-link">getModulesourcepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the modulesourcepath to be used for this compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeHeaderDir()" class="member-name-link">getNativeHeaderDir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the destination directory into which the generated native
 header files should be placed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNowarn()" class="member-name-link">getNowarn</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Should the -nowarn option be used.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOptimize()" class="member-name-link">getOptimize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the optimize flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRelease()" class="member-name-link">getRelease</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the version to use for the <code>--release</code> switch that
 combines <code>source</code>, <code>target</code> and setting the
 bootclasspath.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSource()" class="member-name-link">getSource</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the value of source.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSourcepath()" class="member-name-link">getSourcepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the sourcepath to be used for this compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSrcdir()" class="member-name-link">getSrcdir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the source dirs to find the source java files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSystemJavac()" class="member-name-link">getSystemJavac</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTarget()" class="member-name-link">getTarget</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the target VM that the classes will be compiled for.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTaskSuccess()" class="member-name-link">getTaskSuccess</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the result of the javac task (success or failure).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTempdir()" class="member-name-link">getTempdir</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Where Ant should place temporary files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUpgrademodulepath()" class="member-name-link">getUpgrademodulepath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the upgrademodulepath to be used for this compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getVerbose()" class="member-name-link">getVerbose</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the verbose flag.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isForkedJavac()" class="member-name-link">isForkedJavac</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is this a forked invocation of JDK's javac?</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isIncludeDestClasses()" class="member-name-link">isIncludeDestClasses</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the value of the includeDestClasses property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isJdkCompiler(java.lang.String)" class="member-name-link">isJdkCompiler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compilerImpl)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Is the compiler implementation a jdk compiler</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#recreateSrc()" class="member-name-link">recreateSrc</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Recreate src.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resetFileLists()" class="member-name-link">resetFileLists</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clear the list of files to be compiled and copied..</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#scanDir(java.io.File,java.io.File,java.lang.String%5B%5D)" class="member-name-link">scanDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;files)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Scans the directory looking for source files to be compiled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBootclasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setBootclasspath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;bootclasspath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the bootclasspath that will be used to compile the classes
 against.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBootClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setBootClasspathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a reference to a classpath defined elsewhere.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath to be used for this compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setClasspathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a reference to a classpath defined elsewhere.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCompiler(java.lang.String)" class="member-name-link">setCompiler</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compiler)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Choose the implementation for this particular task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCreateMissingPackageInfoClass(boolean)" class="member-name-link">setCreateMissingPackageInfoClass</a><wbr>(boolean&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Whether package-info.class files will be created by Ant
 matching package-info.java files that have been compiled but
 didn't create class files themselves.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebug(boolean)" class="member-name-link">setDebug</a><wbr>(boolean&nbsp;debug)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether source should be compiled
 with debug information; defaults to off.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDebugLevel(java.lang.String)" class="member-name-link">setDebugLevel</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;v)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Keyword list to be appended to the -g command-line switch.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDepend(boolean)" class="member-name-link">setDepend</a><wbr>(boolean&nbsp;depend)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables dependency-tracking for compilers
 that support this (jikes and classic).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDeprecation(boolean)" class="member-name-link">setDeprecation</a><wbr>(boolean&nbsp;deprecation)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether source should be
 compiled with deprecation information; defaults to off.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory into which the Java source
 files should be compiled.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setEncoding(java.lang.String)" class="member-name-link">setEncoding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the Java source file encoding name.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setErrorProperty(java.lang.String)" class="member-name-link">setErrorProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The property to set on compilation failure.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExecutable(java.lang.String)" class="member-name-link">setExecutable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;forkExec)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the name of the javac executable.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setExtdirs(org.apache.tools.ant.types.Path)" class="member-name-link">setExtdirs</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;extdirs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the extension directories that will be used during the
 compilation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFailonerror(boolean)" class="member-name-link">setFailonerror</a><wbr>(boolean&nbsp;fail)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicates whether the build will continue
 even if there are compilation errors; defaults to true.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFork(boolean)" class="member-name-link">setFork</a><wbr>(boolean&nbsp;f)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, forks the javac compiler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeantruntime(boolean)" class="member-name-link">setIncludeantruntime</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, includes Ant's own classpath in the classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludeDestClasses(boolean)" class="member-name-link">setIncludeDestClasses</a><wbr>(boolean&nbsp;includeDestClasses)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This property controls whether to include the
 destination classes directory in the classpath
 given to the compiler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setIncludejavaruntime(boolean)" class="member-name-link">setIncludejavaruntime</a><wbr>(boolean&nbsp;include)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, includes the Java runtime libraries in the classpath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setListfiles(boolean)" class="member-name-link">setListfiles</a><wbr>(boolean&nbsp;list)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, list the source files being handed off to the compiler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMemoryInitialSize(java.lang.String)" class="member-name-link">setMemoryInitialSize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;memoryInitialSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The initial size of the memory for the underlying VM
 if javac is run externally; ignored otherwise.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMemoryMaximumSize(java.lang.String)" class="member-name-link">setMemoryMaximumSize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;memoryMaximumSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The maximum size of the memory for the underlying VM
 if javac is run externally; ignored otherwise.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulepath(org.apache.tools.ant.types.Path)" class="member-name-link">setModulepath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;mp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the modulepath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulepathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setModulepathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a reference to a modulepath defined elsewhere.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulesourcepath(org.apache.tools.ant.types.Path)" class="member-name-link">setModulesourcepath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;msp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the modulesourcepath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setModulesourcepathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setModulesourcepathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a reference to a modulesourcepath defined elsewhere.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNativeHeaderDir(java.io.File)" class="member-name-link">setNativeHeaderDir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;nhDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory into which the generated native
 header files should be placed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNowarn(boolean)" class="member-name-link">setNowarn</a><wbr>(boolean&nbsp;flag)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, enables the -nowarn option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOptimize(boolean)" class="member-name-link">setOptimize</a><wbr>(boolean&nbsp;optimize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, compiles with optimization enabled.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setProceed(boolean)" class="member-name-link">setProceed</a><wbr>(boolean&nbsp;proceed)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRelease(java.lang.String)" class="member-name-link">setRelease</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;release)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the version to use for the <code>--release</code> switch that
 combines <code>source</code>, <code>target</code> and setting the
 bootclasspath.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSource(java.lang.String)" class="member-name-link">setSource</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;v)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Value of the -source command-line switch; will be ignored by
 all implementations except modern, jikes and gcj (gcj uses
 -fsource).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSourcepath(org.apache.tools.ant.types.Path)" class="member-name-link">setSourcepath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;sourcepath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the sourcepath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSourcepathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setSourcepathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a reference to a source path defined elsewhere.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrcdir(org.apache.tools.ant.types.Path)" class="member-name-link">setSrcdir</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;srcDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the source directories to find the source Java files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTarget(java.lang.String)" class="member-name-link">setTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the target VM that the classes will be compiled for.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTempdir(java.io.File)" class="member-name-link">setTempdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;tmpDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Where Ant should place temporary files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUpdatedProperty(java.lang.String)" class="member-name-link">setUpdatedProperty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;updatedProperty)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The property to set on compilation success.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUpgrademodulepath(org.apache.tools.ant.types.Path)" class="member-name-link">setUpgrademodulepath</a><wbr>(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;ump)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the upgrademodulepath to be used for this compilation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUpgrademodulepathRef(org.apache.tools.ant.types.Reference)" class="member-name-link">setUpgrademodulepathRef</a><wbr>(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a reference to the upgrademodulepath defined elsewhere.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setVerbose(boolean)" class="member-name-link">setVerbose</a><wbr>(boolean&nbsp;verbose)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If true, asks the compiler for verbose output.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="MatchingTask.html#createExclude()">createExclude</a>, <a href="MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="MatchingTask.html#createInclude()">createInclude</a>, <a href="MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="MatchingTask.html#selectorCount()">selectorCount</a>, <a href="MatchingTask.html#selectorElements()">selectorElements</a>, <a href="MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../Task.html#getTaskName()">getTaskName</a>, <a href="../Task.html#getTaskType()">getTaskType</a>, <a href="../Task.html#getWrapper()">getWrapper</a>, <a href="../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../Task.html#init()">init</a>, <a href="../Task.html#isInvalid()">isInvalid</a>, <a href="../Task.html#log(java.lang.String)">log</a>, <a href="../Task.html#log(java.lang.String,int)">log</a>, <a href="../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../Task.html#perform()">perform</a>, <a href="../Task.html#reconfigure()">reconfigure</a>, <a href="../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../ProjectComponent.html#clone()">clone</a>, <a href="../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../ProjectComponent.html#getProject()">getProject</a>, <a href="../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="failOnError">
<h3>failOnError</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">failOnError</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="listFiles">
<h3>listFiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">listFiles</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="compileList">
<h3>compileList</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</span>&nbsp;<span class="element-name">compileList</span></div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Javac</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Javac</span>()</div>
<div class="block">Javac task for compilation of Java files.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getDebugLevel()">
<h3>getDebugLevel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDebugLevel</span>()</div>
<div class="block">Get the value of debugLevel.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>value of debugLevel.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDebugLevel(java.lang.String)">
<h3>setDebugLevel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebugLevel</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;v)</span></div>
<div class="block">Keyword list to be appended to the -g command-line switch.

 This will be ignored by all implementations except modern
 and classic(ver &gt;= 1.2). Legal values are none or a
 comma-separated list of the following keywords: lines, vars,
 and source. If debuglevel is not specified, by default, :none
 will be appended to -g. If debug is not turned on, this attribute
 will be ignored.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>v</code> - Value to assign to debugLevel.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSource()">
<h3>getSource</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSource</span>()</div>
<div class="block">Get the value of source.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>value of source.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSource(java.lang.String)">
<h3>setSource</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSource</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;v)</span></div>
<div class="block">Value of the -source command-line switch; will be ignored by
 all implementations except modern, jikes and gcj (gcj uses
 -fsource).

 <p>If you use this attribute together with jikes or gcj, you
 must make sure that your version of jikes supports the -source
 switch.</p>

 <p>Legal values are 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, and any integral number bigger than 4
 - by default, no -source argument will be used at all.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>v</code> - Value to assign to source.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createSrc()">
<h3>createSrc</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createSrc</span>()</div>
<div class="block">Adds a path for source compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a nested src element.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="recreateSrc()">
<h3>recreateSrc</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">recreateSrc</span>()</div>
<div class="block">Recreate src.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a nested src element.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSrcdir(org.apache.tools.ant.types.Path)">
<h3>setSrcdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrcdir</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;srcDir)</span></div>
<div class="block">Set the source directories to find the source Java files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - the source directories as a path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSrcdir()">
<h3>getSrcdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getSrcdir</span>()</div>
<div class="block">Gets the source dirs to find the source java files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the source directories as a path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir)</span></div>
<div class="block">Set the destination directory into which the Java source
 files should be compiled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>destDir</code> - the destination director</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDestdir()">
<h3>getDestdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestdir</span>()</div>
<div class="block">Gets the destination directory into which the java source files
 should be compiled.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the destination directory</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNativeHeaderDir(java.io.File)">
<h3>setNativeHeaderDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNativeHeaderDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;nhDir)</span></div>
<div class="block">Set the destination directory into which the generated native
 header files should be placed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nhDir</code> - where to place generated native header files</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNativeHeaderDir()">
<h3>getNativeHeaderDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getNativeHeaderDir</span>()</div>
<div class="block">Gets the destination directory into which the generated native
 header files should be placed.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>where to place generated native header files</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSourcepath(org.apache.tools.ant.types.Path)">
<h3>setSourcepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSourcepath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;sourcepath)</span></div>
<div class="block">Set the sourcepath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourcepath</code> - the source path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSourcepath()">
<h3>getSourcepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getSourcepath</span>()</div>
<div class="block">Gets the sourcepath to be used for this compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the source path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createSourcepath()">
<h3>createSourcepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createSourcepath</span>()</div>
<div class="block">Adds a path to sourcepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a sourcepath to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSourcepathRef(org.apache.tools.ant.types.Reference)">
<h3>setSourcepathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSourcepathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Adds a reference to a source path defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a reference to a source path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModulesourcepath(org.apache.tools.ant.types.Path)">
<h3>setModulesourcepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulesourcepath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;msp)</span></div>
<div class="block">Set the modulesourcepath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>msp</code> - the modulesourcepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getModulesourcepath()">
<h3>getModulesourcepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getModulesourcepath</span>()</div>
<div class="block">Gets the modulesourcepath to be used for this compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the modulesourcepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createModulesourcepath()">
<h3>createModulesourcepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createModulesourcepath</span>()</div>
<div class="block">Adds a path to modulesourcepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a modulesourcepath to be configured</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModulesourcepathRef(org.apache.tools.ant.types.Reference)">
<h3>setModulesourcepathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulesourcepathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Adds a reference to a modulesourcepath defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a reference to a modulesourcepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - an Ant Path object containing the compilation classpath.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getClasspath()">
<h3>getClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getClasspath</span>()</div>
<div class="block">Gets the classpath to be used for this compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the class path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Adds a path to the classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a class path to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setClasspathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Adds a reference to a classpath defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a reference to a classpath</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModulepath(org.apache.tools.ant.types.Path)">
<h3>setModulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulepath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;mp)</span></div>
<div class="block">Set the modulepath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mp</code> - an Ant Path object containing the modulepath.</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getModulepath()">
<h3>getModulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getModulepath</span>()</div>
<div class="block">Gets the modulepath to be used for this compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the modulepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createModulepath()">
<h3>createModulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createModulepath</span>()</div>
<div class="block">Adds a path to the modulepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a modulepath to be configured</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setModulepathRef(org.apache.tools.ant.types.Reference)">
<h3>setModulepathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setModulepathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Adds a reference to a modulepath defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a reference to a modulepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUpgrademodulepath(org.apache.tools.ant.types.Path)">
<h3>setUpgrademodulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUpgrademodulepath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;ump)</span></div>
<div class="block">Set the upgrademodulepath to be used for this compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ump</code> - an Ant Path object containing the upgrademodulepath.</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getUpgrademodulepath()">
<h3>getUpgrademodulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getUpgrademodulepath</span>()</div>
<div class="block">Gets the upgrademodulepath to be used for this compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the upgrademodulepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createUpgrademodulepath()">
<h3>createUpgrademodulepath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createUpgrademodulepath</span>()</div>
<div class="block">Adds a path to the upgrademodulepath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>an upgrademodulepath to be configured</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUpgrademodulepathRef(org.apache.tools.ant.types.Reference)">
<h3>setUpgrademodulepathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUpgrademodulepathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Adds a reference to the upgrademodulepath defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a reference to an upgrademodulepath</dd>
<dt>Since:</dt>
<dd>1.9.7</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBootclasspath(org.apache.tools.ant.types.Path)">
<h3>setBootclasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBootclasspath</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;bootclasspath)</span></div>
<div class="block">Sets the bootclasspath that will be used to compile the classes
 against.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>bootclasspath</code> - a path to use as a boot class path (may be more
                      than one)</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getBootclasspath()">
<h3>getBootclasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getBootclasspath</span>()</div>
<div class="block">Gets the bootclasspath that will be used to compile the classes
 against.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the boot path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createBootclasspath()">
<h3>createBootclasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createBootclasspath</span>()</div>
<div class="block">Adds a path to the bootclasspath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a path to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBootClasspathRef(org.apache.tools.ant.types.Reference)">
<h3>setBootClasspathRef</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBootClasspathRef</span><wbr><span class="parameters">(<a href="../types/Reference.html" title="class in org.apache.tools.ant.types">Reference</a>&nbsp;r)</span></div>
<div class="block">Adds a reference to a classpath defined elsewhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>r</code> - a reference to a classpath</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExtdirs(org.apache.tools.ant.types.Path)">
<h3>setExtdirs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExtdirs</span><wbr><span class="parameters">(<a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;extdirs)</span></div>
<div class="block">Sets the extension directories that will be used during the
 compilation.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extdirs</code> - a path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExtdirs()">
<h3>getExtdirs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">getExtdirs</span>()</div>
<div class="block">Gets the extension directories that will be used during the
 compilation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the extension directories as a path</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createExtdirs()">
<h3>createExtdirs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createExtdirs</span>()</div>
<div class="block">Adds a path to extdirs.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a path to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setListfiles(boolean)">
<h3>setListfiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setListfiles</span><wbr><span class="parameters">(boolean&nbsp;list)</span></div>
<div class="block">If true, list the source files being handed off to the compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - if true list the source files</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getListfiles()">
<h3>getListfiles</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getListfiles</span>()</div>
<div class="block">Get the listfiles flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the listfiles flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFailonerror(boolean)">
<h3>setFailonerror</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFailonerror</span><wbr><span class="parameters">(boolean&nbsp;fail)</span></div>
<div class="block">Indicates whether the build will continue
 even if there are compilation errors; defaults to true.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fail</code> - if true halt the build on failure</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setProceed(boolean)">
<h3>setProceed</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setProceed</span><wbr><span class="parameters">(boolean&nbsp;proceed)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>proceed</code> - inverse of failoferror</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFailonerror()">
<h3>getFailonerror</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getFailonerror</span>()</div>
<div class="block">Gets the failonerror flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the failonerror flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDeprecation(boolean)">
<h3>setDeprecation</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDeprecation</span><wbr><span class="parameters">(boolean&nbsp;deprecation)</span></div>
<div class="block">Indicates whether source should be
 compiled with deprecation information; defaults to off.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>deprecation</code> - if true turn on deprecation information</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDeprecation()">
<h3>getDeprecation</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDeprecation</span>()</div>
<div class="block">Gets the deprecation flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deprecation flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMemoryInitialSize(java.lang.String)">
<h3>setMemoryInitialSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMemoryInitialSize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;memoryInitialSize)</span></div>
<div class="block">The initial size of the memory for the underlying VM
 if javac is run externally; ignored otherwise.
 Defaults to the standard VM memory setting.
 (Examples: 83886080, 81920k, or 80m)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>memoryInitialSize</code> - string to pass to VM</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMemoryInitialSize()">
<h3>getMemoryInitialSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMemoryInitialSize</span>()</div>
<div class="block">Gets the memoryInitialSize flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the memoryInitialSize flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setMemoryMaximumSize(java.lang.String)">
<h3>setMemoryMaximumSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMemoryMaximumSize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;memoryMaximumSize)</span></div>
<div class="block">The maximum size of the memory for the underlying VM
 if javac is run externally; ignored otherwise.
 Defaults to the standard VM memory setting.
 (Examples: 83886080, 81920k, or 80m)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>memoryMaximumSize</code> - string to pass to VM</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getMemoryMaximumSize()">
<h3>getMemoryMaximumSize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMemoryMaximumSize</span>()</div>
<div class="block">Gets the memoryMaximumSize flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the memoryMaximumSize flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setEncoding(java.lang.String)">
<h3>setEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setEncoding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding)</span></div>
<div class="block">Set the Java source file encoding name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>encoding</code> - the source file encoding</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getEncoding()">
<h3>getEncoding</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getEncoding</span>()</div>
<div class="block">Gets the java source file encoding name.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the source file encoding name</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDebug(boolean)">
<h3>setDebug</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDebug</span><wbr><span class="parameters">(boolean&nbsp;debug)</span></div>
<div class="block">Indicates whether source should be compiled
 with debug information; defaults to off.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>debug</code> - if true compile with debug information</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDebug()">
<h3>getDebug</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDebug</span>()</div>
<div class="block">Gets the debug flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the debug flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setOptimize(boolean)">
<h3>setOptimize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOptimize</span><wbr><span class="parameters">(boolean&nbsp;optimize)</span></div>
<div class="block">If true, compiles with optimization enabled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>optimize</code> - if true compile with optimization enabled</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getOptimize()">
<h3>getOptimize</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getOptimize</span>()</div>
<div class="block">Gets the optimize flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the optimize flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDepend(boolean)">
<h3>setDepend</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDepend</span><wbr><span class="parameters">(boolean&nbsp;depend)</span></div>
<div class="block">Enables dependency-tracking for compilers
 that support this (jikes and classic).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>depend</code> - if true enable dependency-tracking</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDepend()">
<h3>getDepend</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getDepend</span>()</div>
<div class="block">Gets the depend flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the depend flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setVerbose(boolean)">
<h3>setVerbose</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setVerbose</span><wbr><span class="parameters">(boolean&nbsp;verbose)</span></div>
<div class="block">If true, asks the compiler for verbose output.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>verbose</code> - if true, asks the compiler for verbose output</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getVerbose()">
<h3>getVerbose</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getVerbose</span>()</div>
<div class="block">Gets the verbose flag.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the verbose flag</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTarget(java.lang.String)">
<h3>setTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;target)</span></div>
<div class="block">Sets the target VM that the classes will be compiled for. Valid
 values depend on the compiler, for jdk 1.4 the valid values are
 "1.1", "1.2", "1.3", "1.4", "1.5", "1.6", "1.7", "1.8", "1.9" and any integral number bigger than 4</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - the target VM</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTarget()">
<h3>getTarget</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTarget</span>()</div>
<div class="block">Gets the target VM that the classes will be compiled for.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the target VM</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setRelease(java.lang.String)">
<h3>setRelease</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRelease</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;release)</span></div>
<div class="block">Sets the version to use for the <code>--release</code> switch that
 combines <code>source</code>, <code>target</code> and setting the
 bootclasspath.

 Values depend on the compiler, for jdk 9 the valid values are
 "6", "7", "8", "9".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>release</code> - the value of the release attribute</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getRelease()">
<h3>getRelease</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRelease</span>()</div>
<div class="block">Gets the version to use for the <code>--release</code> switch that
 combines <code>source</code>, <code>target</code> and setting the
 bootclasspath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the value of the release attribute</dd>
<dt>Since:</dt>
<dd>Ant 1.9.8</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludeantruntime(boolean)">
<h3>setIncludeantruntime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeantruntime</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">If true, includes Ant's own classpath in the classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - if true, includes Ant's own classpath in the classpath</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludeantruntime()">
<h3>getIncludeantruntime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludeantruntime</span>()</div>
<div class="block">Gets whether or not the ant classpath is to be included in the classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>whether or not the ant classpath is to be included in the classpath</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludejavaruntime(boolean)">
<h3>setIncludejavaruntime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludejavaruntime</span><wbr><span class="parameters">(boolean&nbsp;include)</span></div>
<div class="block">If true, includes the Java runtime libraries in the classpath.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>include</code> - if true, includes the Java runtime libraries in the classpath</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getIncludejavaruntime()">
<h3>getIncludejavaruntime</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getIncludejavaruntime</span>()</div>
<div class="block">Gets whether or not the java runtime should be included in this
 task's classpath.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the includejavaruntime attribute</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFork(boolean)">
<h3>setFork</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFork</span><wbr><span class="parameters">(boolean&nbsp;f)</span></div>
<div class="block">If true, forks the javac compiler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>f</code> - "true|false|on|off|yes|no"</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setExecutable(java.lang.String)">
<h3>setExecutable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setExecutable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;forkExec)</span></div>
<div class="block">Sets the name of the javac executable.

 <p>Ignored unless fork is true or extJavac has been specified
 as the compiler.</p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>forkExec</code> - the name of the executable</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getExecutable()">
<h3>getExecutable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getExecutable</span>()</div>
<div class="block">The value of the executable attribute, if any.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the java executable</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isForkedJavac()">
<h3>isForkedJavac</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isForkedJavac</span>()</div>
<div class="block">Is this a forked invocation of JDK's javac?</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this is a forked invocation</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getJavacExecutable()">
<h3>getJavacExecutable</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getJavacExecutable</span>()</div>
<div class="block">The name of the javac executable to use in fork-mode.

 <p>This is either the name specified with the executable
 attribute or the full path of the javac compiler of the VM Ant
 is currently running in - guessed by Ant.</p>

 <p>You should <strong>not</strong> invoke this method if you
 want to get the value of the executable command - use <a href="#getExecutable()"><code>getExecutable</code></a> for this.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the name of the javac executable</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNowarn(boolean)">
<h3>setNowarn</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNowarn</span><wbr><span class="parameters">(boolean&nbsp;flag)</span></div>
<div class="block">If true, enables the -nowarn option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>flag</code> - if true, enable the -nowarn option</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getNowarn()">
<h3>getNowarn</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getNowarn</span>()</div>
<div class="block">Should the -nowarn option be used.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if the -nowarn option should be used</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createCompilerArg()">
<h3>createCompilerArg</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Javac.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Javac.ImplementationSpecificArgument</a></span>&nbsp;<span class="element-name">createCompilerArg</span>()</div>
<div class="block">Adds an implementation specific command-line argument.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a ImplementationSpecificArgument to be configured</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCurrentCompilerArgs()">
<h3>getCurrentCompilerArgs</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">getCurrentCompilerArgs</span>()</div>
<div class="block">Get the additional implementation specific command line arguments.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>array of command line arguments, guaranteed to be non-null.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setTempdir(java.io.File)">
<h3>setTempdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTempdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;tmpDir)</span></div>
<div class="block">Where Ant should place temporary files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tmpDir</code> - the temporary directory</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTempdir()">
<h3>getTempdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getTempdir</span>()</div>
<div class="block">Where Ant should place temporary files.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the temporary directory</dd>
<dt>Since:</dt>
<dd>Ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setUpdatedProperty(java.lang.String)">
<h3>setUpdatedProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUpdatedProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;updatedProperty)</span></div>
<div class="block">The property to set on compilation success.
 This property will not be set if the compilation
 fails, or if there are no files to compile.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>updatedProperty</code> - the property name to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setErrorProperty(java.lang.String)">
<h3>setErrorProperty</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setErrorProperty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorProperty)</span></div>
<div class="block">The property to set on compilation failure.
 This property will be set if the compilation
 fails.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>errorProperty</code> - the property name to use.</dd>
<dt>Since:</dt>
<dd>Ant 1.7.1.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setIncludeDestClasses(boolean)">
<h3>setIncludeDestClasses</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setIncludeDestClasses</span><wbr><span class="parameters">(boolean&nbsp;includeDestClasses)</span></div>
<div class="block">This property controls whether to include the
 destination classes directory in the classpath
 given to the compiler.
 The default value is "true".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>includeDestClasses</code> - the value to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isIncludeDestClasses()">
<h3>isIncludeDestClasses</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isIncludeDestClasses</span>()</div>
<div class="block">Get the value of the includeDestClasses property.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the value.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTaskSuccess()">
<h3>getTaskSuccess</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">getTaskSuccess</span>()</div>
<div class="block">Get the result of the javac task (success or failure).</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if compilation succeeded, or
         was not necessary, false if the compilation failed.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createCompilerClasspath()">
<h3>createCompilerClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createCompilerClasspath</span>()</div>
<div class="block">The classpath to use when loading the compiler implementation
 if it is not a built-in one.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Path</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="add(org.apache.tools.ant.taskdefs.compilers.CompilerAdapter)">
<h3>add</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</a>&nbsp;adapter)</span></div>
<div class="block">Set the compiler adapter explicitly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>adapter</code> - CompilerAdapter</dd>
<dt>Since:</dt>
<dd>Ant 1.8.0</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCreateMissingPackageInfoClass(boolean)">
<h3>setCreateMissingPackageInfoClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCreateMissingPackageInfoClass</span><wbr><span class="parameters">(boolean&nbsp;b)</span></div>
<div class="block">Whether package-info.class files will be created by Ant
 matching package-info.java files that have been compiled but
 didn't create class files themselves.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>b</code> - boolean</dd>
<dt>Since:</dt>
<dd>Ant 1.8.3</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Executes the task.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an error occurs</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="resetFileLists()">
<h3>resetFileLists</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resetFileLists</span>()</div>
<div class="block">Clear the list of files to be compiled and copied..</div>
</div>
</section>
</li>
<li>
<section class="detail" id="scanDir(java.io.File,java.io.File,java.lang.String[])">
<h3>scanDir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">scanDir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;srcDir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;destDir,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;files)</span></div>
<div class="block">Scans the directory looking for source files to be compiled.
 The results are returned in the class variable compileList</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcDir</code> - The source directory</dd>
<dd><code>destDir</code> - The destination directory</dd>
<dd><code>files</code> - An array of filenames</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getFileList()">
<h3>getFileList</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]</span>&nbsp;<span class="element-name">getFileList</span>()</div>
<div class="block">Gets the list of files to be compiled.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the list of files as an array</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="isJdkCompiler(java.lang.String)">
<h3>isJdkCompiler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isJdkCompiler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compilerImpl)</span></div>
<div class="block">Is the compiler implementation a jdk compiler</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compilerImpl</code> - the name of the compiler implementation</dd>
<dt>Returns:</dt>
<dd>true if compilerImpl is "modern", "classic",
 "javac1.1", "javac1.2", "javac1.3", "javac1.4", "javac1.5",
 "javac1.6", "javac1.7", "javac1.8", "javac1.9", "javac9" or "javac10+".</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSystemJavac()">
<h3>getSystemJavac</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSystemJavac</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the executable name of the java compiler</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCompiler(java.lang.String)">
<h3>setCompiler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCompiler</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;compiler)</span></div>
<div class="block">Choose the implementation for this particular task.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>compiler</code> - the name of the compiler</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCompiler()">
<h3>getCompiler</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCompiler</span>()</div>
<div class="block">The implementation for this particular task.

 <p>Defaults to the build.compiler property but can be overridden
 via the compiler and fork attributes.</p>

 <p>If fork has been set to true, the result will be extJavac
 and not classic or java1.2 - no matter what the compiler
 attribute looks like.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the compiler.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getCompilerVersion()"><code>getCompilerVersion()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCompilerVersion()">
<h3>getCompilerVersion</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCompilerVersion</span>()</div>
<div class="block">The implementation for this particular task.

 <p>Defaults to the build.compiler property but can be overridden
 via the compiler attribute.</p>

 <p>This method does not take the fork attribute into
 account.</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the compiler.</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getCompiler()"><code>getCompiler()</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="checkParameters()">
<h3>checkParameters</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">checkParameters</span>()
                        throws <span class="exceptions"><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Check that all required attributes have been set and nothing
 silly has been entered.</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - if an error occurs</dd>
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="compile()">
<h3>compile</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">compile</span>()</div>
<div class="block">Perform the compilation.</div>
<dl class="notes">
<dt>Since:</dt>
<dd>Ant 1.5</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
