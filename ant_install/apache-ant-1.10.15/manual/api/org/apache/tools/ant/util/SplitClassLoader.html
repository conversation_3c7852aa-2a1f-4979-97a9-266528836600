<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>SplitClassLoader (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.util, class: SplitClassLoader">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.util</a></div>
<h1 title="Class SplitClassLoader" class="title">Class SplitClassLoader</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">java.lang.ClassLoader</a>
<div class="inheritance"><a href="../AntClassLoader.html" title="class in org.apache.tools.ant">org.apache.tools.ant.AntClassLoader</a>
<div class="inheritance">org.apache.tools.ant.util.SplitClassLoader</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/EventListener.html" title="class or interface in java.util" class="external-link">EventListener</a></code>, <code><a href="../BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</a></code>, <code><a href="../SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">SplitClassLoader</span>
<span class="extends-implements">extends <a href="../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></span></div>
<div class="block">Specialized classloader for tasks that need finer grained control
 over which classes are to be loaded via Ant's classloader and which
 should not even if they are available.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.ClassLoader,org.apache.tools.ant.types.Path,org.apache.tools.ant.Project,java.lang.String%5B%5D)" class="member-name-link">SplitClassLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;splitClasses)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a><wbr>&lt;?&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadClass(java.lang.String,boolean)" class="member-name-link">loadClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname,
 boolean&nbsp;resolve)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads a class with this class loader.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.AntClassLoader">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></h3>
<code><a href="../AntClassLoader.html#addJavaLibraries()">addJavaLibraries</a>, <a href="../AntClassLoader.html#addLoaderPackageRoot(java.lang.String)">addLoaderPackageRoot</a>, <a href="../AntClassLoader.html#addPathComponent(java.io.File)">addPathComponent</a>, <a href="../AntClassLoader.html#addPathElement(java.lang.String)">addPathElement</a>, <a href="../AntClassLoader.html#addPathFile(java.io.File)">addPathFile</a>, <a href="../AntClassLoader.html#addSystemPackageRoot(java.lang.String)">addSystemPackageRoot</a>, <a href="../AntClassLoader.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</a>, <a href="../AntClassLoader.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</a>, <a href="../AntClassLoader.html#cleanup()">cleanup</a>, <a href="../AntClassLoader.html#close()">close</a>, <a href="../AntClassLoader.html#defineClassFromData(java.io.File,byte%5B%5D,java.lang.String)">defineClassFromData</a>, <a href="../AntClassLoader.html#definePackage(java.io.File,java.lang.String)">definePackage</a>, <a href="../AntClassLoader.html#definePackage(java.io.File,java.lang.String,java.util.jar.Manifest)">definePackage</a>, <a href="../AntClassLoader.html#findClass(java.lang.String)">findClass</a>, <a href="../AntClassLoader.html#findResource(java.lang.String)">findResource</a>, <a href="../AntClassLoader.html#findResources(java.lang.String)">findResources</a>, <a href="../AntClassLoader.html#findResources(java.lang.String,boolean)">findResources</a>, <a href="../AntClassLoader.html#forceLoadClass(java.lang.String)">forceLoadClass</a>, <a href="../AntClassLoader.html#forceLoadSystemClass(java.lang.String)">forceLoadSystemClass</a>, <a href="../AntClassLoader.html#getClasspath()">getClasspath</a>, <a href="../AntClassLoader.html#getConfiguredParent()">getConfiguredParent</a>, <a href="../AntClassLoader.html#getNamedResources(java.lang.String)">getNamedResources</a>, <a href="../AntClassLoader.html#getResource(java.lang.String)">getResource</a>, <a href="../AntClassLoader.html#getResourceAsStream(java.lang.String)">getResourceAsStream</a>, <a href="../AntClassLoader.html#getResources(java.lang.String)">getResources</a>, <a href="../AntClassLoader.html#getResourceURL(java.io.File,java.lang.String)">getResourceURL</a>, <a href="../AntClassLoader.html#initializeClass(java.lang.Class)">initializeClass</a>, <a href="../AntClassLoader.html#isInPath(java.io.File)">isInPath</a>, <a href="../AntClassLoader.html#log(java.lang.String,int)">log</a>, <a href="../AntClassLoader.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</a>, <a href="../AntClassLoader.html#newAntClassLoader(java.lang.ClassLoader,org.apache.tools.ant.Project,org.apache.tools.ant.types.Path,boolean)">newAntClassLoader</a>, <a href="../AntClassLoader.html#resetThreadContextLoader()">resetThreadContextLoader</a>, <a href="../AntClassLoader.html#setClassPath(org.apache.tools.ant.types.Path)">setClassPath</a>, <a href="../AntClassLoader.html#setIsolated(boolean)">setIsolated</a>, <a href="../AntClassLoader.html#setParent(java.lang.ClassLoader)">setParent</a>, <a href="../AntClassLoader.html#setParentFirst(boolean)">setParentFirst</a>, <a href="../AntClassLoader.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="../AntClassLoader.html#setThreadContextLoader()">setThreadContextLoader</a>, <a href="../AntClassLoader.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</a>, <a href="../AntClassLoader.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</a>, <a href="../AntClassLoader.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</a>, <a href="../AntClassLoader.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</a>, <a href="../AntClassLoader.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</a>, <a href="../AntClassLoader.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</a>, <a href="../AntClassLoader.html#toString()">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.ClassLoader">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#clearAssertionStatus()" title="class or interface in java.lang" class="external-link">clearAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#defineClass(byte%5B%5D,int,int)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#defineClass(java.lang.String,byte%5B%5D,int,int,java.security.ProtectionDomain)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#defineClass(java.lang.String,java.nio.ByteBuffer,java.security.ProtectionDomain)" title="class or interface in java.lang" class="external-link">defineClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#definePackage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.net.URL)" title="class or interface in java.lang" class="external-link">definePackage</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#findClass(java.lang.String,java.lang.String)" title="class or interface in java.lang" class="external-link">findClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#findLibrary(java.lang.String)" title="class or interface in java.lang" class="external-link">findLibrary</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#findLoadedClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findLoadedClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#findResource(java.lang.String,java.lang.String)" title="class or interface in java.lang" class="external-link">findResource</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#findSystemClass(java.lang.String)" title="class or interface in java.lang" class="external-link">findSystemClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getClassLoadingLock(java.lang.String)" title="class or interface in java.lang" class="external-link">getClassLoadingLock</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getDefinedPackage(java.lang.String)" title="class or interface in java.lang" class="external-link">getDefinedPackage</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getDefinedPackages()" title="class or interface in java.lang" class="external-link">getDefinedPackages</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getName()" title="class or interface in java.lang" class="external-link">getName</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getPackage(java.lang.String)" title="class or interface in java.lang" class="external-link">getPackage</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getPackages()" title="class or interface in java.lang" class="external-link">getPackages</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getParent()" title="class or interface in java.lang" class="external-link">getParent</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getPlatformClassLoader()" title="class or interface in java.lang" class="external-link">getPlatformClassLoader</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getSystemClassLoader()" title="class or interface in java.lang" class="external-link">getSystemClassLoader</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getSystemResource(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResource</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getSystemResourceAsStream(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResourceAsStream</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getSystemResources(java.lang.String)" title="class or interface in java.lang" class="external-link">getSystemResources</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#getUnnamedModule()" title="class or interface in java.lang" class="external-link">getUnnamedModule</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#isRegisteredAsParallelCapable()" title="class or interface in java.lang" class="external-link">isRegisteredAsParallelCapable</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#loadClass(java.lang.String)" title="class or interface in java.lang" class="external-link">loadClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#registerAsParallelCapable()" title="class or interface in java.lang" class="external-link">registerAsParallelCapable</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#resolveClass(java.lang.Class)" title="class or interface in java.lang" class="external-link">resolveClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#resources(java.lang.String)" title="class or interface in java.lang" class="external-link">resources</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#setClassAssertionStatus(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">setClassAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#setDefaultAssertionStatus(boolean)" title="class or interface in java.lang" class="external-link">setDefaultAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#setPackageAssertionStatus(java.lang.String,boolean)" title="class or interface in java.lang" class="external-link">setPackageAssertionStatus</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html#setSigners(java.lang.Class,java.lang.Object%5B%5D)" title="class or interface in java.lang" class="external-link">setSigners</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.ClassLoader,org.apache.tools.ant.types.Path,org.apache.tools.ant.Project,java.lang.String[])">
<h3>SplitClassLoader</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">SplitClassLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassLoader.html" title="class or interface in java.lang" class="external-link">ClassLoader</a>&nbsp;parent,
 <a href="../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;path,
 <a href="../Project.html" title="class in org.apache.tools.ant">Project</a>&nbsp;project,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;splitClasses)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parent</code> - ClassLoader</dd>
<dd><code>path</code> - Path</dd>
<dd><code>project</code> - Project</dd>
<dd><code>splitClasses</code> - classes contained herein will not be loaded
 via Ant's classloader</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="loadClass(java.lang.String,boolean)">
<h3>loadClass</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">loadClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classname,
 boolean&nbsp;resolve)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../AntClassLoader.html#loadClass(java.lang.String,boolean)">AntClassLoader</a></code></span></div>
<div class="block">Loads a class with this class loader.

 This class attempts to load the class in an order determined by whether
 or not the class matches the system/loader package lists, with the
 loader package list taking priority. If the classloader is in isolated
 mode, failure to load the class in this loader will result in a
 ClassNotFoundException.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../AntClassLoader.html#loadClass(java.lang.String,boolean)">loadClass</a></code>&nbsp;in class&nbsp;<code><a href="../AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</a></code></dd>
<dt>Parameters:</dt>
<dd><code>classname</code> - The name of the class to be loaded.
                  Must not be <code>null</code>.</dd>
<dd><code>resolve</code> - <code>true</code> if all classes upon which this class
                depends are to be loaded.</dd>
<dt>Returns:</dt>
<dd>the required Class object</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/ClassNotFoundException.html" title="class or interface in java.lang" class="external-link">ClassNotFoundException</a></code> - if the requested class does not exist
 on the system classpath (when not in isolated mode) or this loader's
 classpath.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
