<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title><PERSON>j<PERSON><PERSON><PERSON> (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.taskdefs.optional.ejb, class: EjbJar">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</a></div>
<h1 title="Class EjbJar" class="title">Class EjbJar</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</a>
<div class="inheritance"><a href="../../../Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</a>
<div class="inheritance"><a href="../../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</a>
<div class="inheritance">org.apache.tools.ant.taskdefs.optional.ejb.EjbJar</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="../../../types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">EjbJar</span>
<span class="extends-implements">extends <a href="../../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></span></div>
<div class="block">Provides automated EJB JAR file creation.
 <p>
 Extends the
 MatchingTask class provided in the default ant distribution to provide a
 directory scanning EJB jarfile generator.
 </p>

 <p>
 The task works by taking the deployment descriptors one at a time and
 parsing them to locate the names of the classes which should be placed in
 the jar. The classnames are translated to java.io.Files by replacing
 periods with File.separatorChar and resolving the generated filename as a
 relative path under the srcDir attribute. All necessary files are then
 assembled into a jarfile. One jarfile is constructed for each deployment
 descriptor found.
 </p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="EjbJar.CMPVersion.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</a></code></div>
<div class="col-last even-row-color">
<div class="block">CMP versions supported
 valid CMP versions are 1.0 and 2.0</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="EjbJar.DTDLocation.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.DTDLocation</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Inner class used to record information about the location of a local DTD</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="EjbJar.NamingScheme.html" class="type-name-link" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</a></code></div>
<div class="col-last even-row-color">
<div class="block">An EnumeratedAttribute class for handling different EJB jar naming
 schemes</div>
</div>
</div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Fields inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="../../MatchingTask.html#fileset">fileset</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.Task">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#target">target</a>, <a href="../../../Task.html#taskName">taskName</a>, <a href="../../../Task.html#taskType">taskType</a>, <a href="../../../Task.html#wrapper">wrapper</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.ProjectComponent">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#description">description</a>, <a href="../../../ProjectComponent.html#location">location</a>, <a href="../../../ProjectComponent.html#project">project</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">EjbJar</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addDeploymentTool(org.apache.tools.ant.taskdefs.optional.ejb.EJBDeploymentTool)" class="member-name-link">addDeploymentTool</a><wbr>(<a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a>&nbsp;deploymentTool)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a deployment tool to the list of deployment tools that will be
 processed</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="BorlandDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createBorland()" class="member-name-link">createBorland</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for Borland server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createClasspath()" class="member-name-link">createClasspath</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds to the classpath used to locate the super classes and
 interfaces of the classes that will make up the EJB JAR.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="EjbJar.DTDLocation.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.DTDLocation</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createDTD()" class="member-name-link">createDTD</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a DTD location record.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="IPlanetDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetDeploymentTool</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createIplanet()" class="member-name-link">createIplanet</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for iPlanet Application Server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="JbossDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createJboss()" class="member-name-link">createJboss</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for JBoss server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createJonas()" class="member-name-link">createJonas</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for JOnAS server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="OrionDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">OrionDeploymentTool</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createOrion()" class="member-name-link">createOrion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a orion nested element used to configure a
 deployment tool for Orion server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createSupport()" class="member-name-link">createSupport</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a fileset for support elements.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="WeblogicDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWeblogic()" class="member-name-link">createWeblogic</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for WebLogic server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWeblogictoplink()" class="member-name-link">createWeblogictoplink</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for WebLogic when using the TOPLink
 Object-Relational mapping.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="WebsphereDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createWebsphere()" class="member-name-link">createWebsphere</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a deployment tool for WebSphere 4.0 server.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#execute()" class="member-name-link">execute</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Invoked by Ant after the task is prepared, when it is ready to execute
 this task.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCmpversion()" class="member-name-link">getCmpversion</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the CMP version.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestdir()" class="member-name-link">getDestdir</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the destination directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasejarname(java.lang.String)" class="member-name-link">setBasejarname</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base name of the EJB JAR that is to be created if it is not
 to be determined from the name of the deployment descriptor files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBasenameterminator(java.lang.String)" class="member-name-link">setBasenameterminator</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The string which terminates the bean name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClasspath(org.apache.tools.ant.types.Path)" class="member-name-link">setClasspath</a><wbr>(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the classpath to use when resolving classes for inclusion in the jar.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCmpversion(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.CMPVersion)" class="member-name-link">setCmpversion</a><wbr>(<a href="EjbJar.CMPVersion.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</a>&nbsp;version)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the CMP version.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDependency(java.lang.String)" class="member-name-link">setDependency</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;analyzer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the analyzer to use when adding in dependencies to the JAR.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDescriptordir(java.io.File)" class="member-name-link">setDescriptordir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the descriptor directory.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDestdir(java.io.File)" class="member-name-link">setDestdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the destination directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFlatdestdir(boolean)" class="member-name-link">setFlatdestdir</a><wbr>(boolean&nbsp;inValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Controls whether the
 destination JARs are written out in the destination directory with
 the same hierarchical structure from which the deployment descriptors
 have been read.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setGenericjarsuffix(java.lang.String)" class="member-name-link">setGenericjarsuffix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the suffix for the generated jar file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setManifest(java.io.File)" class="member-name-link">setManifest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;manifest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the Manifest file to use when jarring.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNaming(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.NamingScheme)" class="member-name-link">setNaming</a><wbr>(<a href="EjbJar.NamingScheme.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</a>&nbsp;namingScheme)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the naming scheme used to determine the name of the generated jars
 from the deployment descriptor</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSrcdir(java.io.File)" class="member-name-link">setSrcdir</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the source directory, which is the directory that
 contains the classes that will be added to the EJB jar.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.taskdefs.MatchingTask">Methods inherited from class&nbsp;org.apache.tools.ant.taskdefs.<a href="../../MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</a></h3>
<code><a href="../../MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</a>, <a href="../../MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</a>, <a href="../../MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</a>, <a href="../../MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</a>, <a href="../../MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</a>, <a href="../../MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</a>, <a href="../../MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</a>, <a href="../../MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</a>, <a href="../../MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</a>, <a href="../../MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</a>, <a href="../../MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</a>, <a href="../../MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</a>, <a href="../../MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</a>, <a href="../../MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</a>, <a href="../../MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</a>, <a href="../../MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</a>, <a href="../../MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</a>, <a href="../../MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</a>, <a href="../../MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</a>, <a href="../../MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</a>, <a href="../../MatchingTask.html#createExclude()">createExclude</a>, <a href="../../MatchingTask.html#createExcludesFile()">createExcludesFile</a>, <a href="../../MatchingTask.html#createInclude()">createInclude</a>, <a href="../../MatchingTask.html#createIncludesFile()">createIncludesFile</a>, <a href="../../MatchingTask.html#createPatternSet()">createPatternSet</a>, <a href="../../MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</a>, <a href="../../MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</a>, <a href="../../MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</a>, <a href="../../MatchingTask.html#hasSelectors()">hasSelectors</a>, <a href="../../MatchingTask.html#selectorCount()">selectorCount</a>, <a href="../../MatchingTask.html#selectorElements()">selectorElements</a>, <a href="../../MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="../../MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</a>, <a href="../../MatchingTask.html#setExcludes(java.lang.String)">setExcludes</a>, <a href="../../MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</a>, <a href="../../MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="../../MatchingTask.html#setIncludes(java.lang.String)">setIncludes</a>, <a href="../../MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</a>, <a href="../../MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</a>, <a href="../../MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</a>, <a href="../../MatchingTask.html#XsetItems(java.lang.String)">XsetItems</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.Task">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></h3>
<code><a href="../../../Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</a>, <a href="../../../Task.html#getOwningTarget()">getOwningTarget</a>, <a href="../../../Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#getTaskName()">getTaskName</a>, <a href="../../../Task.html#getTaskType()">getTaskType</a>, <a href="../../../Task.html#getWrapper()">getWrapper</a>, <a href="../../../Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</a>, <a href="../../../Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</a>, <a href="../../../Task.html#handleFlush(java.lang.String)">handleFlush</a>, <a href="../../../Task.html#handleInput(byte%5B%5D,int,int)">handleInput</a>, <a href="../../../Task.html#handleOutput(java.lang.String)">handleOutput</a>, <a href="../../../Task.html#init()">init</a>, <a href="../../../Task.html#isInvalid()">isInvalid</a>, <a href="../../../Task.html#log(java.lang.String)">log</a>, <a href="../../../Task.html#log(java.lang.String,int)">log</a>, <a href="../../../Task.html#log(java.lang.String,java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#log(java.lang.Throwable,int)">log</a>, <a href="../../../Task.html#maybeConfigure()">maybeConfigure</a>, <a href="../../../Task.html#perform()">perform</a>, <a href="../../../Task.html#reconfigure()">reconfigure</a>, <a href="../../../Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</a>, <a href="../../../Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</a>, <a href="../../../Task.html#setTaskName(java.lang.String)">setTaskName</a>, <a href="../../../Task.html#setTaskType(java.lang.String)">setTaskType</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.ProjectComponent">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../../../ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</a></h3>
<code><a href="../../../ProjectComponent.html#clone()">clone</a>, <a href="../../../ProjectComponent.html#getDescription()">getDescription</a>, <a href="../../../ProjectComponent.html#getLocation()">getLocation</a>, <a href="../../../ProjectComponent.html#getProject()">getProject</a>, <a href="../../../ProjectComponent.html#setDescription(java.lang.String)">setDescription</a>, <a href="../../../ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>EjbJar</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">EjbJar</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addDeploymentTool(org.apache.tools.ant.taskdefs.optional.ejb.EJBDeploymentTool)">
<h3>addDeploymentTool</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addDeploymentTool</span><wbr><span class="parameters">(<a href="EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</a>&nbsp;deploymentTool)</span></div>
<div class="block">Add a deployment tool to the list of deployment tools that will be
 processed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>deploymentTool</code> - a deployment tool instance to which descriptors
        will be passed for processing.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createOrion()">
<h3>createOrion</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="OrionDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">OrionDeploymentTool</a></span>&nbsp;<span class="element-name">createOrion</span>()</div>
<div class="block">Create a orion nested element used to configure a
 deployment tool for Orion server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
<dt>Since:</dt>
<dd>Ant 1.10.2</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createWeblogic()">
<h3>createWeblogic</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="WeblogicDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</a></span>&nbsp;<span class="element-name">createWeblogic</span>()</div>
<div class="block">Adds a deployment tool for WebLogic server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createWebsphere()">
<h3>createWebsphere</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="WebsphereDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</a></span>&nbsp;<span class="element-name">createWebsphere</span>()</div>
<div class="block">Adds a deployment tool for WebSphere 4.0 server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createBorland()">
<h3>createBorland</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="BorlandDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</a></span>&nbsp;<span class="element-name">createBorland</span>()</div>
<div class="block">Adds a deployment tool for Borland server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createIplanet()">
<h3>createIplanet</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="IPlanetDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetDeploymentTool</a></span>&nbsp;<span class="element-name">createIplanet</span>()</div>
<div class="block">Adds a deployment tool for iPlanet Application Server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createJboss()">
<h3>createJboss</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="JbossDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</a></span>&nbsp;<span class="element-name">createJboss</span>()</div>
<div class="block">Adds a deployment tool for JBoss server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createJonas()">
<h3>createJonas</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</a></span>&nbsp;<span class="element-name">createJonas</span>()</div>
<div class="block">Adds a deployment tool for JOnAS server.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createWeblogictoplink()">
<h3>createWeblogictoplink</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</a></span>&nbsp;<span class="element-name">createWeblogictoplink</span>()</div>
<div class="block">Adds a deployment tool for WebLogic when using the TOPLink
 Object-Relational mapping.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the deployment tool instance to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createClasspath()">
<h3>createClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a></span>&nbsp;<span class="element-name">createClasspath</span>()</div>
<div class="block">Adds to the classpath used to locate the super classes and
 interfaces of the classes that will make up the EJB JAR.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the path to be configured.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createDTD()">
<h3>createDTD</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="EjbJar.DTDLocation.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.DTDLocation</a></span>&nbsp;<span class="element-name">createDTD</span>()</div>
<div class="block">Create a DTD location record. This stores the location of a DTD. The
 DTD is identified by its public Id. The location may either be a file
 location or a resource location.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the DTD location object to be configured by Ant</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createSupport()">
<h3>createSupport</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../../../types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</a></span>&nbsp;<span class="element-name">createSupport</span>()</div>
<div class="block">Adds a fileset for support elements.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a fileset which can be populated with support files.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setManifest(java.io.File)">
<h3>setManifest</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setManifest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;manifest)</span></div>
<div class="block">Set the Manifest file to use when jarring. As of EJB 1.1, manifest
 files are no longer used to configure the EJB. However, they still
 have a vital importance if the EJB is intended to be packaged in an
 EAR file. By adding "Class-Path" settings to a Manifest file, the EJB
 can look for classes inside the EAR file itself, allowing for easier
 deployment. This is outlined in the J2EE specification, and all J2EE
 components are meant to support it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>manifest</code> - the manifest to be used in the EJB jar</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setSrcdir(java.io.File)">
<h3>setSrcdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSrcdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</span></div>
<div class="block">Sets the source directory, which is the directory that
 contains the classes that will be added to the EJB jar. Typically
 this will include the home and remote interfaces and the bean class.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inDir</code> - the source directory.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDescriptordir(java.io.File)">
<h3>setDescriptordir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDescriptordir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</span></div>
<div class="block">Set the descriptor directory. The descriptor directory contains the
 EJB deployment descriptors. These are XML files that declare the
 properties of a bean in a particular deployment scenario. Such
 properties include, for example, the transactional nature of the bean
 and the security access control to the bean's methods.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inDir</code> - the directory containing the deployment descriptors.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDependency(java.lang.String)">
<h3>setDependency</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDependency</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;analyzer)</span></div>
<div class="block">Set the analyzer to use when adding in dependencies to the JAR.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>analyzer</code> - the name of the dependency analyzer or a class.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBasejarname(java.lang.String)">
<h3>setBasejarname</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasejarname</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</span></div>
<div class="block">Set the base name of the EJB JAR that is to be created if it is not
 to be determined from the name of the deployment descriptor files.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - the basename that will be used when writing the jar
      file containing the EJB</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setNaming(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.NamingScheme)">
<h3>setNaming</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNaming</span><wbr><span class="parameters">(<a href="EjbJar.NamingScheme.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</a>&nbsp;namingScheme)</span></div>
<div class="block">Set the naming scheme used to determine the name of the generated jars
 from the deployment descriptor</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>namingScheme</code> - the naming scheme to be used</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getDestdir()">
<h3>getDestdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getDestdir</span>()</div>
<div class="block">Gets the destination directory.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>destination directory</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setDestdir(java.io.File)">
<h3>setDestdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDestdir</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;inDir)</span></div>
<div class="block">Set the destination directory. The EJB jar files will be written into
 this directory. The jar files that exist in this directory are also
 used when determining if the contents of the jar file have changed.
 Note that this parameter is only used if no deployment tools are
 specified. Typically each deployment tool will specify its own
 destination directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inDir</code> - the destination directory in which to generate jars</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCmpversion()">
<h3>getCmpversion</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCmpversion</span>()</div>
<div class="block">Gets the CMP version.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>CMP version</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCmpversion(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.CMPVersion)">
<h3>setCmpversion</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCmpversion</span><wbr><span class="parameters">(<a href="EjbJar.CMPVersion.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</a>&nbsp;version)</span></div>
<div class="block">Sets the CMP version.
 Must be either <code>1.0</code> or <code>2.0</code>.
 Default is <code>1.0</code>.
 Initially, only the JBoss implementation does something specific for CMP 2.0.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>version</code> - CMP version.</dd>
<dt>Since:</dt>
<dd>ant 1.6</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setClasspath(org.apache.tools.ant.types.Path)">
<h3>setClasspath</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClasspath</span><wbr><span class="parameters">(<a href="../../../types/Path.html" title="class in org.apache.tools.ant.types">Path</a>&nbsp;classpath)</span></div>
<div class="block">Set the classpath to use when resolving classes for inclusion in the jar.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classpath</code> - the classpath to use.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setFlatdestdir(boolean)">
<h3>setFlatdestdir</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFlatdestdir</span><wbr><span class="parameters">(boolean&nbsp;inValue)</span></div>
<div class="block">Controls whether the
 destination JARs are written out in the destination directory with
 the same hierarchical structure from which the deployment descriptors
 have been read. If this is set to true the generated EJB jars are
 written into the root of the destination directory, otherwise they
 are written out in the same relative position as the deployment
 descriptors in the descriptor directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - the new value of the flatdestdir flag.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setGenericjarsuffix(java.lang.String)">
<h3>setGenericjarsuffix</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setGenericjarsuffix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inString)</span></div>
<div class="block">Set the suffix for the generated jar file. When generic jars are
 generated, they have a suffix which is appended to the the bean name
 to create the name of the jar file. Note that this suffix includes
 the extension fo te jar file and should therefore end with an
 appropriate extension such as .jar or .ear</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inString</code> - the string to use as the suffix.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setBasenameterminator(java.lang.String)">
<h3>setBasenameterminator</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBasenameterminator</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inValue)</span></div>
<div class="block">The string which terminates the bean name.
 The convention used by this task is
 that bean descriptors are named as the BeanName with some suffix. The
 baseNameTerminator string separates the bean name and the suffix and
 is used to determine the bean name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inValue</code> - a string which marks the end of the basename.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="execute()">
<h3>execute</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">execute</span>()
             throws <span class="exceptions"><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></span></div>
<div class="block">Invoked by Ant after the task is prepared, when it is ready to execute
 this task.

 This will configure all of the nested deployment tools to allow them to
 process the jar. If no deployment tools have been configured a generic
 tool is created to handle the jar.

 A parser is configured and then each descriptor found is passed to all
 the deployment tool elements for processing.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../Task.html#execute()">execute</a></code>&nbsp;in class&nbsp;<code><a href="../../../Task.html" title="class in org.apache.tools.ant">Task</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="../../../BuildException.html" title="class in org.apache.tools.ant">BuildException</a></code> - thrown whenever a problem is
            encountered that cannot be recovered from, to signal to ant
            that a major problem occurred within this task.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
