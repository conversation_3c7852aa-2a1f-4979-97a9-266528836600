<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title><PERSON><PERSON><PERSON><PERSON><PERSON> (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.ant.types, class: TarScanner">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.ant.types</a></div>
<h1 title="Class TarScanner" class="title">Class TarScanner</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">org.apache.tools.ant.DirectoryScanner</a>
<div class="inheritance"><a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.ArchiveScanner</a>
<div class="inheritance">org.apache.tools.ant.types.TarScanner</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</a></code>, <code><a href="ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</a></code>, <code><a href="selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TarScanner</span>
<span class="extends-implements">extends <a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></span></div>
<div class="block">Scans tar archives for resources.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.types.ArchiveScanner">Fields inherited from class&nbsp;org.apache.tools.ant.types.<a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></h3>
<code><a href="ArchiveScanner.html#srcFile">srcFile</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.apache.tools.ant.DirectoryScanner">Fields inherited from class&nbsp;org.apache.tools.ant.<a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></h3>
<code><a href="../DirectoryScanner.html#basedir">basedir</a>, <a href="../DirectoryScanner.html#DEFAULTEXCLUDES">DEFAULTEXCLUDES</a>, <a href="../DirectoryScanner.html#dirsDeselected">dirsDeselected</a>, <a href="../DirectoryScanner.html#dirsExcluded">dirsExcluded</a>, <a href="../DirectoryScanner.html#dirsIncluded">dirsIncluded</a>, <a href="../DirectoryScanner.html#dirsNotIncluded">dirsNotIncluded</a>, <a href="../DirectoryScanner.html#DOES_NOT_EXIST_POSTFIX">DOES_NOT_EXIST_POSTFIX</a>, <a href="../DirectoryScanner.html#errorOnMissingDir">errorOnMissingDir</a>, <a href="../DirectoryScanner.html#everythingIncluded">everythingIncluded</a>, <a href="../DirectoryScanner.html#excludes">excludes</a>, <a href="../DirectoryScanner.html#filesDeselected">filesDeselected</a>, <a href="../DirectoryScanner.html#filesExcluded">filesExcluded</a>, <a href="../DirectoryScanner.html#filesIncluded">filesIncluded</a>, <a href="../DirectoryScanner.html#filesNotIncluded">filesNotIncluded</a>, <a href="../DirectoryScanner.html#haveSlowResults">haveSlowResults</a>, <a href="../DirectoryScanner.html#includes">includes</a>, <a href="../DirectoryScanner.html#isCaseSensitive">isCaseSensitive</a>, <a href="../DirectoryScanner.html#MAX_LEVELS_OF_SYMLINKS">MAX_LEVELS_OF_SYMLINKS</a>, <a href="../DirectoryScanner.html#selectors">selectors</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TarScanner</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fillMapsFromArchive(org.apache.tools.ant.types.Resource,java.lang.String,java.util.Map,java.util.Map,java.util.Map,java.util.Map)" class="member-name-link">fillMapsFromArchive</a><wbr>(<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;fileEntries,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;matchFileEntries,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;dirEntries,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;matchDirEntries)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fills the file and directory maps with resources read from the
 archive.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.types.ArchiveScanner">Methods inherited from class&nbsp;org.apache.tools.ant.types.<a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></h3>
<code><a href="ArchiveScanner.html#getIncludedDirectories()">getIncludedDirectories</a>, <a href="ArchiveScanner.html#getIncludedDirsCount()">getIncludedDirsCount</a>, <a href="ArchiveScanner.html#getIncludedFiles()">getIncludedFiles</a>, <a href="ArchiveScanner.html#getIncludedFilesCount()">getIncludedFilesCount</a>, <a href="ArchiveScanner.html#getResource(java.lang.String)">getResource</a>, <a href="ArchiveScanner.html#init()">init</a>, <a href="ArchiveScanner.html#match(java.lang.String)">match</a>, <a href="ArchiveScanner.html#scan()">scan</a>, <a href="ArchiveScanner.html#setEncoding(java.lang.String)">setEncoding</a>, <a href="ArchiveScanner.html#setErrorOnMissingArchive(boolean)">setErrorOnMissingArchive</a>, <a href="ArchiveScanner.html#setSrc(java.io.File)">setSrc</a>, <a href="ArchiveScanner.html#setSrc(org.apache.tools.ant.types.Resource)">setSrc</a>, <a href="ArchiveScanner.html#trimSeparator(java.lang.String)">trimSeparator</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.apache.tools.ant.DirectoryScanner">Methods inherited from class&nbsp;org.apache.tools.ant.<a href="../DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</a></h3>
<code><a href="../DirectoryScanner.html#addDefaultExclude(java.lang.String)">addDefaultExclude</a>, <a href="../DirectoryScanner.html#addDefaultExcludes()">addDefaultExcludes</a>, <a href="../DirectoryScanner.html#addExcludes(java.lang.String%5B%5D)">addExcludes</a>, <a href="../DirectoryScanner.html#clearResults()">clearResults</a>, <a href="../DirectoryScanner.html#couldHoldIncluded(java.lang.String)">couldHoldIncluded</a>, <a href="../DirectoryScanner.html#getBasedir()">getBasedir</a>, <a href="../DirectoryScanner.html#getDefaultExcludes()">getDefaultExcludes</a>, <a href="../DirectoryScanner.html#getDeselectedDirectories()">getDeselectedDirectories</a>, <a href="../DirectoryScanner.html#getDeselectedFiles()">getDeselectedFiles</a>, <a href="../DirectoryScanner.html#getExcludedDirectories()">getExcludedDirectories</a>, <a href="../DirectoryScanner.html#getExcludedFiles()">getExcludedFiles</a>, <a href="../DirectoryScanner.html#getNotFollowedSymlinks()">getNotFollowedSymlinks</a>, <a href="../DirectoryScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</a>, <a href="../DirectoryScanner.html#getNotIncludedFiles()">getNotIncludedFiles</a>, <a href="../DirectoryScanner.html#isCaseSensitive()">isCaseSensitive</a>, <a href="../DirectoryScanner.html#isEverythingIncluded()">isEverythingIncluded</a>, <a href="../DirectoryScanner.html#isExcluded(java.lang.String)">isExcluded</a>, <a href="../DirectoryScanner.html#isFollowSymlinks()">isFollowSymlinks</a>, <a href="../DirectoryScanner.html#isIncluded(java.lang.String)">isIncluded</a>, <a href="../DirectoryScanner.html#isSelected(java.lang.String,java.io.File)">isSelected</a>, <a href="../DirectoryScanner.html#match(java.lang.String,java.lang.String)">match</a>, <a href="../DirectoryScanner.html#match(java.lang.String,java.lang.String,boolean)">match</a>, <a href="../DirectoryScanner.html#matchPath(java.lang.String,java.lang.String)">matchPath</a>, <a href="../DirectoryScanner.html#matchPath(java.lang.String,java.lang.String,boolean)">matchPath</a>, <a href="../DirectoryScanner.html#matchPatternStart(java.lang.String,java.lang.String)">matchPatternStart</a>, <a href="../DirectoryScanner.html#matchPatternStart(java.lang.String,java.lang.String,boolean)">matchPatternStart</a>, <a href="../DirectoryScanner.html#removeDefaultExclude(java.lang.String)">removeDefaultExclude</a>, <a href="../DirectoryScanner.html#resetDefaultExcludes()">resetDefaultExcludes</a>, <a href="../DirectoryScanner.html#scandir(java.io.File,java.lang.String,boolean)">scandir</a>, <a href="../DirectoryScanner.html#setBasedir(java.io.File)">setBasedir</a>, <a href="../DirectoryScanner.html#setBasedir(java.lang.String)">setBasedir</a>, <a href="../DirectoryScanner.html#setCaseSensitive(boolean)">setCaseSensitive</a>, <a href="../DirectoryScanner.html#setErrorOnMissingDir(boolean)">setErrorOnMissingDir</a>, <a href="../DirectoryScanner.html#setExcludes(java.lang.String%5B%5D)">setExcludes</a>, <a href="../DirectoryScanner.html#setFollowSymlinks(boolean)">setFollowSymlinks</a>, <a href="../DirectoryScanner.html#setIncludes(java.lang.String%5B%5D)">setIncludes</a>, <a href="../DirectoryScanner.html#setMaxLevelsOfSymlinks(int)">setMaxLevelsOfSymlinks</a>, <a href="../DirectoryScanner.html#setSelectors(org.apache.tools.ant.types.selectors.FileSelector%5B%5D)">setSelectors</a>, <a href="../DirectoryScanner.html#slowScan()">slowScan</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TarScanner</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TarScanner</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="fillMapsFromArchive(org.apache.tools.ant.types.Resource,java.lang.String,java.util.Map,java.util.Map,java.util.Map,java.util.Map)">
<h3>fillMapsFromArchive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fillMapsFromArchive</span><wbr><span class="parameters">(<a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&nbsp;src,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;encoding,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;fileEntries,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;matchFileEntries,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;dirEntries,
 <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="Resource.html" title="class in org.apache.tools.ant.types">Resource</a>&gt;&nbsp;matchDirEntries)</span></div>
<div class="block">Fills the file and directory maps with resources read from the
 archive.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ArchiveScanner.html#fillMapsFromArchive(org.apache.tools.ant.types.Resource,java.lang.String,java.util.Map,java.util.Map,java.util.Map,java.util.Map)">fillMapsFromArchive</a></code>&nbsp;in class&nbsp;<code><a href="ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</a></code></dd>
<dt>Parameters:</dt>
<dd><code>src</code> - the archive to scan.</dd>
<dd><code>encoding</code> - encoding used to encode file names inside the archive.</dd>
<dd><code>fileEntries</code> - Map (name to resource) of non-directory
 resources found inside the archive.</dd>
<dd><code>matchFileEntries</code> - Map (name to resource) of non-directory
 resources found inside the archive that matched all include
 patterns and didn't match any exclude patterns.</dd>
<dd><code>dirEntries</code> - Map (name to resource) of directory
 resources found inside the archive.</dd>
<dd><code>matchDirEntries</code> - Map (name to resource) of directory
 resources found inside the archive that matched all include
 patterns and didn't match any exclude patterns.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
