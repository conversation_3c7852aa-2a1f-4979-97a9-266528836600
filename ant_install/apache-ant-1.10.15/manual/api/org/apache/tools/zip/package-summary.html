<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>org.apache.tools.zip (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li>Related Packages</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<div class="header">
<h1 title="Package org.apache.tools.zip" class="title">Package org.apache.tools.zip</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">org.apache.tools.zip</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button><button id="class-summary-tab5" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab5', 2)" class="table-tab">Exception Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AbstractUnicodeExtraField.html" title="class in org.apache.tools.zip">AbstractUnicodeExtraField</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A common base class for Unicode extra information extra fields.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="AsiExtraField.html" title="class in org.apache.tools.zip">AsiExtraField</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adds Unix file permission and UID/GID fields as well as symbolic
 link handling.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip"><code>ZipExtraField</code></a> that knows how to parse central
 directory data.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ExtraFieldUtils.html" title="class in org.apache.tools.zip">ExtraFieldUtils</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">ZipExtraField related methods</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ExtraFieldUtils.UnparseableExtraField.html" title="class in org.apache.tools.zip">ExtraFieldUtils.UnparseableExtraField</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">"enum" for the possible actions to take if the extra field
 cannot be parsed.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GeneralPurposeBit.html" title="class in org.apache.tools.zip">GeneralPurposeBit</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Parser/encoder for the "general purpose bit" field in ZIP's local
 file and central directory headers.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="JarMarker.html" title="class in org.apache.tools.zip">JarMarker</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">If this extra field is added as the very first extra field of the
 archive, Solaris will consider it an executable jar file.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UnicodeCommentExtraField.html" title="class in org.apache.tools.zip">UnicodeCommentExtraField</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Info-ZIP Unicode Comment Extra Field (0x6375):</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="UnicodePathExtraField.html" title="class in org.apache.tools.zip">UnicodePathExtraField</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Info-ZIP Unicode Path Extra Field (0x7075):</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Constants from stat.h on Unix systems.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="UnparseableExtraFieldData.html" title="class in org.apache.tools.zip">UnparseableExtraFieldData</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Wrapper for extra field data that doesn't conform to the recommended format of header-tag + size + data.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UnrecognizedExtraField.html" title="class in org.apache.tools.zip">UnrecognizedExtraField</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Simple placeholder for all those extra fields we don't want to deal
 with.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="UnsupportedZipFeatureException.html" title="class in org.apache.tools.zip">UnsupportedZipFeatureException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Exception thrown when attempting to read or write data for a zip
 entry that uses ZIP features not supported by this library.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="UnsupportedZipFeatureException.Feature.html" title="class in org.apache.tools.zip">UnsupportedZipFeatureException.Feature</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">ZIP Features that may or may not be supported.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Zip64ExtendedInformationExtraField.html" title="class in org.apache.tools.zip">Zip64ExtendedInformationExtraField</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Holds size and other extended information for entries that use Zip64
 features.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="Zip64Mode.html" title="enum class in org.apache.tools.zip">Zip64Mode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">The different modes <a href="ZipOutputStream.html" title="class in org.apache.tools.zip"><code>ZipOutputStream</code></a> can operate in.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="Zip64RequiredException.html" title="class in org.apache.tools.zip">Zip64RequiredException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Exception thrown when attempting to write data that requires Zip64
 support to an archive and <a href="ZipOutputStream.html#setUseZip64(org.apache.tools.zip.Zip64Mode)"><code>UseZip64</code></a> has been set to <a href="Zip64Mode.html#Never"><code>Never</code></a>.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ZipEightByteInteger.html" title="class in org.apache.tools.zip">ZipEightByteInteger</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Utility class that represents an eight byte integer with conversion
 rules for the big endian byte order of ZIP files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="ZipEncoding.html" title="interface in org.apache.tools.zip">ZipEncoding</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">An interface for encoders that do a pretty encoding of ZIP
 filenames.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ZipEncodingHelper.html" title="class in org.apache.tools.zip">ZipEncodingHelper</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Static helper functions for robustly encoding filenames in zip files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Extension that adds better handling of extra fields and provides
 access to the internal and external file attributes.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">General format of extra field data.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ZipFile.html" title="class in org.apache.tools.zip">ZipFile</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Replacement for <code>java.util.ZipFile</code>.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ZipLong.html" title="class in org.apache.tools.zip">ZipLong</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Utility class that represents a four byte integer with conversion
 rules for the big endian byte order of ZIP files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Reimplementation of <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/util/zip/ZipOutputStream.html" title="class or interface in java.util.zip" class="external-link"><code>java.util.zip.ZipOutputStream</code></a> that does handle the extended
 functionality of this package, especially internal/external file
 attributes and extra fields with different layouts for local file
 data and central directory entries.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ZipOutputStream.UnicodeExtraFieldPolicy.html" title="class in org.apache.tools.zip">ZipOutputStream.UnicodeExtraFieldPolicy</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">enum that represents the possible policies for creating Unicode
 extra fields.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Utility class that represents a two byte integer with conversion
 rules for the big endian byte order of ZIP files.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ZipUtil.html" title="class in org.apache.tools.zip">ZipUtil</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Utility class for handling DOS and Java time conversions.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</body>
</html>
