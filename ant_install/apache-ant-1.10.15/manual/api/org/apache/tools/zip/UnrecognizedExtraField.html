<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (22) on Sun Aug 25 20:18:10 IST 2024 -->
<title>UnrecognizedExtraField (Apache Ant API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2024-08-25">
<meta name="description" content="declaration: package: org.apache.tools.zip, class: UnrecognizedExtraField">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.apache.tools.zip</a></div>
<h1 title="Class UnrecognizedExtraField" class="title">Class UnrecognizedExtraField</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.apache.tools.zip.UnrecognizedExtraField</div>
</div>
<section class="class-description" id="class-description">
<div class="horizontal-scroll">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></code>, <code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">UnrecognizedExtraField</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></span></div>
<div class="block">Simple placeholder for all those extra fields we don't want to deal
 with.

 <p>Assumes local file data and central directory entries are
 identical - unless told the opposite.</p></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">UnrecognizedExtraField</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryData()" class="member-name-link">getCentralDirectoryData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the central data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCentralDirectoryLength()" class="member-name-link">getCentralDirectoryLength</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the central data length.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeaderId()" class="member-name-link">getHeaderId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the header id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataData()" class="member-name-link">getLocalFileDataData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the local data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLocalFileDataLength()" class="member-name-link">getLocalFileDataLength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the length of the local data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseFromCentralDirectoryData(byte%5B%5D,int,int)" class="member-name-link">parseFromCentralDirectoryData</a><wbr>(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Populate data from this array as if it was in central directory data.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#parseFromLocalFileData(byte%5B%5D,int,int)" class="member-name-link">parseFromLocalFileData</a><wbr>(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Populate data from this array as if it was in local file data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCentralDirectoryData(byte%5B%5D)" class="member-name-link">setCentralDirectoryData</a><wbr>(byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the extra field data in central directory.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHeaderId(org.apache.tools.zip.ZipShort)" class="member-name-link">setHeaderId</a><wbr>(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;headerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the header id.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLocalFileDataData(byte%5B%5D)" class="member-name-link">setLocalFileDataData</a><wbr>(byte[]&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the extra field data in the local file data -
 without Header-ID or length specifier.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/22/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>UnrecognizedExtraField</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">UnrecognizedExtraField</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setHeaderId(org.apache.tools.zip.ZipShort)">
<h3>setHeaderId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHeaderId</span><wbr><span class="parameters">(<a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a>&nbsp;headerId)</span></div>
<div class="block">Set the header id.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerId</code> - the header id to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getHeaderId()">
<h3>getHeaderId</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getHeaderId</span>()</div>
<div class="block">Get the header id.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getHeaderId()">getHeaderId</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the header id</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setLocalFileDataData(byte[])">
<h3>setLocalFileDataData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLocalFileDataData</span><wbr><span class="parameters">(byte[]&nbsp;data)</span></div>
<div class="block">Set the extra field data in the local file data -
 without Header-ID or length specifier.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - the field data to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataLength()">
<h3>getLocalFileDataLength</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getLocalFileDataLength</span>()</div>
<div class="block">Get the length of the local data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getLocalFileDataLength()">getLocalFileDataLength</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the length of the local data</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getLocalFileDataData()">
<h3>getLocalFileDataData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getLocalFileDataData</span>()</div>
<div class="block">Get the local data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getLocalFileDataData()">getLocalFileDataData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the local data</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="setCentralDirectoryData(byte[])">
<h3>setCentralDirectoryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCentralDirectoryData</span><wbr><span class="parameters">(byte[]&nbsp;data)</span></div>
<div class="block">Set the extra field data in central directory.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - the data to use</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryLength()">
<h3>getCentralDirectoryLength</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="ZipShort.html" title="class in org.apache.tools.zip">ZipShort</a></span>&nbsp;<span class="element-name">getCentralDirectoryLength</span>()</div>
<div class="block">Get the central data length.
 If there is no central data, get the local file data length.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getCentralDirectoryLength()">getCentralDirectoryLength</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the central data length</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getCentralDirectoryData()">
<h3>getCentralDirectoryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">getCentralDirectoryData</span>()</div>
<div class="block">Get the central data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#getCentralDirectoryData()">getCentralDirectoryData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Returns:</dt>
<dd>the central data if present, else return the local file data</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseFromLocalFileData(byte[],int,int)">
<h3>parseFromLocalFileData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseFromLocalFileData</span><wbr><span class="parameters">(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="ZipExtraField.html#parseFromLocalFileData(byte%5B%5D,int,int)">ZipExtraField</a></code></span></div>
<div class="block">Populate data from this array as if it was in local file data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="ZipExtraField.html#parseFromLocalFileData(byte%5B%5D,int,int)">parseFromLocalFileData</a></code>&nbsp;in interface&nbsp;<code><a href="ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</a></code></dd>
<dt>Parameters:</dt>
<dd><code>data</code> - the array of bytes.</dd>
<dd><code>offset</code> - the source location in the data array.</dd>
<dd><code>length</code> - the number of bytes to use in the data array.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="ZipExtraField.html#parseFromLocalFileData(byte%5B%5D,int,int)"><code>ZipExtraField.parseFromLocalFileData(byte[], int, int)</code></a></li>
</ul>
</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="parseFromCentralDirectoryData(byte[],int,int)">
<h3>parseFromCentralDirectoryData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">parseFromCentralDirectoryData</span><wbr><span class="parameters">(byte[]&nbsp;data,
 int&nbsp;offset,
 int&nbsp;length)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="CentralDirectoryParsingZipExtraField.html#parseFromCentralDirectoryData(byte%5B%5D,int,int)">CentralDirectoryParsingZipExtraField</a></code></span></div>
<div class="block">Populate data from this array as if it was in central directory data.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="CentralDirectoryParsingZipExtraField.html#parseFromCentralDirectoryData(byte%5B%5D,int,int)">parseFromCentralDirectoryData</a></code>&nbsp;in interface&nbsp;<code><a href="CentralDirectoryParsingZipExtraField.html" title="interface in org.apache.tools.zip">CentralDirectoryParsingZipExtraField</a></code></dd>
<dt>Parameters:</dt>
<dd><code>data</code> - the array of bytes.</dd>
<dd><code>offset</code> - the source location in the data array.</dd>
<dd><code>length</code> - the number of bytes to use in the data array.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</body>
</html>
