<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Properties controlling javac</title>
</head>

<body>

<p>The <var>source</var> and <var>target</var> attributes
of <code>&lt;javac&gt;</code> don't have any default values for
historical reasons.  Since the underlying <kbd>javac</kbd>
compiler defaults depends on the JDK you use, you may encounter
build files that don't explicitly set those attributes and that will
no longer compile using a newer JDK.  If you cannot change the build
file, Apache Ant provides two properties that help you setting
default values for these attributes.  If the attributes have been
set explicitly, the properties listed here will be ignored.</p>

<h2 id="source">ant.build.javac.source</h2>

<p><em>Since Ant 1.7</em></p>

<p>Provides a default value for <code>&lt;javac&gt;</code>'s
and <code>&lt;javadoc&gt;</code>'s <var>source</var> attribute.</p>

<h2 id="target">ant.build.javac.target</h2>

<p><em>Since Ant 1.7</em></p>

<p>Provides a default value
for <code>&lt;javac&gt;</code>'s <var>target</var> attribute.</p>

</body>
</html>
