<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>jarlib-manifest Task</title>
</head>

<body>

<h2 id="jarlib-manifest">jarlib-manifest</h2>
<h3>Description</h3>
<p>Task to generate a manifest that declares all the dependencies in manifest. The dependencies are
determined by looking in the specified path and searching for Extension / "Optional Package"
specifications in the manifests of the jars.</p>

<p>Note that this task works with extensions as defined by the "Optional Package" specification.
For more information about optional packages, see the document <em>Optional Package Versioning</em>
in the documentation bundle for your Java Standard Edition package, in
file <code>guide/extensions/versioning.html</code> or the
online <a href="https://docs.oracle.com/javase/8/docs/technotes/guides/extensions/versioning.html"
target="_top">Extension and ExtensionSet documentation</a> for further details.</p>

<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>destfile</td>
    <td>The file to generate Manifest into</td>
    <td>Yes</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>extension</h4>
<p><a href="../Types/extension.html">Extension</a> the extension that this library implements.</p>

<h4>depends</h4>
<p><a href="../Types/extensionset.html">ExtensionSet</a>s containing all dependencies for jar.</p>

<h4>options</h4>
<p><a href="../Types/extensionset.html">ExtensionSet</a>s containing all optional dependencies for
jar. (Optional dependencies will be used if present else they will be ignored)</p>

<h3>Examples</h3>
<p>Basic Manifest generated for single Extension</p>
<pre>
&lt;extension id=&quot;e1&quot;
           extensionName=&quot;MyExtensions&quot;
           specificationVersion=&quot;1.0&quot;
           specificationVendor=&quot;Peter Donald&quot;
           implementationVendorID=&quot;vv&quot;
           implementationVendor=&quot;Apache&quot;
           implementationVersion=&quot;2.0&quot;
           implementationURL=&quot;https://somewhere.com&quot;/&gt;

&lt;jarlib-manifest destfile=&quot;myManifest.txt&quot;&gt;
    &lt;extension refid=&quot;e1&quot;/&gt;
&lt;/jarlib-manifest&gt;</pre>

<p>Search for extension in fileset: a large example with required and optional dependencies</p>
<pre>
&lt;extension id=&quot;e1&quot;
           extensionName=&quot;MyExtensions&quot;
           specificationVersion=&quot;1.0&quot;
           specificationVendor=&quot;Peter Donald&quot;
           implementationVendorID=&quot;vv&quot;
           implementationVendor=&quot;Apache&quot;
           implementationVersion=&quot;2.0&quot;
           implementationURL=&quot;https://somewhere.com&quot;/&gt;

&lt;extensionSet id=&quot;option.ext&quot;&gt;
    &lt;libfileset dir=&quot;lib/option&quot;&gt;
        &lt;include name=&quot;**/*.jar&quot;/&gt;
    &lt;/libfileset&gt;
&lt;/extensionSet&gt;

&lt;extensionSet id=&quot;depends.ext&quot;&gt;
    &lt;libfileset dir=&quot;lib/required&quot;&gt;
        &lt;include name=&quot;*.jar&quot;/&gt;
    &lt;/libfileset&gt;
&lt;/extensionSet&gt;

&lt;jarlib-manifest destfile=&quot;myManifest.txt&quot;&gt;
    &lt;extension refid=&quot;e1&quot;/&gt;
    &lt;depends refid=&quot;depends.ext&quot;/&gt;
    &lt;options refid=&quot;option.ext&quot;/&gt;
&lt;/jarlib-manifest&gt;</pre>

</body>
</html>
