<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Available Task</title>
</head>

<body>

<h2 id="available">Available</h2>
<h3>Description</h3>
<p>Sets a property if a resource is available at run time. This resource can be a file, a directory,
a class in the classpath, or a JVM system resource.</p>
<p><strong>Note</strong>: a class is available in the classpath when it can be loaded; i.e., all
classes it depends on must be in the classpath, too.</p>
<p>If the resource is present, the property value is set to <q>true</q> by default; otherwise, the
property is not set. You can set the value to something other than the default by specifying
the <var>value</var> attribute.</p>
<p>Normally, this task is used to set properties that are useful to avoid target execution depending
on system parameters.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>property</td>
    <td>The name of the property to set.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>value</td>
    <td>The value to set the property to.</td>
    <td>No; defaults to <q>true</q></td>
  </tr>
  <tr>
    <td>classname</td>
    <td>The class to look for in the classpath.</td>
    <td rowspan="3">Exactly one of the three</td>
  </tr>
  <tr>
    <td>file</td>
    <td class="left">The file to look for.</td>
  </tr>
  <tr>
    <td>resource</td>
    <td class="left">The resource to look for in the JVM.</td>
  </tr>
  <tr>
    <td>classpath</td>
    <td>The classpath to use when looking up <var>classname</var> or <var>resource</var>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>filepath</td>
    <td>The path to use when looking up <var>file</var>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>The classpath to use, given as a <a href="../using.html#references">reference</a> to a path
    defined elsewhere.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>type</td>
    <td>The type of <var>file</var> to look for, either a directory (<var>type</var>=<q>dir</q>) or
    a file (<var>type</var>=<q>file</q>). If not set, the property will be set if the name specified
    in the <var>file</var> attribute exists as either a file or a directory.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>ignoresystemclasses</td>
    <td>Ignore Ant's runtime classes, using only the specified classpath.  Only affects
    the <var>classname</var> attribute.</td>
    <td>No; defaults to <q>false</q></td>
  </tr>
  <tr>
    <td>searchparents</td>
    <td>This contains the behaviour of the <q>file</q> <var>type</var>.  If <q>true</q>, the task
    will, when searching for a file, search not only the directories specified but will also search
    the parent directories of those specified.  If <q>false</q>, only the directories specified will
    be searched.  <em>Since Ant 1.7</em></td>
    <td>No; defaults to <q>false</q></td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>classpath</h4>
<p><code>Available</code>'s <var>classpath</var> attribute is
a <a href="../using.html#path">path-like structure</a> and can also be set via a nested
<code>&lt;classpath&gt;</code> element.</p>
<h4>filepath</h4>
<p><code>Available</code>'s <var>filepath</var> attribute is
a <a href="../using.html#path">path-like structure</a> and can also be set via a
nested <code>&lt;filepath&gt;</code> element.</p>

<h3>Examples</h3>
<p>Set the <code>Myclass.present</code> property to the value <q>true</q> if the
class <code>org.whatever.Myclass</code> is found in Ant's classpath.</p>
<pre>&lt;available classname=&quot;org.whatever.Myclass&quot; property=&quot;Myclass.present&quot;/&gt;</pre>

<p>Set the <code>jaxp.jar.present</code> property to the value <q>true</q> if the
file <samp>./lib/jaxp11/jaxp.jar</samp> is found.</p>
<pre>
&lt;property name=&quot;jaxp.jar&quot; value=&quot;./lib/jaxp11/jaxp.jar&quot;/&gt;
&lt;available file=&quot;${jaxp.jar}&quot; property=&quot;jaxp.jar.present&quot;/&gt;</pre>

<p>Set the <code>local.lib.present</code> property to the value <q>true</q> if the
directory <samp>/usr/local/lib</samp> is found.</p>
<pre>
&lt;available file=&quot;/usr/local/lib&quot; type=&quot;dir&quot;
           property=&quot;local.lib.present&quot;/&gt;</pre>

<p>Set the <code>jaxp11.present</code> property to the value <q>true</q> if the
class <code>javax.xml.transform.Transformer</code> is found in the classpath referenced
by <samp>jaxp</samp> (in this case, <samp>./lib/jaxp11/jaxp.jar</samp>).</p>
<pre>
...in project ...
&lt;property name=&quot;jaxp.jar&quot; value=&quot;./lib/jaxp11/jaxp.jar&quot;/&gt;
&lt;path id=&quot;jaxp&quot; location=&quot;${jaxp.jar}&quot;/&gt;
...in target ...
&lt;available classname=&quot;javax.xml.transform.Transformer&quot;
           classpathref=&quot;jaxp&quot; property=&quot;jaxp11.present&quot;/&gt;</pre>

<p>Set the <code>have.extras</code> property to the value <q>true</q> if the resource
file <samp>extratasks.properties</samp> is found.</p>
<pre>
&lt;available property=&quot;have.extras&quot; resource=&quot;extratasks.properties&quot;&gt;
  &lt;classpath&gt;
    &lt;pathelement location=&quot;/usr/local/ant/extra.jar&quot;/&gt;
  &lt;/classpath&gt;
&lt;/available&gt;</pre>

</body>
</html>
