<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">
<head>
   <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
   <title>JDepend Task</title>
</head>

<body>

<h2 id="jdepend">JDepend</h2>

<h3>Description</h3>

<p>Invokes the <a href="https://github.com/clarkware/jdepend" target="_top">JDepend</a>
parser.</p>

<p>This parser &quot;traverses a set of Java source file directories and generates design quality
metrics for each Java package&quot;.  It allows to &quot;automatically measure the quality of a
design in terms of its extensibility, reusability, and maintainability to effectively manage and
control package dependencies.&quot;</p>

<p>Source file directories are defined by nested <code>&lt;sourcespath&gt;</code>. Class file
directories are defined by nested <code>&lt;classespath&gt;</code>. See <a href="#nested">nested
elements</a>.</p>

<p>Optionally, you can also set the <var>outputfile</var> name where the output is stored. By
default the task writes its report to the standard output.</p>

<p>The task requires at least the JDepend 1.2 version.</p>

<h3>Parameters</h3>

<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Description</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>outputfile</td>
    <td>The output file name. If not set, the output is printed on the standard output.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>format</td>
    <td>The format to write the output in,
    either <q>text</q> (default) or <q>xml</q></td>
    <td>No; the default is <q>text</q></td>
  </tr>
  <tr>
    <td>fork</td>
    <td>Run the tests in a separate JVM.</td>
    <td>No; default is <q>off</q></td>
  </tr>
  <tr>
    <td>haltonerror</td>
      <td>Stop the build process if an error occurs during the <code>jdepend</code> analysis.</td>
    <td>No; default is <q>off</q></td>
  </tr>
  <tr>
    <td>timeout</td>
    <td>Cancel the operation if it doesn't finish in the given time (measured in milliseconds).</td>
    <td>No, ignored if <var>fork</var> is <q>false</q></td>
  </tr>
  <tr>
    <td>jvm</td>
    <td>The command used to invoke JVM. The command is resolved
      by <code>java.lang.Runtime.exec()</code>.</td>
    <td>No; default <kbd>java</kbd>, ignored if <var>fork</var> is <q>false</q></td>
  </tr>
  <tr>
    <td>dir</td>
    <td>The directory to invoke JVM in.</td>
    <td>No, ignored if <var>fork</var> is <q>false</q></td>
  </tr>
  <tr>
    <td>includeruntime</td>
    <td>Implicitly add the classes required to run <code>jdepend</code> in forked mode. <em>Since
      Apache Ant 1.6</em>.</td>
    <td>No; default is <q>no</q>, ignored if <var>fork</var> is <q>false</q></td>
  </tr>
  <tr>
    <td>classpathref</td>
    <td>the <var>classpath</var> to use, given as reference to a path defined elsewhere.</td>
    <td>No</td>
  </tr>
</table>

<h3 id="nested">Parameters specified as nested elements</h3>

<p><code>jdepend</code> supports four nested
elements: <code>&lt;classpath&gt;</code>, <code>&lt;classespath&gt;</code>
and <code>&lt;sourcespath&gt;</code>, that represent <a href="../using.html#path">path-like
structures</a>, and <code>&lt;exclude&gt;</code>.</p>

<p><code>&lt;sourcespath&gt;</code> is used to define the paths of the source code to analyze, but
it is deprecated. With version 2.5 of JDepend, only class files are analyzed. The nested
element <code>&lt;classespath&gt;</code> replaces <code>&lt;sourcespath&gt;</code> and is used to
define the paths of compiled class code to analyze; the <code>&lt;sourcespath&gt;</code> variable is
still available in case you are using an earlier version of JDepend.
The <code>&lt;exclude&gt;</code> element can be used to set packages to ignore (requires JDepend 2.5
or above).</p>

<h3>Examples</h3>

<p>Invoke JDepend on the <samp>build</samp> directory, writing the output on the standard
output. The classpath is defined using a reference.</p>
<pre>
&lt;jdepend classpathref="base.path"&gt;
    &lt;classespath&gt;
        &lt;pathelement location="build"/&gt;
    &lt;/classespath&gt;
&lt;/jdepend&gt;</pre>

<p>This invoke JDepend in a separate JVM on the <samp>src</samp> and <samp>testsrc</samp>
directories, writing the output to the <samp>docs/jdepend.xml</samp> file in XML format. The
classpath is defined using nested elements.</p>
<pre>
&lt;jdepend outputfile="docs/jdepend.xml" fork="yes" format="xml"&gt;
    &lt;sourcespath&gt;
        &lt;pathelement location="src"/&gt;
    &lt;/sourcespath&gt;
    &lt;classpath&gt;
        &lt;pathelement location="classes"/&gt;
        &lt;pathelement location="lib/jdepend.jar"/&gt;
    &lt;/classpath&gt;
&lt;/jdepend&gt;</pre>

<p>Invoke JDepend with the build directory as the base for class files to analyze, and ignore all
classes in the <samp>java.*</samp> and <samp>javax.*</samp> packages.</p>
<pre>
&lt;jdepend classpathref="base.path"&gt;
    &lt;exclude name="java.*&gt;
    &lt;exclude name="javax.*&gt;
    &lt;classespath&gt;
        &lt;pathelement location="build"/&gt;
    &lt;/classespath&gt;
&lt;/jdepend&gt;</pre>

</body>
</html>
