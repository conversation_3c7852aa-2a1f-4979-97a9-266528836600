<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
  <title>ClearCase Tasks</title>
</head>

<body>
<h1>Apache Ant ClearCase Tasks</h1>
<p>by:<br/>
<PERSON> (cwhite at aracnet dot com),<br/>
<PERSON> (spkane at genomatica dot com),<br/>
<PERSON> (<PERSON><PERSON> at vectorscm dot com), and<br/>
<PERSON>    (sean at cm-logic dot com)</p>

<p>Version 1.6&mdash;02/25/2003</p>

<h1>ClearCase Support</h1>
<h2>Table of Contents</h2>
<ul>
  <li><a href="#introduction">Introduction</a>
  <li><a href="#cccheckin">CCCheckin</a>
  <li><a href="#cccheckout">CCCheckout</a>
  <li><a href="#ccuncheckout">CCUnCheckout</a>
  <li><a href="#ccupdate">CCUpdate</a>
  <li><a href="#ccmklbtype">CCMklbtype</a>
  <li><a href="#ccmklabel">CCMklabel</a>
  <li><a href="#ccrmtype">CCRmtype</a>
  <li><a href="#cclock">CCLock</a>
  <li><a href="#ccunlock">CCUnlock</a>
  <li><a href="#ccmkbl">CCMkbl</a>
  <li><a href="#ccmkattr">CCMkattr</a>
  <li><a href="#ccmkdir">CCMkdir</a>
  <li><a href="#ccmkelem">CCMkelem</a></li>

</ul>

<hr/>
<h2 id="introduction">Introduction</h2>
<p>Apache Ant provides several optional tasks for working with ClearCase. These tasks correspond to
various ClearCase commands using the <kbd>cleartool</kbd> program. The current tasks available for
Ant correspond to only a few of the significant ClearCase commands.</p>

<p>More tasks can be easily added by deriving from the ClearCase class and then adding functionality
that is specific to that ClearCase command.</p>
<p>Important: these tasks all require <kbd>cleartool</kbd> on the command line.  If a task fails
with an <code>IOException</code>, especially <code>error=2</code> on Windows, this is your
problem.</p>

<hr/>

<h2 id="cccheckin">CCCheckin</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool checkin</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view file or directory that the command will operate on</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>nowarn</td>
    <td>Suppress warning messages</td>
    <td>No</td>
  </tr>
  <tr>
    <td>preservetime</td>
    <td>Preserve the modification time</td>
    <td>No</td>
  </tr>
  <tr>
    <td>keepcopy</td>
    <td>Keeps a copy of the file with a <samp>.keep</samp> extension</td>
    <td>No</td>
  </tr>
  <tr>
    <td>identical</td>
    <td>Allows the file to be checked in even if it is identical to the original</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails</td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>checkin</code> on the file <samp>c:/views/viewdir/afile</samp>. Comment
text from the file <samp>acomment.txt</samp> is added to ClearCase as a comment. All warning
messages are suppressed. The file is checked in even if it is <em>identical</em> to the
original.</p>
<pre>
&lt;cccheckin viewpath="c:/views/viewdir/afile"
           commentfile="acomment.txt"
           nowarn="true"
           identical="true"/&gt;</pre>

<hr/>

<h2 id="cccheckout">CCCheckout</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool checkout</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view file or directory that the command will operate on</td>
    <td>No</td>
  </tr>
  <tr>
    <td>reserved</td>
    <td>Specifies whether to check out the file as reserved or not</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>out</td>
    <td>Creates a writable file under a different filename</td>
    <td>No</td>
  </tr>
  <tr>
    <td>nodata</td>
    <td>Checks out the file but does not create an editable file containing its data</td>
    <td>No</td>
  </tr>
  <tr>
    <td>branch</td>
    <td>Specify a branch to check out the file to</td>
    <td>No</td>
  </tr>
  <tr>
    <td>version</td>
    <td>Allows checkout of a version other than <code>/main/LATEST</code> (or whatever is selected
    by a config spec)</td>
    <td>No</td>
  </tr>
  <tr>
    <td>nowarn</td>
    <td>Suppress warning messages</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>notco</td>
    <td>Fail if it's already checked out to the current view. Set to <q>false</q> to ignore
    it.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>checkout</code> on the file <samp>c:/views/viewdir/afile</samp>. It is
checked out as <em>reserved</em> on branch called <samp>abranch</samp>. All warning messages are
suppressed. A &quot;<samp>Some comment text</samp>&quot; is added to ClearCase as a comment.</p>
<pre>
&lt;cccheckout viewpath="c:/views/viewdir/afile"
            reserved="true"
            branch="abranch"
            nowarn="true"
            comment="Some comment text"/&gt;</pre>

<hr/>

<h2 id="ccuncheckout">CCUnCheckout</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool uncheckout</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view file or directory that the command will operate on</td>
    <td>No</td>
  </tr>
  <tr>
    <td>keepcopy</td>
    <td>Specifies whether to keep a copy of the file with a <samp>.keep</samp> extension or not</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>uncheckout</code> on the file <samp>c:/views/viewdir/afile</samp>. A
copy of the file named <samp>c:/views/viewdir/afile.keep</samp> is kept.</p>
<pre>&lt;ccuncheckout viewpath="c:/views/viewdir/afile" keepcopy="true"/&gt;</pre>

<hr/>

<h2 id="ccupdate">CCUpdate</h2>
<h3>Description</h3>
<p>Task to perform an <kbd>cleartool update</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase snapshot view file or directory that the command will operate on</td>
    <td>No</td>
  </tr>
  <tr>
    <td>graphical</td>
    <td>Displays a graphical dialog during the update</td>
    <td>No</td>
  </tr>
  <tr>
    <td>log</td>
    <td>Specifies a log file for ClearCase to write to</td>
    <td>No</td>
  </tr>
  <tr>
    <td>overwrite</td>
    <td>Specifies whether to overwrite hijacked files or not</td>
    <td>No</td>
  </tr>
  <tr>
    <td>rename</td>
    <td>Specifies that hijacked files should be renamed with a <samp>.keep</samp> extension</td>
    <td>No</td>
  </tr>
  <tr>
    <td>currenttime</td>
    <td>Specifies that modification time should be written as the current time. Mutually exclusive
    with <var>preservetime</var>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>preservetime</td>
    <td>Specifies that modification time should preserved from the VOB time. Mutually exclusive
    with <var>currenttime</var>.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/>  <em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>update</code> on the snapshot view
directory <samp>c:/views/viewdir</samp>. A graphical dialog will be displayed. The output will be
logged to <samp>log.log</samp> and it will overwrite any hijacked files. The modified time will be
set to the current time.</p>
<pre>
&lt;ccupdate viewpath="c:/views/viewdir"
          graphical="false"
          log="log.log"
          overwrite="true"
          currenttime="true"
          rename="false"/&gt;</pre>

<hr/>

<h2 id="ccmklbtype">CCMklbtype</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool mklbtype</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>typename</td>
    <td>Name of the label type to create</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>vob</td>
    <td>Name of the VOB</td>
    <td>No</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>Replace an existing label definition of the same type</td>
    <td>No</td>
  </tr>
  <tr>
    <td>global</td>
    <td>Creates a label type that is global to the VOB or to VOBs that use this VOB.</td>
    <td rowspan="2">No; only one of the two may be used,
    default <var>ordinary</var>=<q>true</q></td>
  </tr>
  <tr>
    <td>ordinary</td>
    <td class="left">Creates a label type that can be used only in the current VOB.</td>
  </tr>
  <tr>
    <td>pbranch</td>
    <td>Allows the label type to be used once per branch in a given element's version tree</td>
    <td>No</td>
  </tr>
  <tr>
    <td>shared</td>
    <td>Sets the way mastership is checked by ClearCase. See ClearCase documentation for
    details</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Perform a ClearCase <code>mklbtype</code> to create a label type named <samp>VERSION_1</samp>. It
is created as <em>ordinary</em> so it is available only to the current VOB. The text <q>Development
version 1</q> is added as a comment.</p>
<pre>
&lt;ccmklbtype typename="VERSION_1"
            ordinary="true"
            comment="Development version 1"/&gt;</pre>

<hr/>

<h2 id="ccmklabel">CCMklabel</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool mklabel</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>typename</td>
    <td>Name of the label type</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view file or directory that the command will operate on</td>
    <td>No</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>Replace a label of the same type on the same branch</td>
    <td>No</td>
  </tr>
  <tr>
    <td>recurse</td>
    <td>Process each subdirectory under <var>viewpath</var></td>
    <td>No</td>
  </tr>
  <tr>
    <td>version</td>
    <td>Identify a specific version to attach the label to</td>
    <td>No</td>
  </tr>
  <tr>
    <td>vob</td>
    <td>Name of the VOB</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Perform a ClearCase <code>mklabel</code> on the file <samp>c:/views/viewdir/afile</samp> under
the <samp>main</samp> branch for version 2 (<samp>\main\2</samp>). Text &quot;<samp>Some comment
text</samp>&quot; is added as a comment. It will <em>recurse</em> all subdirectories.</p>
<pre>
&lt;ccmklabel viewpath="c:/views/viewdir/afile"
           comment="Some comment text"
           recurse="true"
           version="\main\2"
           typename="VERSION_1"/&gt;
</pre>

<hr/>

<h2 id="ccrmtype">CCRmtype</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool rmtype</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>typekind</td>
    <td>The kind of type to create. Valid types are:
      <table>
        <tr>
          <th scope="col">Kind</th>
          <th scope="col">Description</th>
        </tr>
        <tr>
          <td><q>attype</q></td>
          <td>attribute type</td>
        </tr>
        <tr>
          <td><q>brtype</q></td>
          <td>branch type</td>
        </tr>
        <tr>
          <td><q>eltype</q></td>
          <td>element type</td>
        </tr>
        <tr>
          <td><q>hltype</q></td>
           <td>hyperlink type</td>
        </tr>
        <tr>
          <td><q>lbtype</q></td>
          <td>label type</td>
        </tr>
        <tr>
          <td><q>trtype</q></td>
          <td>trigger type</td>
        </tr>
      </table></td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>typename</td>
    <td>The name of the type to remove</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>ignore</td>
    <td>Used with trigger types only. Forces removal of trigger type even if a pre-operation trigger
    would prevent its removal</td>
    <td>No</td>
  </tr>
  <tr>
    <td>rmall</td>
    <td>Removes all instances of a type and the type object itself</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.  <em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>

<h3>Examples</h3>

<p>Perform a ClearCase <code>rmtype</code> to remove a label type (<q>lbtype</q>)
named <samp>VERSION_1</samp>. Comment text from the file <samp>acomment.txt</samp> is added as a
comment. All instances of the type are removed, including the type object itself.</p>
<pre>
&lt;ccrmtype typekind="lbtype"
          typename="VERSION_1"
          commentfile="acomment.txt"
          rmall="true"/&gt;</pre>

<hr/>

<h2 id="cclock">CCLock</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool lock</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>replace</td>
    <td>Specifies replacing an existing lock</td>
    <td>No</td>
  </tr>
  <tr>
    <td>nusers</td>
    <td>Specifies user(s) who can still modify the object</td>
    <td>No</td>
  </tr>
  <tr>
    <td>obsolete</td>
    <td>Specifies that the object should be marked obsolete</td>
    <td>No</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specifies how to populate comments fields</td>
    <td>No</td>
  </tr>
  <tr>
    <td>pname</td>
    <td>Specifies the object pathname to be locked.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>objselect</td>
    <td><em>Obsolete</em>. Use <var>objsel</var> instead.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>objsel</td>
    <td>Specifies the object(s) to be locked.<br/><em>Since Ant 1.6.1</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>lock</code> on the
object <samp>stream:Application_Integration@\MyProject_PVOB</samp>.</p>
<pre>&lt;cclock objsel="stream:Application_Integration@\MyProject_PVOB"/&gt;</pre>

<hr/>

<h2 id="ccunlock">CCUnlock</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool unlock</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specifies how to populate comments fields</td>
    <td>No</td>
  </tr>
  <tr>
    <td>pname</td>
    <td>Specifies the object pathname to be unlocked.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>objselect</td>
    <td><em>Obsolete</em>. Use <var>objsel</var> instead.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>objsel</td>
    <td>Specifies the object(s) to be unlocked.<br/><em>Since Ant 1.6.1</em></td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>unlock</code> on the
object <samp>stream:Application_Integration@\MyProject_PVOB</samp>.</p>
<pre>&lt;ccunlock objsel="stream:Application_Integration@\MyProject_PVOB"/&gt;</pre>

<hr/>

<h2 id="ccmkbl">CCMkbl</h2>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool mkbl</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>baselinerootname</td>
    <td>Specify the name to be associated with the baseline.</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>nowarn</td>
    <td>Suppress warning messages</td>
    <td>No</td>
  </tr>
  <tr>
    <td>identical</td>
    <td>Allows the baseline to be created even if it is identical to the previous baseline.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>full</td>
    <td>Creates a full baseline.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>nlabel</td>
    <td>Allows the baseline to be created without a label.</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.<br/><em>Since Ant 1.6.1</em></td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>mkbl</code> on the Integration view at <samp>v:\ApplicationCC</samp>
even if it is <em>identical</em> to a previous baseline. The new baseline with be incremental and
named <samp>Application_Baseline_AUTO</samp>.</p>
<pre>
&lt;ccmkbl baselinerootname="Application_Baseline_AUTO"
        identical="yes"
        full="no"
        viewpath="v:\ApplicationCC"/&gt;</pre>

<hr/>

<h2 id="ccmkattr">CCMkattr</h2>
<p><em>Since Ant 1.6.1</em></p>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool mkattr</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view file or directory that the command will operate on</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>replace</td>
    <td>Replace the value of the attribute if it already exists</td>
    <td>No</td>
  </tr>
  <tr>
    <td>recurse</td>
    <td>Process each subdirectory under <var>viewpath</var></td>
    <td>No</td>
  </tr>
  <tr>
    <td>version</td>
    <td>Identify a specific version to attach the attribute to</td>
    <td>No</td>
  </tr>
  <tr>
    <td>typename</td>
    <td>Name of the attribute type</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>typevalue</td>
    <td>Value to attach to the attribute type</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.</td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>mkattr</code> on the file <samp>c:/views/viewdir/afile</samp> and
attach the attribute <samp>BugFix</samp> with a value of <samp>34445</samp> to it.</p>
<pre>
&lt;ccmkattr viewpath=&quot;c:/views/viewdir/afile&quot;
          typename=&quot;BugFix&quot;
          typevalue=&quot;34445&quot;/&gt;
</pre>

<hr/>

<h2 id="ccmkdir">CCMkdir</h2>
<p><em>Since Ant 1.6.1</em></p>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool mkdir</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view directory that the command will operate on</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>nocheckout</td>
    <td>Do not checkout after element creation</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.</td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform a ClearCase <code>mkdir</code> on the dir <samp>c:/views/viewdir/adir</samp> and do not
automatically check it out.</p>
<pre>
&lt;ccmkdir viewpath=&quot;c:/views/viewdir/adir&quot;
         nocheckout=&quot;true&quot;
         comment=&quot;Some comment text&quot;/&gt;</pre>

<hr/>

<h2 id="ccmkelem">CCMkelem</h2>
<p><em>Since Ant 1.6.1</em></p>
<h3>Description</h3>
<p>Task to perform a <kbd>cleartool mkelem</kbd> command to ClearCase.</p>
<h3>Parameters</h3>
<table class="attr">
  <tr>
    <th scope="col">Attribute</th>
    <th scope="col">Values</th>
    <th scope="col">Required</th>
  </tr>
  <tr>
    <td>viewpath</td>
    <td>Path to the ClearCase view file or directory that the command will operate on</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>comment</td>
    <td>Specify a comment</td>
    <td rowspan="2">No; only one of the two may be used</td>
  </tr>
  <tr>
    <td>commentfile</td>
    <td class="left">Specify a file containing a comment</td>
  </tr>
  <tr>
    <td>nowarn</td>
    <td>Suppress warning messages</td>
    <td>No</td>
  </tr>
  <tr>
    <td>nocheckout</td>
    <td>Do not checkout after element creation</td>
    <td>No</td>
  </tr>
  <tr>
    <td>checkin</td>
    <td>Checkin element after creation</td>
    <td>No</td>
  </tr>
  <tr>
    <td>preservetime</td>
    <td>Preserve the modification time (for checkin)</td>
    <td>No</td>
  </tr>
  <tr>
    <td>master</td>
    <td>Assign mastership of the main branch to the current site</td>
    <td>No</td>
  </tr>
  <tr>
    <td>eltype</td>
    <td>Element type to use during element creation</td>
    <td>No</td>
  </tr>
  <tr>
    <td>failonerr</td>
    <td>Throw an exception if the command fails.</td>
    <td>No; default is <q>true</q></td>
  </tr>
</table>
<h3>Examples</h3>

<p>Perform ClearCase <code>mkelem</code> on the file <samp>c:/views/viewdir/afile</samp> with
element type <samp>text_file</samp>, check in the file after creation.</p>
<pre>
&lt;ccmkelem viewpath=&quot;c:/views/viewdir/afile&quot;
          eltype=&quot;text_file&quot;
          checkin=&quot;true&quot;
          comment=&quot;Some comment text&quot;/&gt;</pre>

</body>
</html>
