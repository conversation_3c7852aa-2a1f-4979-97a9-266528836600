<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->

<html lang="en">
  <head>
    <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
    <title>Whichresource Task</title>
  </head>

  <body>
    <h2 id="whichresource">Whichresource</h2>
    <h3>Description</h3>
    <p>Find a class or resource on the supplied classpath, or the system classpath if none is
      supplied.  The named property is set if the item can be found.  For example:</p>
    <pre>&lt;whichresource resource="/log4j.properties" property="log4j.url"/&gt;</pre>
    <h3>Parameters</h3>
    <table class="attr">
      <tr>
        <th scope="col">Attribute</th>
        <th scope="col">Description</th>
        <th scope="col">Required</th>
      </tr>
      <tr>
        <td>property</td>
        <td>The property to fill with the URL of the resource of class.</td>
        <td>Yes</td>
      </tr>
      <tr>
        <td>class</td>
        <td>The name of the class to look for.</td>
        <td rowspan="2">Exactly one of these</td>
      </tr>
      <tr>
        <td>resource</td>
        <td class="left">The name of the resource to look for.</td>
      </tr>
      <tr>
        <td>classpath</td>
        <td>The classpath to use when looking up <var>class</var> or <var>resource</var>.</td>
        <td>No</td>
      </tr>
      <tr>
        <td>classpathref</td>
        <td>The classpath to use, given as a <a href="../using.html#references">reference</a> to a
          path defined elsewhere.  <em>Since Apache Ant 1.7.1</em>.</td>
        <td>No</td>
      </tr>
    </table>
    <h3>Parameters specified as nested elements</h3>
    <h4>classpath</h4>
    <p><code>Whichresource</code>'s <var>classpath</var> attribute is
      a <a href="../using.html#path">path-like structure</a> and can also be set via a
      nested <code>&lt;classpath&gt;</code> element.</p>
    <h3>Examples</h3>
    <p>The following shows using a classpath reference.</p>
    <pre>
&lt;path id="bsf.classpath"&gt;
  &lt;fileset dir="${user.home}/lang/bsf" includes="*.jar"/&gt;
&lt;/path&gt;
&lt;whichresource property="bsf.class.location"
               class="org.apache.bsf.BSFManager"
               classpathref="bsf.classpath"/&gt;
&lt;echo&gt;${bsf.class.location}&lt;/echo&gt;</pre>
    <p>The following shows using a nested classpath.</p>
    <pre>
&lt;whichresource property="ant-contrib.antlib.location"
               resource="net/sf/antcontrib/antlib.xml"&gt;
  &lt;classpath&gt;
    &lt;path path="f:/testing/ant-contrib/target/ant-contrib.jar"/&gt;
  &lt;/classpath&gt;
&lt;/whichresource&gt;
&lt;echo&gt;${ant-contrib.antlib.location}&lt;/echo&gt;</pre>
  </body>
</html>
