<!DOCTYPE html>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en">

<head>
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual&mdash;Feedback</title>
</head>

<body>

<h1 id="feedback">Feedback and Troubleshooting</h1>
<p>If things do not work, especially simple things like <kbd>ant -version</kbd>, then something is
  wrong with your configuration. Before filing bug reports and emailing all the Apache Ant mailing
  lists</p>
<ol>
  <li>Check your environment variables. Are <code>ANT_HOME</code> and <code>JAVA_HOME</code>
    correct? If they have quotes or trailing slashes, remove them.</li>
  <li>Unset <code>CLASSPATH</code>; if that is wrong things go horribly wrong. Ant does not need
    the <code>CLASSPATH</code> variable defined to anything to work.</li>
  <li>Make sure there are no versions of <samp>crimson.jar</samp> or other XML parsers
    in <samp>JAVA_HOME/jre/lib/ext</samp></li>
  <li>Is your path correct? is Ant on it? What about <samp>JAVA_HOME/bin</samp>? have you tested
    this? If you are using Jikes, is it on the path? A CreateProcess error (especially
    with <code>error=2</code>) on Windows usually means executable not found on the path.</li>
  <li>Which version of ant are you running? Other applications distribute a copy&mdash;it may be
    being picked up by accident.</li>
  <li>If a task failing to run is from <samp>optional.jar</samp> in <samp>ANT_HOME/lib</samp>? Are
    there any libraries which it depends on missing?</li>
  <li>If a task doesn't do what you expect, run <kbd>ant -verbose</kbd> or <kbd>ant -debug</kbd>
    to see what is happening</li>
</ol>
<p>If you can't fix your problem, start with the <a href="https://ant.apache.org/mail.html"
  target="_top">Ant User Mailing List</a>. These are other ant users who will help you learn to use
  ant. If they cannot fix it then someone may suggest filing a bug report, which will escalate the
  issue. Remember of course, that support, like all open source development tasks, is voluntary. If
  you haven't invested time in helping yourself by following the steps above, it is unlikely that
  anyone will invest the time in helping you.</p>
<p>Also, if you don't understand something, the <a href="https://ant.apache.org/mail.html"
  target="_top">Ant User Mailing List</a> is the place to ask questions. Not the developer list, nor
  the individuals whose names appears in the source and documentation. If they answered all such
  emails, nobody would have any time to improve Ant.</p>
<p>To provide feedback on this software, please subscribe to
  the <a href="https://ant.apache.org/mail.html" target="_top">Ant User Mailing List</a></p>
<p>If you want to contribute to Ant or stay current with the latest development, join
  the <a href="https://ant.apache.org/mail.html" target="_top">Ant Development Mailing List</a></p>
<p>A searchable archive can be found at <a href="https://marc.info"
  target="_top">https://marc.info</a>. Other archives will be documented online
  at <a href="https://ant.apache.org/mail.html#Archives" target="_top">Mailing Lists
  Archives</a></p>

</body>
</html>
