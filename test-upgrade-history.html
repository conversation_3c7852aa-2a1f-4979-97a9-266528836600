<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试升级历史功能</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>测试升级历史功能</h1>
    
    <div id="result"></div>
    
    <script>
        // 测试升级历史API
        function testUpgradeHistory() {
            $.ajax({
                url: '/version-info/upgrade/history',
                type: 'GET',
                success: function(data) {
                    console.log('升级历史数据:', data);
                    $('#result').html('<h2>成功获取升级历史:</h2><pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('获取升级历史失败:', error);
                    $('#result').html('<h2>获取升级历史失败:</h2><p>' + error + '</p>');
                }
            });
        }
        
        // 页面加载后自动测试
        $(document).ready(function() {
            testUpgradeHistory();
        });
    </script>
</body>
</html>
