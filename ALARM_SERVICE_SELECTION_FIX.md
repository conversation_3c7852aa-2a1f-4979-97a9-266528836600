# 服务报警页面服务选择查询条件修复

## 问题描述

在服务报警页面选择服务查询条件后，点击查询按钮，查询条件中的"服务"选择又变回了"所有服务"，而服务审计页面的查询功能是正常的。

## 问题根源

通过对比服务审计和服务报警的代码发现，问题在于数据类型不匹配：

### 服务审计页面（正常工作）
- 控制器参数：`Long serviceId`
- 模板属性：`selectedServiceId`
- 下拉框比较：`th:selected="${service.id == selectedServiceId}"`
- 数据类型：Long 与 Long 比较

### 服务报警页面（有问题）
- 控制器参数：`String appname`
- 模板属性：`selectedAppName`
- 下拉框比较：`th:selected="${service.id == selectedAppName}"`
- 数据类型：Long 与 String 比较 ❌

## 问题分析

在alarms-page.html中：
```html
<option th:each="service : ${services}"
        th:value="${service.id}"
        th:text="${service.name}"
        th:selected="${service.id == selectedAppName}"></option>
```

这里 `service.id` 是 Long 类型，而 `selectedAppName` 是 String 类型，导致比较失败，所以选中状态无法正确设置。

## 修复方案

在 AlarmController 中将 String 类型的 appname 转换为 Long 类型后再传递给模板：

```java
// 确保appname参数正确传递 - 需要转换为Long类型进行比较
Long selectedAppNameLong = null;
if (appname != null && !appname.trim().isEmpty()) {
    try {
        selectedAppNameLong = Long.parseLong(appname);
    } catch (NumberFormatException e) {
        logger.warn("无效的服务ID格式: {}", appname);
    }
}

model.addAttribute("selectedAppName", selectedAppNameLong);
```

## 修复内容

### 1. **AlarmController.searchAlarms方法**
- 添加了 String 到 Long 的类型转换
- 增加了异常处理，防止无效的服务ID格式
- 确保正常查询和异常处理都使用相同的转换逻辑

### 2. **数据类型一致性**
- 前端模板：`service.id` (Long)
- 后端传递：`selectedAppName` (Long)
- 比较操作：Long == Long ✅

## 代码对比

### 修复前
```java
model.addAttribute("selectedAppName", appname); // String类型
```

### 修复后
```java
Long selectedAppNameLong = null;
if (appname != null && !appname.trim().isEmpty()) {
    try {
        selectedAppNameLong = Long.parseLong(appname);
    } catch (NumberFormatException e) {
        logger.warn("无效的服务ID格式: {}", appname);
    }
}
model.addAttribute("selectedAppName", selectedAppNameLong); // Long类型
```

## 测试场景

1. **正常查询流程**
   - 选择特定服务
   - 点击查询按钮
   - 验证服务选择保持不变

2. **边界情况测试**
   - 空服务选择（所有服务）
   - 无效的服务ID格式
   - 不存在的服务ID

3. **分页测试**
   - 查询后翻页
   - 验证服务选择在分页时保持不变

## 相关文件

- `src/main/java/com/udpproxy/controller/AlarmController.java`
- `src/main/resources/templates/alarms-page.html`

## 修复验证

修复后，服务报警页面应该能够：
1. ✅ 选择特定服务进行查询
2. ✅ 查询后保持服务选择状态
3. ✅ 分页时保持查询条件
4. ✅ 与服务审计页面行为一致

## 注意事项

- 确保服务ID的数据类型在前后端保持一致
- 添加了异常处理，防止无效的服务ID格式导致程序异常
- 保持了与服务审计页面相同的数据处理逻辑

## 类似问题预防

在其他页面开发时，注意：
1. 确保前端模板中的数据类型与后端传递的数据类型一致
2. 在进行 `th:selected` 比较时，确保两边的数据类型相同
3. 对于ID类型的参数，建议统一使用 Long 类型
4. 添加适当的类型转换和异常处理
