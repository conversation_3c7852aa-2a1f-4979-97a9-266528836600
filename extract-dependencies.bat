@echo off
echo 正在提取依赖包，请稍候...

REM 设置JAR文件路径
set JAR_FILE=E:\qd\udp\target\udp-proxy-system-0.0.1-SNAPSHOT.jar

REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
  echo 错误: JAR文件不存在: %JAR_FILE%
  exit /b 1
)

REM 创建临时目录
echo 创建临时目录...
if exist "temp_extract" rd /s /q "temp_extract"
mkdir temp_extract

REM 解压JAR文件
echo 解压JAR文件...
jar -xf "%JAR_FILE%" -C temp_extract

REM 创建lib目录
echo 创建lib目录...
if not exist "lib" mkdir lib

REM 复制依赖库文件
echo 复制依赖库文件...
xcopy "temp_extract\BOOT-INF\lib\*" "lib\" /E /Y

REM 复制Spring Boot加载器
echo 复制Spring Boot加载器...
mkdir temp_loader
jar -xf "%JAR_FILE%" org/springframework/boot/loader -C temp_loader
xcopy "temp_loader\org\springframework\boot\loader\*" "lib\spring-boot-loader\" /E /Y

REM 创建Spring Boot加载器JAR
echo 创建Spring Boot加载器JAR...
cd temp_loader
jar -cf ..\lib\spring-boot-loader-2.5.5.jar org
cd ..

REM 清理临时目录
echo 清理临时目录...
rd /s /q "temp_extract"
rd /s /q "temp_loader"

echo 依赖提取完成! 所有依赖已保存到lib目录。
echo 现在可以使用Ant构建脚本构建项目了。

pause 