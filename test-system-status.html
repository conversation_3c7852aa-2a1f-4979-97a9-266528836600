<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .health-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .health-good {
            background-color: #28a745;
        }
        .health-warning {
            background-color: #ffc107;
        }
        .health-error {
            background-color: #dc3545;
        }
        
        /* 系统状态文字颜色 */
        .system-status-normal {
            color: #28a745;
        }
        .system-status-warning {
            color: #ffc107;
        }
        .system-status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>系统状态显示测试</h1>
        
        <div class="test-section">
            <h3>系统状态模拟</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-server me-2"></i>系统运行状态</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <span id="systemStatusIndicator" class="health-indicator health-good"></span>
                                <h6 id="systemStatusText" class="mb-0 system-status-normal">系统正常运行中</h6>
                            </div>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>运行时间:</span>
                                    <span>3天12小时45分</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>启动时间:</span>
                                    <span>2024-06-15 08:30:00</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <button type="button" class="btn btn-success" onclick="setNormalStatus()">正常状态</button>
                <button type="button" class="btn btn-warning" onclick="setWarningStatus()">警告状态</button>
                <button type="button" class="btn btn-danger" onclick="setErrorStatus()">异常状态</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>状态说明</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <span class="health-indicator health-good"></span>
                        <span class="system-status-normal">正常状态 - 绿色文字</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <span class="health-indicator health-warning"></span>
                        <span class="system-status-warning">警告状态 - 黄色文字</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <span class="health-indicator health-error"></span>
                        <span class="system-status-error">异常状态 - 红色粗体文字</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function setNormalStatus() {
            updateSystemStatus('health-good', '系统正常运行中');
        }
        
        function setWarningStatus() {
            updateSystemStatus('health-warning', '系统运行异常，请注意');
        }
        
        function setErrorStatus() {
            updateSystemStatus('health-error', '系统异常: 配置文件错误');
        }
        
        function updateSystemStatus(statusClass, statusText) {
            const indicator = document.getElementById('systemStatusIndicator');
            const textElement = document.getElementById('systemStatusText');
            
            if (indicator) {
                indicator.className = 'health-indicator ' + statusClass;
            }
            
            if (textElement) {
                textElement.textContent = statusText;
                textElement.className = 'mb-0'; // 重置类名
                
                if (statusClass === 'health-good') {
                    textElement.classList.add('system-status-normal');
                } else if (statusClass === 'health-warning') {
                    textElement.classList.add('system-status-warning');
                } else if (statusClass === 'health-error') {
                    textElement.classList.add('system-status-error');
                }
            }
        }
    </script>
</body>
</html>
