<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>只读时间控件测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* 只读状态的样式 */
        input[readonly] {
            background-color: #f8f9fa;
            cursor: pointer;
            user-select: none;
        }

        input[readonly]:focus {
            background-color: #f8f9fa;
            outline: none;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* 禁用文本选择 */
        input[type="datetime-local"] {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .instruction {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>只读时间控件测试</h1>

        <div class="instruction">
            <h5><i class="bi bi-info-circle"></i> 测试说明</h5>
            <p>以下时间控件已设置为输入保护模式，您可以：</p>
            <ul>
                <li>✅ 点击控件选择日期和时间</li>
                <li>✅ 使用日历选择器正常操作</li>
                <li>✅ 使用Tab键和方向键导航</li>
                <li>❌ 无法通过键盘输入数字或字母</li>
                <li>❌ 无法粘贴内容</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>只读时间控件</h3>
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="startTime" class="form-label">开始时间</label>
                    <input type="datetime-local" class="form-control" id="startTime" name="startTime"
                           step="1" min="2000-01-01T00:00:00" max="2099-12-31T23:59:59">
                </div>
                <div class="col-md-6">
                    <label for="endTime" class="form-label">结束时间</label>
                    <input type="datetime-local" class="form-control" id="endTime" name="endTime"
                           step="1" min="2000-01-01T00:00:00" max="2099-12-31T23:59:59">
                </div>
            </div>
            <div class="mt-3">
                <button type="button" class="btn btn-primary" onclick="testTimeValues()">获取时间值</button>
                <button type="button" class="btn btn-secondary" onclick="setCurrentTime()">设置当前时间</button>
                <button type="button" class="btn btn-info" onclick="clearTimes()">清空时间</button>
            </div>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults" class="alert alert-info">
                点击"获取时间值"按钮查看结果
            </div>
        </div>

        <div class="test-section">
            <h3>功能验证</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>应该可以做的：</h5>
                    <ul class="text-success">
                        <li>点击日历图标选择时间</li>
                        <li>使用鼠标滚轮调整时间</li>
                        <li>通过控件按钮修改时间</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>应该被阻止的：</h5>
                    <ul class="text-danger">
                        <li>键盘输入数字或字母</li>
                        <li>选择文本内容</li>
                        <li>复制粘贴操作</li>
                        <li>删除或退格操作</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 移除只读属性，改用键盘事件阻止来禁止手动输入
            $("#startTime, #endTime").removeAttr('readonly');

            // 禁用键盘输入事件，但保留控件交互
            $("#startTime, #endTime").on('keydown keypress keyup', function(e) {
                // 允许Tab键和方向键用于导航
                if (e.keyCode === 9 || e.keyCode === 37 || e.keyCode === 38 || e.keyCode === 39 || e.keyCode === 40) {
                    return true;
                }
                e.preventDefault();
                return false;
            });

            // 添加额外的输入保护
            $("#startTime, #endTime").on('paste', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁用右键菜单
            $("#startTime, #endTime").on('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });
        });

        function testTimeValues() {
            const startTime = $('#startTime').val();
            const endTime = $('#endTime').val();

            let results = '<strong>当前时间值:</strong><br>';
            results += `开始时间: ${startTime || '未设置'}<br>`;
            results += `结束时间: ${endTime || '未设置'}<br>`;

            if (startTime) {
                const startDate = new Date(startTime);
                results += `开始时间解析: ${startDate.toString()}<br>`;
            }

            if (endTime) {
                const endDate = new Date(endTime);
                results += `结束时间解析: ${endDate.toString()}<br>`;
            }

            $('#testResults').html(results);
        }

        function setCurrentTime() {
            const now = new Date();
            const isoString = now.toISOString().slice(0, 19);
            $('#startTime').val(isoString);

            const endTime = new Date(now.getTime() + 3600000); // 1小时后
            const endIsoString = endTime.toISOString().slice(0, 19);
            $('#endTime').val(endIsoString);

            $('#testResults').html('<span class="text-success">已设置当前时间和1小时后的结束时间</span>');
        }

        function clearTimes() {
            $('#startTime').val('');
            $('#endTime').val('');
            $('#testResults').html('<span class="text-info">时间已清空</span>');
        }
    </script>
</body>
</html>
