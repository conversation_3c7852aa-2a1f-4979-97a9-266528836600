package com.unimas.pg.patchhistory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.unimas.ui2.common.StreamUtil;
import org.apache.commons.io.IOUtils;
import org.codehaus.jackson.type.TypeReference;

import com.unimas.pg.common.ConfUtil;
import com.unimas.pg.common.JSONUtils;

public class PatchHistoryLogic {
	public List<Map<String, Object>> loadhistoryInf(){
		List<Map<String, Object>> datalist =new ArrayList<Map<String,Object>>();
		ConfUtil util = ConfUtil.getInstance();
		String file_path = util.getPatchHistoryInfoFile();
		File f = new File(file_path);
		if(f.exists()){
			try {
				datalist = JSONUtils.getObjFromFile(f, new TypeReference<List<Map<String, Object>>>(){});
			} catch (Exception e) {
				e.printStackTrace();
			}
		
		}		
		return datalist;
	}
	
	public String getOneDecInfo(String uuid){
		StringBuffer sb = new StringBuffer();
		ConfUtil util = ConfUtil.getInstance();
		String file_path = util.getPatchReadmeDir()+uuid;
		File f = new File(file_path);
		if(f.exists()){
			InputStream in = null;
			try {
				in = new FileInputStream(f);
				List<?> readLines = IOUtils.readLines(in,"utf-8");
				int index = readLines.indexOf("[���÷�Χ]");
				for(int i=index;i<readLines.size();i++){
					sb.append(readLines.get(i)).append("<br>");
				}
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				IOUtils.closeQuietly(in);
			}
		}
		return sb.toString();
	}
	
	public static void main(String[] args) {
		PatchHistoryLogic logic =new PatchHistoryLogic();
		System.out.println(logic.loadhistoryInf());
		System.out.println(logic.getOneDecInfo("8c86e1c3-395c-4677-b190-175ce37cfbc5"));
	}
}
