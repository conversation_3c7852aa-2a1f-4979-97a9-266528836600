package com.unimas.pg.patchhistory;

import java.util.List;
import java.util.Map;

import com.unimas.pg.common.JsonmapRDM;

public class UIPatchHistoryDo {
	
	public String loadhistoryInf(Map map) throws Exception{
		JsonmapRDM json = null;
		PatchHistoryLogic logic = new PatchHistoryLogic();
		List<Map<String, Object>> data = logic.loadhistoryInf();
		if(data.size() > 0){
			json = new JsonmapRDM(true);
			json.putPojo("data", data);
		}else{
			json = new JsonmapRDM(false);
		}
		return json.getData();
	}
	
	public String getOneDecInfo(Map map) throws Exception{
		JsonmapRDM json = new JsonmapRDM(true);
		String uuid = (String)map.get("flag");
		PatchHistoryLogic logic = new PatchHistoryLogic();
		String dec = logic.getOneDecInfo(uuid);
		json.putPojo("msg", dec);
		return json.getData();
	}

}
