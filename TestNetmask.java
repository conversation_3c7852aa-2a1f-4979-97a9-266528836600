public class TestNetmask {
    
    private static String prefixLengthToNetmask(int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            return "*************";
        }

        if (prefixLength == 0) {
            return "0.0.0.0";
        }

        long mask = 0xFFFFFFFFL << (32 - prefixLength);
        
        int octet1 = (int) ((mask >>> 24) & 0xFF);
        int octet2 = (int) ((mask >>> 16) & 0xFF);
        int octet3 = (int) ((mask >>> 8) & 0xFF);
        int octet4 = (int) (mask & 0xFF);

        String result = octet1 + "." + octet2 + "." + octet3 + "." + octet4;
        System.out.println("Prefix " + prefixLength + " -> " + result);
        return result;
    }

    private static int netmaskToPrefixLength(String netmask) {
        try {
            String[] parts = netmask.split("\\.");
            if (parts.length != 4) {
                System.out.println("Invalid netmask format: " + netmask);
                return 24;
            }

            int prefixLength = 0;
            for (String part : parts) {
                int octet = Integer.parseInt(part);
                if (octet < 0 || octet > 255) {
                    System.out.println("Invalid octet: " + octet);
                    return 24;
                }
                prefixLength += Integer.bitCount(octet);
            }

            System.out.println("Netmask " + netmask + " -> " + prefixLength);
            return prefixLength;
        } catch (Exception e) {
            System.out.println("Error converting netmask: " + netmask + ", error: " + e.getMessage());
            return 24;
        }
    }

    public static void main(String[] args) {
        System.out.println("=== Testing prefix to netmask ===");
        prefixLengthToNetmask(8);   // should be *********
        prefixLengthToNetmask(16);  // should be ***********
        prefixLengthToNetmask(24);  // should be *************
        prefixLengthToNetmask(32);  // should be ***************
        
        System.out.println("\n=== Testing netmask to prefix ===");
        netmaskToPrefixLength("*********");      // should be 8
        netmaskToPrefixLength("***********");    // should be 16
        netmaskToPrefixLength("*************");  // should be 24
        netmaskToPrefixLength("***************"); // should be 32
        
        System.out.println("\n=== Testing round trip ===");
        String mask8 = prefixLengthToNetmask(8);
        int prefix8 = netmaskToPrefixLength(mask8);
        System.out.println("Round trip 8: " + prefix8);
        
        String mask16 = prefixLengthToNetmask(16);
        int prefix16 = netmaskToPrefixLength(mask16);
        System.out.println("Round trip 16: " + prefix16);
    }
}
