<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间控件测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* 时间控件样式优化 */
        input[type="datetime-local"] {
            min-width: 200px;
            font-family: monospace;
        }

        /* 修复某些浏览器的时间控件显示问题 */
        input[type="datetime-local"]::-webkit-datetime-edit {
            padding: 0;
        }

        input[type="datetime-local"]::-webkit-datetime-edit-fields-wrapper {
            background: transparent;
        }

        input[type="datetime-local"]::-webkit-calendar-picker-indicator {
            background: transparent;
            bottom: 0;
            color: transparent;
            cursor: pointer;
            height: auto;
            left: 0;
            position: absolute;
            right: 0;
            top: 0;
            width: auto;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>时间控件测试页面</h1>

        <div class="test-section">
            <h3>精确到秒的时间控件</h3>
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="startTime" class="form-label">开始时间</label>
                    <input type="datetime-local" class="form-control" id="startTime" name="startTime" step="1" min="2000-01-01T00:00:00" max="2099-12-31T23:59:59">
                </div>
                <div class="col-md-6">
                    <label for="endTime" class="form-label">结束时间</label>
                    <input type="datetime-local" class="form-control" id="endTime" name="endTime" step="1" min="2000-01-01T00:00:00" max="2099-12-31T23:59:59">
                </div>
            </div>
            <div class="mt-3">
                <button type="button" class="btn btn-primary" onclick="testTimeValues()">测试时间值</button>
                <button type="button" class="btn btn-secondary" onclick="setCurrentTime()">设置当前时间</button>
                <button type="button" class="btn btn-warning" onclick="testInvalidTime()">测试无效时间</button>
                <button type="button" class="btn btn-danger" onclick="testInvalidYear()">测试无效年份</button>
            </div>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults" class="alert alert-info">
                点击"测试时间值"按钮查看结果
            </div>
        </div>

        <div class="test-section">
            <h3>浏览器兼容性信息</h3>
            <div id="browserInfo" class="alert alert-secondary"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 显示浏览器信息
            const browserInfo = `
                <strong>浏览器信息:</strong><br>
                User Agent: ${navigator.userAgent}<br>
                支持 datetime-local: ${document.createElement('input').type = 'datetime-local' ? '是' : '否'}
            `;
            $('#browserInfo').html(browserInfo);

            // 格式化日期和时间输入，支持精确到秒
            $("#startTime, #endTime").on("change", function() {
                const val = $(this).val();
                if (val) {
                    // 确保日期时间格式符合ISO标准，精确到秒
                    try {
                        const date = new Date(val);
                        // 格式化为 YYYY-MM-DDTHH:MM:SS
                        const isoString = date.toISOString().slice(0, 19);
                        $(this).val(isoString);
                    } catch (e) {
                        console.error("Invalid date format", e);
                    }
                }
            });

            // 初始化时间控件，设置默认值格式
            $("#startTime, #endTime").each(function() {
                const val = $(this).val();
                if (val && val.length === 16) {
                    // 如果只有分钟精度，添加秒
                    $(this).val(val + ':00');
                }
            });
        });

        function testTimeValues() {
            const startTime = $('#startTime').val();
            const endTime = $('#endTime').val();

            let results = '<strong>时间值测试结果:</strong><br>';
            results += `开始时间: ${startTime || '未设置'}<br>`;
            results += `结束时间: ${endTime || '未设置'}<br>`;

            if (startTime) {
                const startDate = new Date(startTime);
                results += `开始时间解析: ${startDate.toString()}<br>`;
                results += `开始时间ISO: ${startDate.toISOString()}<br>`;
            }

            if (endTime) {
                const endDate = new Date(endTime);
                results += `结束时间解析: ${endDate.toString()}<br>`;
                results += `结束时间ISO: ${endDate.toISOString()}<br>`;
            }

            if (startTime && endTime) {
                const diff = new Date(endTime) - new Date(startTime);
                results += `时间差: ${diff / 1000} 秒<br>`;
            }

            $('#testResults').html(results);
        }

        function setCurrentTime() {
            const now = new Date();
            const isoString = now.toISOString().slice(0, 19);
            $('#startTime').val(isoString);

            const endTime = new Date(now.getTime() + 3600000); // 1小时后
            const endIsoString = endTime.toISOString().slice(0, 19);
            $('#endTime').val(endIsoString);
        }

        function testInvalidTime() {
            // 测试无效时间范围
            const now = new Date();
            const startTime = now.toISOString().slice(0, 19);
            const endTime = new Date(now.getTime() - 3600000).toISOString().slice(0, 19); // 1小时前

            $('#startTime').val(startTime);
            $('#endTime').val(endTime);

            // 触发验证
            $('#startTime').trigger('change');
        }

        function testInvalidYear() {
            // 测试无效年份（超出范围）
            $('#startTime').val('1999-12-31T23:59:59');
            $('#endTime').val('2100-01-01T00:00:00');

            // 触发验证
            $('#startTime').trigger('input');
            $('#endTime').trigger('input');
        }
    </script>
</body>
</html>
